#!/bin/bash

# Docker构建优化脚本
set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 开始优化的Docker构建...${NC}"

# 启用BuildKit
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

# 检查是否有现有镜像用于缓存
CACHE_FROM=""
if docker image inspect fintary-api:dev >/dev/null 2>&1; then
    echo -e "${YELLOW}📦 发现现有镜像，将用作缓存${NC}"
    CACHE_FROM="--cache-from fintary-api:dev"
fi

# 构建时间统计
START_TIME=$(date +%s)

echo -e "${YELLOW}🔧 构建参数:${NC}"
echo "  - BuildKit: 启用"
echo "  - 缓存: ${CACHE_FROM:-无}"
echo "  - 并行构建: 启用"

# 执行构建
docker build \
    --progress=plain \
    $CACHE_FROM \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    -f api/Dockerfile.dev.optimized \
    -t fintary-api:dev \
    .

END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo -e "${GREEN}✅ 构建完成！${NC}"
echo -e "${GREEN}⏱️  总耗时: ${DURATION}秒${NC}"

# 显示镜像大小
echo -e "${YELLOW}📊 镜像信息:${NC}"
docker images fintary-api:dev --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

# 可选：清理构建缓存
read -p "是否清理Docker构建缓存？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}🧹 清理构建缓存...${NC}"
    docker builder prune -f
    echo -e "${GREEN}✅ 缓存清理完成${NC}"
fi
