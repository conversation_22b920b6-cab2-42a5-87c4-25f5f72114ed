// #!/usr/bin/env node

// const fs = require('fs');
// const path = require('path');
// const { execSync } = require('child_process');

// /**
//  * 修复注释顺序：将 @ts-expect-error 移到 biome-ignore 之前
//  */
// class CommentOrderFixer {
//   constructor() {
//     this.processedFiles = new Set();
//   }

//   /**
//    * 获取所有需要处理的文件
//    */
//   getFilesToProcess() {
//     const files = [];

//     try {
//       // 运行 biome check 获取错误信息
//       execSync('npx biome check --reporter=json', {
//         encoding: 'utf8',
//         cwd: process.cwd(),
//         stdio: 'pipe',
//       });
//     } catch (error) {
//       const errorOutput = error.stdout || error.stderr || '';

//       try {
//         const result = JSON.parse(errorOutput);
//         if (result.diagnostics) {
//           for (const diagnostic of result.diagnostics) {
//             if (diagnostic.file_path && diagnostic.file_path.endsWith('.ts')) {
//               files.push(diagnostic.file_path);
//             }
//           }
//         }
//       } catch (parseError) {
//         // 如果 JSON 解析失败，尝试从文本输出中提取文件路径
//         const lines = errorOutput.split('\n');
//         for (const line of lines) {
//           const match = line.match(/^(.+\.ts):/);
//           if (match) {
//             files.push(match[1]);
//           }
//         }
//       }
//     }

//     return [...new Set(files)]; // 去重
//   }

//   /**
//    * 修复单个文件中的注释顺序
//    */
//   fixFileCommentOrder(filePath) {
//     try {
//       const content = fs.readFileSync(filePath, 'utf8');
//       const lines = content.split('\n');
//       let modified = false;

//       for (let i = 0; i < lines.length - 1; i++) {
//         const currentLine = lines[i].trim();
//         const nextLine = lines[i + 1].trim();

//         // 检查是否是 biome-ignore 后跟 @ts-expect-error 的情况
//         if (
//           currentLine.includes('// biome-ignore') &&
//           nextLine.includes('// @ts-expect-error')
//         ) {
//           // 获取原始缩进
//           const currentIndent = lines[i].match(/^(\s*)/)[1];
//           const nextIndent = lines[i + 1].match(/^(\s*)/)[1];

//           // 交换两行，保持缩进
//           lines[i] = nextIndent + nextLine;
//           lines[i + 1] = currentIndent + currentLine;

//           modified = true;
//           console.log(
//             `✓ Fixed comment order in ${path.relative(process.cwd(), filePath)}:${i + 1}`
//           );
//         }
//       }

//       if (modified) {
//         fs.writeFileSync(filePath, lines.join('\n'));
//         return true;
//       }

//       return false;
//     } catch (err) {
//       console.error(`✗ Error processing ${filePath}: ${err.message}`);
//       return false;
//     }
//   }

//   /**
//    * 主处理函数
//    */
//   process() {
//     console.log('🔍 Finding files with comment order issues...');

//     // 获取所有 TypeScript 文件
//     const allFiles = this.getAllTSFiles();

//     console.log(`📁 Processing ${allFiles.length} TypeScript files...`);

//     let totalFixed = 0;
//     for (const filePath of allFiles) {
//       if (this.fixFileCommentOrder(filePath)) {
//         totalFixed++;
//       }
//     }

//     console.log(`\n🎉 Finished! Fixed comment order in ${totalFixed} files`);
//   }

//   /**
//    * 获取所有 TypeScript 文件
//    */
//   getAllTSFiles() {
//     const files = [];

//     const walkDir = (dir) => {
//       const items = fs.readdirSync(dir);

//       for (const item of items) {
//         const fullPath = path.join(dir, item);
//         const stat = fs.statSync(fullPath);

//         if (stat.isDirectory()) {
//           // 跳过 node_modules 和其他不需要的目录
//           if (
//             !item.startsWith('.') &&
//             item !== 'node_modules' &&
//             item !== 'dist' &&
//             item !== 'build'
//           ) {
//             walkDir(fullPath);
//           }
//         } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
//           files.push(fullPath);
//         }
//       }
//     };

//     // 从 api 目录开始
//     const apiDir = path.join(process.cwd(), 'api');
//     if (fs.existsSync(apiDir)) {
//       walkDir(apiDir);
//     }

//     return files;
//   }
// }

// // CLI 接口
// function main() {
//   const args = process.argv.slice(2);

//   if (args.includes('--help') || args.includes('-h')) {
//     console.log(`
// Usage: node fix-comment-order.js

// This script fixes the order of comments by moving @ts-expect-error before biome-ignore.

// Examples:
//   node fix-comment-order.js    # Fix comment order in all TypeScript files
//     `);
//     return;
//   }

//   const fixer = new CommentOrderFixer();
//   fixer.process();
// }

// if (require.main === module) {
//   main();
// }

// module.exports = { CommentOrderFixer };
