// #!/usr/bin/env node

// const fs = require('fs');
// const path = require('path');
// const { execSync } = require('child_process');

// /**
//  * 简化版的 TypeScript 错误自动添加 @ts-expect-error 脚本
//  */
// class SimpleTSExpectErrorAdder {
//   constructor() {
//     this.processedFiles = new Set();
//   }

//   /**
//    * 获取 TypeScript 编译错误
//    */
//   getTSErrors() {
//     const errors = [];

//     try {
//       // 尝试在当前目录运行 tsc
//       execSync('npx tsc --noEmit --pretty false', {
//         encoding: 'utf8',
//         cwd: process.cwd(),
//         stdio: 'pipe',
//       });
//     } catch (error) {
//       const errorOutput = error.stdout || error.stderr || '';
//       this.parseErrorOutput(errorOutput, errors);
//     }

//     // 如果当前目录没有错误，尝试在 api 目录运行
//     if (errors.length === 0 && fs.existsSync(path.join(process.cwd(), 'api'))) {
//       try {
//         execSync('npx tsc --noEmit --pretty false', {
//           encoding: 'utf8',
//           cwd: path.join(process.cwd(), 'api'),
//           stdio: 'pipe',
//         });
//       } catch (error) {
//         const errorOutput = error.stdout || error.stderr || '';
//         this.parseErrorOutput(errorOutput, errors, 'api/');
//       }
//     }

//     return errors;
//   }

//   parseErrorOutput(errorOutput, errors, prefix = '') {
//     const lines = errorOutput.split('\n');

//     for (const line of lines) {
//       // 匹配 TypeScript 错误格式: file.ts(line,col): error TSxxxx: message
//       const match = line.match(
//         /^(.+?)\((\d+),(\d+)\):\s*error\s+TS(\d+):\s*(.+)$/
//       );
//       if (match) {
//         const [, file, lineNum, column, code, message] = match;
//         const fullPath = prefix
//           ? path.resolve(prefix + file)
//           : path.resolve(file);
//         errors.push({
//           file: fullPath,
//           line: parseInt(lineNum, 10),
//           column: parseInt(column, 10),
//           code: parseInt(code, 10),
//           message: message.trim(),
//         });
//       }
//     }
//   }

//   /**
//    * 检查是否应该添加 @ts-expect-error
//    */
//   shouldAddTsExpectError(error) {
//     const targetCodes = [
//       7006, // Parameter implicitly has an 'any' type
//       7053, // Element implicitly has an 'any' type
//       2322, // Type 'X' is not assignable to type 'Y'
//       2345, // Argument of type 'X' is not assignable to parameter of type 'Y'
//       2769, // No overload matches this call
//       18046, // 'error' is of type 'unknown'
//       2339, // Property does not exist on type
//       2571, // Object is of type 'unknown'
//       2304, // Cannot find name
//       2353, // Object literal may only specify known properties
//       18047, // 'X' is possibly 'null'
//       18048, // 'X' is possibly 'undefined'
//       18049, // 'X' is possibly 'null' or 'undefined'
//       2531, // Object is possibly 'null'
//       2532, // Object is possibly 'undefined'
//       2564, // Property has no initializer and is not definitely assigned
//       7005, // Variable implicitly has an 'any' type
//       7034, // Variable implicitly has type 'any' in some locations
//       7031, // Binding element implicitly has an 'any' type
//       7008, // Member implicitly has an 'any' type
//       2538, // Type cannot be used as an index type
//       2454, // Variable is used before being assigned
//       2344, // Type does not satisfy the constraint
//       7016, // Could not find a declaration file for module
//       2321, // Excessive stack depth comparing types
//       2416, // Property in type is not assignable to the same property
//       2790, // The operand of a 'delete' operator must be optional
//       2464, // A computed property name must be of type
//       2722, // Cannot invoke an object which is possibly 'undefined'
//       2554, // Expected 0 arguments, but got 1
//       2365, // Operator cannot be applied to types
//       2352, // Conversion of type may be a mistake
//       2411, // Property of type is not assignable to string index
//       2783, // Property is specified more than once
//       7010, // Function lacks return-type annotation
//       7019, // Rest parameter implicitly has an 'any[]' type
//       2739, // Type is missing the following properties
//       2740, // Type is missing the following properties
//       2488, // Type must have a '[Symbol.iterator]()' method
//       2533, // Object is possibly 'null' or 'undefined'
//       7022, // Variable implicitly has type 'any' because it does not have a type annotation
//       2551, // Property does not exist on type
//       2677, // A type predicate's type must be assignable to its parameter's type
//       7023, // Function implicitly has return type 'any'
//     ];

//     return targetCodes.includes(error.code);
//   }

//   /**
//    * 在指定行前添加 @ts-expect-error 注释
//    */
//   addTsExpectError(filePath, lineNumber) {
//     try {
//       const content = fs.readFileSync(filePath, 'utf8');
//       const lines = content.split('\n');

//       // 检查前一行是否已经有 @ts-expect-error
//       const prevLineIndex = lineNumber - 2;
//       if (prevLineIndex >= 0) {
//         const prevLine = lines[prevLineIndex].trim();
//         if (
//           prevLine.includes('@ts-expect-error') ||
//           prevLine.includes('// @ts-expect-error') ||
//           prevLine.includes('/* @ts-expect-error */')
//         ) {
//           return false; // 已经存在，跳过
//         }
//       }

//       // 获取当前行的缩进
//       const currentLine = lines[lineNumber - 1] || '';
//       const indentMatch = currentLine.match(/^(\s*)/);
//       const indentation = indentMatch ? indentMatch[1] : '';

//       // 插入 @ts-expect-error 注释
//       const tsExpectErrorLine = `${indentation}// @ts-expect-error`;
//       lines.splice(lineNumber - 1, 0, tsExpectErrorLine);

//       fs.writeFileSync(filePath, lines.join('\n'));
//       console.log(
//         `✓ Added @ts-expect-error to ${path.relative(process.cwd(), filePath)}:${lineNumber}`
//       );
//       return true;
//     } catch (err) {
//       console.error(
//         `✗ Error processing ${filePath}:${lineNumber} - ${err.message}`
//       );
//       return false;
//     }
//   }

//   /**
//    * 处理单个文件的错误
//    */
//   processFile(filePath, errors) {
//     const fileErrors = errors.filter((error) => error.file === filePath);
//     if (fileErrors.length === 0) return 0;

//     console.log(
//       `\nProcessing ${path.relative(process.cwd(), filePath)} (${fileErrors.length} errors)...`
//     );

//     // 按行号倒序排序，避免添加注释后行号偏移
//     fileErrors.sort((a, b) => b.line - a.line);

//     let addedCount = 0;
//     for (const error of fileErrors) {
//       if (this.shouldAddTsExpectError(error)) {
//         if (this.addTsExpectError(filePath, error.line)) {
//           addedCount++;
//         }
//       } else {
//         console.log(
//           `  - Skipped TS${error.code}: ${error.message.substring(0, 80)}...`
//         );
//       }
//     }

//     return addedCount;
//   }

//   /**
//    * 移除未使用的 @ts-expect-error 指令
//    */
//   removeUnusedTsExpectError(filePath, lineNumber) {
//     try {
//       const content = fs.readFileSync(filePath, 'utf8');
//       const lines = content.split('\n');

//       // 检查指定行是否包含 @ts-expect-error
//       const targetLine = lines[lineNumber - 1];
//       if (targetLine && targetLine.trim().includes('@ts-expect-error')) {
//         // 移除该行
//         lines.splice(lineNumber - 1, 1);
//         fs.writeFileSync(filePath, lines.join('\n'));
//         console.log(
//           `✓ Removed unused @ts-expect-error from ${path.relative(process.cwd(), filePath)}:${lineNumber}`
//         );
//         return true;
//       }
//       return false;
//     } catch (err) {
//       console.error(
//         `✗ Error processing ${filePath}:${lineNumber} - ${err.message}`
//       );
//       return false;
//     }
//   }

//   /**
//    * 处理未使用的 @ts-expect-error 指令
//    */
//   processUnusedDirectives(targetPath) {
//     console.log('🔍 Getting TypeScript errors...');
//     const errors = this.getTSErrors();

//     // 过滤出 TS2578 错误（未使用的 @ts-expect-error 指令）
//     const unusedDirectives = errors.filter((error) => error.code === 2578);

//     if (unusedDirectives.length === 0) {
//       console.log('✅ No unused @ts-expect-error directives found!');
//       return;
//     }

//     console.log(
//       `📋 Found ${unusedDirectives.length} unused @ts-expect-error directives`
//     );

//     // 按文件分组处理错误
//     const fileGroups = new Map();

//     for (const error of unusedDirectives) {
//       if (targetPath && !error.file.includes(targetPath)) {
//         continue; // 跳过不匹配的文件
//       }

//       if (!fileGroups.has(error.file)) {
//         fileGroups.set(error.file, []);
//       }
//       fileGroups.get(error.file).push(error);
//     }

//     console.log(`📁 Processing ${fileGroups.size} files...`);

//     // 处理每个文件 - 按行号倒序排序，避免删除后行号偏移
//     let totalRemoved = 0;
//     for (const [filePath, fileErrors] of fileGroups) {
//       console.log(
//         `\nProcessing ${path.relative(process.cwd(), filePath)} (${fileErrors.length} unused directives)...`
//       );

//       // 按行号倒序排序
//       fileErrors.sort((a, b) => b.line - a.line);

//       for (const error of fileErrors) {
//         if (this.removeUnusedTsExpectError(filePath, error.line)) {
//           totalRemoved++;
//         }
//       }
//     }

//     console.log(
//       `\n🎉 Finished! Removed ${totalRemoved} unused @ts-expect-error directives`
//     );
//   }

//   /**
//    * 主处理函数
//    */
//   process(targetPath, removeUnused = false) {
//     if (removeUnused) {
//       return this.processUnusedDirectives(targetPath);
//     }

//     console.log('🔍 Getting TypeScript errors...');
//     const errors = this.getTSErrors();

//     if (errors.length === 0) {
//       console.log('✅ No TypeScript errors found!');
//       return;
//     }

//     console.log(`📋 Found ${errors.length} TypeScript errors`);

//     // 按文件分组处理错误
//     const fileGroups = new Map();

//     for (const error of errors) {
//       if (targetPath && !error.file.includes(targetPath)) {
//         continue; // 跳过不匹配的文件
//       }

//       if (!fileGroups.has(error.file)) {
//         fileGroups.set(error.file, []);
//       }
//       fileGroups.get(error.file).push(error);
//     }

//     console.log(`📁 Processing ${fileGroups.size} files...`);

//     // 处理每个文件
//     let totalAdded = 0;
//     for (const [filePath, fileErrors] of fileGroups) {
//       totalAdded += this.processFile(filePath, fileErrors);
//     }

//     console.log(`\n🎉 Finished! Added ${totalAdded} @ts-expect-error comments`);
//   }
// }

// // CLI 接口
// function main() {
//   const args = process.argv.slice(2);
//   const removeUnused = args.includes('--remove-unused') || args.includes('-r');
//   const targetPath = args.find(
//     (arg) => !arg.startsWith('--') && !arg.startsWith('-')
//   );

//   if (args.includes('--help') || args.includes('-h')) {
//     console.log(`
// Usage: node simple-ts-expect-error.js [options] [target-path]

// Options:
//   --remove-unused, -r    Remove unused @ts-expect-error directives
//   --help, -h            Show this help message
//   target-path           Optional path filter (e.g., "oneHQ.ts" or "services/queue")

// Examples:
//   node simple-ts-expect-error.js                           # Add @ts-expect-error to all files
//   node simple-ts-expect-error.js oneHQ.ts                 # Process files containing "oneHQ.ts"
//   node simple-ts-expect-error.js services/queue           # Process files in services/queue
//   node simple-ts-expect-error.js --remove-unused          # Remove unused @ts-expect-error directives
//   node simple-ts-expect-error.js -r oneHQ.ts             # Remove unused directives from oneHQ.ts files
//     `);
//     return;
//   }

//   const adder = new SimpleTSExpectErrorAdder();
//   adder.process(targetPath, removeUnused);
// }

// if (require.main === module) {
//   main();
// }

// module.exports = { SimpleTSExpectErrorAdder };
