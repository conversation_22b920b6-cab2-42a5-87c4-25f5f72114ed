// #!/usr/bin/env ts-node

// import * as fs from 'fs';
// import * as path from 'path';
// // @ts-nocheck
// import * as ts from 'typescript';
// import { execSync } from 'child_process';

// interface TSError {
//   file: string;
//   line: number;
//   column: number;
//   code: number;
//   message: string;
// }

// class TSExpectErrorAdder {
//   private configPath: string;
//   private program: ts.Program;
//   private checker: ts.TypeChecker;

//   constructor(configPath: string = './tsconfig.json') {
//     this.configPath = configPath;
//     const config = ts.readConfigFile(configPath, ts.sys.readFile);
//     const parsedConfig = ts.parseJsonConfigFileContent(
//       config.config,
//       ts.sys,
//       path.dirname(configPath)
//     );

//     this.program = ts.createProgram(
//       parsedConfig.fileNames,
//       parsedConfig.options
//     );
//     this.checker = this.program.getTypeChecker();
//   }

//   /**
//    * 获取 TypeScript 编译错误
//    */
//   private getTSErrors(): TSError[] {
//     const errors: TSError[] = [];

//     try {
//       // 运行 tsc --noEmit 获取错误信息
//       const output = execSync('npx tsc --noEmit --pretty false', {
//         encoding: 'utf8',
//         cwd: process.cwd(),
//       });
//       // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
//     } catch (error: any) {
//       const errorOutput = error.stdout || error.message;
//       const lines = errorOutput.split('\n');

//       for (const line of lines) {
//         const match = line.match(
//           /^(.+?)\((\d+),(\d+)\):\s*error\s+TS(\d+):\s*(.+)$/
//         );
//         if (match) {
//           const [, file, lineNum, column, code, message] = match;
//           errors.push({
//             file: path.resolve(file),
//             line: parseInt(lineNum, 10),
//             column: parseInt(column, 10),
//             code: parseInt(code, 10),
//             message: message.trim(),
//           });
//         }
//       }
//     }

//     return errors;
//   }

//   /**
//    * 检查是否应该添加 @ts-expect-error
//    */
//   private shouldAddTsExpectError(error: TSError): boolean {
//     const ignoreCodes = [
//       7006, // Parameter implicitly has an 'any' type
//       7053, // Element implicitly has an 'any' type
//       2322, // Type 'X' is not assignable to type 'Y'
//       2345, // Argument of type 'X' is not assignable to parameter of type 'Y'
//       2769, // No overload matches this call
//       18046, // 'error' is of type 'unknown'
//       2578, // Unused '@ts-expect-error' directive
//     ];

//     return ignoreCodes.includes(error.code);
//   }

//   /**
//    * 在指定行前添加 @ts-expect-error 注释
//    */
//   private addTsExpectError(
//     filePath: string,
//     lineNumber: number,
//     indentation: string = ''
//   ): void {
//     const content = fs.readFileSync(filePath, 'utf8');
//     const lines = content.split('\n');

//     // 检查前一行是否已经有 @ts-expect-error
//     const prevLineIndex = lineNumber - 2;
//     if (prevLineIndex >= 0) {
//       const prevLine = lines[prevLineIndex].trim();
//       if (
//         prevLine.includes('@ts-expect-error') ||
//         prevLine.includes('// @ts-expect-error')
//       ) {
//         return; // 已经存在，跳过
//       }
//     }

//     // 获取当前行的缩进
//     const currentLine = lines[lineNumber - 1];
//     const currentIndentation = currentLine.match(/^(\s*)/)?.[1] || indentation;

//     // 插入 @ts-expect-error 注释
//     const tsExpectErrorLine = `${currentIndentation}// @ts-expect-error`;
//     lines.splice(lineNumber - 1, 0, tsExpectErrorLine);

//     fs.writeFileSync(filePath, lines.join('\n'));
//     console.log(`Added @ts-expect-error to ${filePath}:${lineNumber}`);
//   }

//   /**
//    * 获取源文件的缩进风格
//    */
//   private getIndentation(sourceFile: ts.SourceFile): string {
//     const text = sourceFile.getFullText();
//     const lines = text.split('\n');

//     for (const line of lines) {
//       const match = line.match(/^(\s+)/);
//       if (match) {
//         const indent = match[1];
//         if (indent.includes('\t')) {
//           return '\t';
//         } else {
//           return '  '; // 默认2个空格
//         }
//       }
//     }

//     return '  '; // 默认2个空格
//   }

//   /**
//    * 处理单个文件的错误
//    */
//   private processFile(filePath: string, errors: TSError[]): void {
//     const fileErrors = errors.filter((error) => error.file === filePath);
//     if (fileErrors.length === 0) return;

//     console.log(`Processing ${filePath} with ${fileErrors.length} errors...`);

//     const sourceFile = this.program.getSourceFile(filePath);
//     if (!sourceFile) return;

//     const indentation = this.getIndentation(sourceFile);

//     // 按行号倒序排序，避免添加注释后行号偏移
//     fileErrors.sort((a, b) => b.line - a.line);

//     for (const error of fileErrors) {
//       if (this.shouldAddTsExpectError(error)) {
//         this.addTsExpectError(filePath, error.line, indentation);
//       }
//     }
//   }

//   /**
//    * 主处理函数
//    */
//   public async process(targetPath?: string): Promise<void> {
//     console.log('Getting TypeScript errors...');
//     const errors = this.getTSErrors();

//     if (errors.length === 0) {
//       console.log('No TypeScript errors found!');
//       return;
//     }

//     console.log(`Found ${errors.length} TypeScript errors`);

//     // 按文件分组处理错误
//     const fileGroups = new Map<string, TSError[]>();

//     for (const error of errors) {
//       if (targetPath && !error.file.includes(targetPath)) {
//         continue; // 跳过不匹配的文件
//       }

//       if (!fileGroups.has(error.file)) {
//         fileGroups.set(error.file, []);
//       }
//       fileGroups.get(error.file)!.push(error);
//     }

//     // 处理每个文件
//     for (const [filePath, fileErrors] of fileGroups) {
//       this.processFile(filePath, fileErrors);
//     }

//     console.log('Finished processing TypeScript errors');
//   }
// }

// // CLI 接口
// async function main() {
//   const args = process.argv.slice(2);
//   const targetPath = args[0]; // 可选的目标路径过滤器

//   const adder = new TSExpectErrorAdder();
//   await adder.process(targetPath);
// }

// if (require.main === module) {
//   main().catch(console.error);
// }

// export { TSExpectErrorAdder };
