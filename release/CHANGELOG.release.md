# release

## Releases on 2025-08-11

### Version 4.36.1
<details>

### Patch Changes
 - Fixing manual group handling on the Commissions page
</details>

### Version 4.36.0
<details>

### Minor Changes
 - Supports for advance commission schedules

### Patch Changes
 - Remove role checking for grouping rules page
 - Adds group search and alphabetical sorting for Data update tools
 - Extend data actions tool to being able to set json fields in the db
</details>

## Releases on 2025-08-07

### Version 4.35.3
<details>

### Patch Changes
 - Fixed pagination on agents transactions details page
 - Delete orphan virtual records after ungrouping
 - Reset data on closing grouping dialog
 - Fixed agent commissions payout rate for sales rep in comp report calculation.
 - Added agent payout rate field to bulk edit in commissions page
</details>

## Releases on 2025-08-05

### Version 4.35.2
<details>

### Patch Changes
 - Fix the issue where bulk document edits cannot be saved
</details>

## Releases on 2025-08-04

### Version 4.35.1
<details>

### Patch Changes
 - Update the commission total calculation on the Documents page to exclude duplicate commission amounts introduced by Grouping V2.
</details>

## Releases on 2025-08-01

### Version 4.35.0
<details>

### Minor Changes
 - Implemented agent payout balance optimization for report group summary page

### Patch Changes
 - Add auto document processing trigger to bulk edit.
 - Add more carriers to the carrier syncing whitelist for Broker Alliance account
</details>

## Releases on 2025-07-31

### Version 4.34.0
<details>

### Minor Changes
 - Allow users to do bulk un-group commissions
 - Re-enable the member syncing feature for RiskTag
 - Added statement month filter to documents page.

### Patch Changes
 - Document part optimisations:
  1. Fixed page crash issue when uploading files in the local environment
  2. Added status tooltips on the admin document page
  3. Added processor selector and classification to the API whitelist
  4. Replaced document type logic with currency.js
 - Add more tests for comp calc regression tests.
 - Revert comp calc with no comp grid to its previous working logic.
</details>

## Releases on 2025-07-30

### Version 4.33.2
<details>

### Patch Changes
 - Fix the document page crash issue by filter color
 - Fix the issue of group line split percentage comp error
</details>

## Releases on 2025-07-29

### Version 4.33.1
<details>

### Patch Changes
 - Sync sales vps for DMI
 - Include Premium amount field to bulk edit csv in the commissions page.
 - Dashboard improvements including data filter in chart info, reduction of implicit defaults (such as date filter type), schema validation on save, and `none` as a date type.
 - Improvements for Global Documents:
  • Add an enhanced company filters that shows global companies, or account companies if no global ones exist.
  • Remove fuzzy search and replace with strict matching.
  • Add edit mode for documents.
  • Fix various UI bugs.
 - Added capability to update grouped commission lines to data actions tool
 - Improved extraction logic to better distinguish amounts from date-like numbers.
</details>

### Version 4.33.0
<details>

### Minor Changes
 - Fixed: bulk edit from csv of commissions failed on grouped data and it didn't show a clear error message.

### Patch Changes
 - Fixed error when update policy term months
 - Updated the compensation type logic, now we treat compensation type as Renew only when the policy start year is greater than 1
 - Fixed override documents upload
 - Add an auto-trigger for E2E document processing.
  Currently, auto-processing is triggered in two scenarios:
  1. When a document is uploaded for the first time, and both the company and type fields are not empty.
  2. When the company and type were previously empty but are now filled, and the document status is still “new”.
 - Enhancements to BA MOO grouping logic
</details>

## Releases on 2025-07-28

### Version 4.32.4
<details>

### Patch Changes
 - Added feature to edit agent receivables at policies page
</details>

## Releases on 2025-07-25

### Version 4.32.3
<details>

### Patch Changes
 - Fix the issue where page crashes when clicking on the compare button
 - Fix comp calculation is not taking into account the writing agent split
 - Add comp grid viewer pdf export
 - Fix the issue where pages crashes when selecting any data after manual grouping
</details>

