export const stringSimilarity = (
  str1: string,
  str2: string,
  threshold: number = 10
): boolean => {
  let count = 0;
  for (let i = 0; i < str1?.length; i++) {
    if (str1[i] === str2[i]) {
      count++;
    } else {
      break;
    }
  }
  return count >= threshold;
};

export const findBestMatchKey = (
  obj: object,
  key: string,
  threshold: number = 10
): string => {
  const keys = Object.keys(obj);
  const bestMatch = keys.reduce((best, current) => {
    const similarity = stringSimilarity(key, current, threshold);
    return similarity ? current : best;
  }, '');
  if (!bestMatch) {
    return key;
  }
  // Check if the key is shorter than the best match
  // If so, replace obj[bestMatch] with obj[key] and delete obj[key], return the shorter key

  if (key.length < bestMatch.length) {
    obj[key] = obj[bestMatch];
    delete obj[bestMatch];
    return key;
  }
  return bestMatch;
};
