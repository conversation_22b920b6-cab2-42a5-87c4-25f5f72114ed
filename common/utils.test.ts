import { describe, expect, it } from 'vitest';

import {
  arrayToMap,
  enumToSelectOptions,
  splitCustomerName,
  mergeObjects,
} from './utils';

describe('arrayToMap', () => {
  it('Given an array and a string key, when it is converted to a map, then the result should be a map with keys derived from the string key', () => {
    const arr = [
      { id: 1, name: '<PERSON>' },
      { id: 2, name: '<PERSON>' },
    ];
    const key = 'id';
    const result = arrayToMap(arr, key);
    expect(result).toEqual({
      1: { id: 1, name: 'Alice' },
      2: { id: 2, name: '<PERSON>' },
    });
  });

  it('Given an array and a function key, when it is converted to a map, then the result should be a map with keys derived from the function key', () => {
    const arr = [
      { id: 1, name: 'Alice' },
      { id: 2, name: 'Bob' },
    ];
    const keyFn = (e: { name: string }) => e.name;
    const result = arrayToMap(arr, keyFn);
    expect(result).toEqual({
      Alice: { id: 1, name: '<PERSON>' },
      Bob: { id: 2, name: '<PERSON>' },
    });
  });

  it('Given a null or undefined array, when it is processed, then the result should be an empty object', () => {
    expect(arrayToMap(null, 'id')).toEqual({});
    expect(arrayToMap(undefined, 'id')).toEqual({});
  });
});

describe('enumToSelectOptions', () => {
  it('converts an enum object to an array of select options', () => {
    const enumObj = { ACTIVE: 'Active', INACTIVE: 'Inactive' };
    const result = enumToSelectOptions(enumObj);
    expect(result).toEqual([
      { id: 'ACTIVE', label: 'Active' },
      { id: 'INACTIVE', label: 'Inactive' },
    ]);
  });

  it('returns an empty array for an empty object', () => {
    expect(enumToSelectOptions({})).toEqual([]);
  });
});

describe('splitCustomerName', () => {
  it('splits a full name into first and last name', () => {
    expect(splitCustomerName('John Doe')).toEqual({
      first_name: 'John',
      last_name: 'Doe',
    });
  });

  it('splits a name with a comma', () => {
    expect(splitCustomerName('Doe, John')).toEqual({
      first_name: 'Doe',
      last_name: ' John',
    });
  });

  it('handles names with multiple spaces', () => {
    expect(splitCustomerName('John    Doe')).toEqual({
      first_name: 'John',
      last_name: 'Doe',
    });
  });

  it('returns empty fields for an empty name', () => {
    expect(splitCustomerName('')).toEqual({
      first_name: '',
      last_name: '',
    });
  });

  it('handles single names', () => {
    expect(splitCustomerName('John')).toEqual({
      first_name: 'John',
      last_name: '',
    });
  });
});

describe('mergeObjects', () => {
  it('merges two objects', () => {
    const obj1 = { a: 1, b: 2 };
    const obj2 = { b: 3, c: 4 };
    expect(mergeObjects(obj1, obj2)).toEqual({ a: 1, b: 3, c: 4 });
  });

  it('merges objects into an array if `asArray` is true', () => {
    const obj1 = { a: 1 };
    const obj2 = { b: 2 };
    expect(mergeObjects(obj1, obj2, true)).toEqual([{ a: 1 }, { b: 2 }]);
  });

  it('merges into an existing array', () => {
    const obj1 = [{ a: 1 }];
    const obj2 = { b: 2 };
    expect(mergeObjects(obj1, obj2)).toEqual([{ a: 1 }, { b: 2 }]);
  });

  it('filters out null and undefined objects', () => {
    expect(mergeObjects(null, { b: 2 })).toEqual({ b: 2 });
    expect(mergeObjects({ a: 1 }, undefined)).toEqual({ a: 1 });
  });

  it('returns null if all objects are invalid and `asArray` is true', () => {
    expect(mergeObjects(null, undefined, true)).toBeNull();
  });
});
