import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

import Formatter from './Formatter';
import { DATE_TIME_FORMATS } from './constants/date-time';

describe('Formatter.percentage', () => {
  describe('When inputs are not calculated', () => {
    const inputs = [
      { input: 0, expectedResult: '0%' },
      { input: 0.003, expectedResult: '0.30%' },
      { input: 0.00125, expectedResult: '0.13%' },
      { input: 0.00125999999999, expectedResult: '0.13%' },
      { input: 0.25, expectedResult: '25%' },
      { input: 1, expectedResult: '100%' },
      { input: 1.5, expectedResult: '150%' },
      { input: -0.25, expectedResult: '-25%' },
      { input: -1.5, expectedResult: '-150%' },
    ];

    it.each(inputs)(
      'Given $input, should return $expectedResult by multiplying input',
      (value) => {
        expect(Formatter.percentage(value.input)).toBe(value.expectedResult);
      }
    );
  });

  describe('When inputs are pre calculated', () => {
    const inputsPreCalculated = [
      { input: 0, expectedResult: '0%' },
      { input: 150, expectedResult: '150%' },
      { input: -0.25, expectedResult: '-0.25%' },
      { input: -150, expectedResult: '-150%' },
    ];

    it.each(inputsPreCalculated)(
      'Given $input, should return $expectedResult without multiplying input',
      (value) => {
        expect(Formatter.percentage(value.input, { isPercentage: true })).toBe(
          value.expectedResult
        );
      }
    );
  });

  describe('When inputs are invalid numbers', () => {
    const inputs = [
      { input: null, expectedResult: '' },
      { input: undefined, expectedResult: '' },
      { input: 'abc', expectedResult: '' },
      { input: '["100"]', expectedResult: '' },
      { input: { a: '100' }.toString(), expectedResult: '' },
    ];

    it.each(inputs)(
      'Given $input, should return $expectedResult for invalid input',
      (value) => {
        expect(Formatter.percentage(value.input)).toBe(value.expectedResult);
      }
    );
  });

  describe('When rate is below 10%', () => {
    const options = {
      addPrecisionForRateLowerThanTen: true,
      isPercentage: true,
    };

    it.each([
      { input: '9.000144444', expectedResult: '9.000%' },
      { input: '9.001444444', expectedResult: '9.001%' },
      { input: '9.001444445', expectedResult: '9.002%' },
      { input: '9.014444445', expectedResult: '9.015%' },
      { input: '9.015444445', expectedResult: '9.016%' },
      { input: '9.019444445', expectedResult: '9.020%' },
      { input: '9.194444445', expectedResult: '9.195%' },
      { input: '9.19554444', expectedResult: '9.196%' },
      { input: '9.199555555', expectedResult: '9.200%' },
      { input: '9.19', expectedResult: '9.190%' },
      { input: '9.10', expectedResult: '9.100%' },
      { input: '9.1', expectedResult: '9.100%' },
      { input: '9', expectedResult: '9.000%' },
      { input: '0', expectedResult: '0.000%' },
    ])(
      'Given $input, should return $expectedResult with 3 decimal',
      ({ input, expectedResult }) => {
        expect(Formatter.percentage(input, options)).toEqual(expectedResult);
      }
    );
  });

  describe('When rate is greater 10%', () => {
    const options = {
      addPrecisionForRateLowerThanTen: true,
      isPercentage: true,
    };

    it.each([
      { input: '10.000123', expectedResult: '10.00%' },
      { input: '10.000199', expectedResult: '10.00%' },
      { input: '10.00199', expectedResult: '10.00%' },
      { input: '10.01399', expectedResult: '10.01%' },
      { input: '10.01499', expectedResult: '10.02%' },
      { input: '10.019', expectedResult: '10.02%' },
      { input: '10.01', expectedResult: '10.01%' },
      { input: '10.00', expectedResult: '10%' },
      { input: '10', expectedResult: '10%' },
      { input: '100.00123', expectedResult: '100%' },
      { input: '100.00199', expectedResult: '100%' },
      { input: '100.0199', expectedResult: '100%' },
      { input: '100.199', expectedResult: '100%' },
      { input: '100.19', expectedResult: '100%' },
      { input: '100.10', expectedResult: '100%' },
      { input: '100.1', expectedResult: '100%' },
      { input: '100', expectedResult: '100%' },
    ])(
      'Given $input, should return $expectedResult with 2 decimal places',
      ({ input, expectedResult }) => {
        expect(Formatter.percentage(input, options)).toEqual(expectedResult);
      }
    );
  });

  describe('When withoutSymbol is true', () => {
    const inputs = [
      { input: 0, expectedResult: '0' },
      { input: 0.003, expectedResult: '0.30' },
      { input: 0.00125, expectedResult: '0.13' },
      { input: 0.00125999999999, expectedResult: '0.13' },
      { input: 0.25, expectedResult: '25' },
      { input: 1, expectedResult: '100' },
      { input: 1.5, expectedResult: '150' },
      { input: -0.25, expectedResult: '-25' },
      { input: -1.5, expectedResult: '-150' },
    ];

    it.each(inputs)(
      'Given $input, should return $expectedResult without percentage symbol',
      (value) => {
        expect(Formatter.percentage(value.input, { withoutSymbol: true })).toBe(
          value.expectedResult
        );
      }
    );
  });
});

describe('Formatter.agentReportLog', () => {
  beforeEach(() => {
    vi.useFakeTimers();
    const fixedDate = new Date('2025-03-24T09:32:38.833-04:00');
    vi.setSystemTime(fixedDate);
    vi.spyOn(Formatter, 'dateTime').mockImplementation((date) => {
      if (date === 1742830358833) return '3/24/2025, 9:32:38 AM';
      if (date === 1742830339572) return '3/24/2025, 9:32:19 AM';
      return 'mocked-date';
    });
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.restoreAllMocks();
  });

  const defaultAgentContents = [
    {
      alerts: 'Criteria ("contacts" Not writing agent "undefined"}) not met.',
      calculatedAt: 1742830358833,
    },
    {
      calcBasis: '0',
      agentSplit: '1',
      calcMethod: 'payoutRate',
      multiplier: '1',
      payoutRate: '1',
      profile_id: 11123291,
      agentUplines: ['Case Manager Pool'],
      calculatedAt: 1742830339572,
      contactStrId: '-cBdGoZVSQLRj_id_002',
      profile_name: 'Case managers agent override',
      commissionRate: 'Infinity',
      hierarchySplit: '1',
      profile_str_id: 'GZ1FSDFWFLtPgW2res2tF_p',
      commissionAmount: '-Infinity',
    },
  ];

  it('Given AGENT_COMMISSION_LOG, should return formatted log text for valid input', () => {
    const contentMapByIdOrStrId = {
      '-cBdGoZVSQLRj_id_001': [defaultAgentContents[0]],
      '-cBdGoZVSQLRj_id_002': [defaultAgentContents[1]],
    };

    const statementDataItem = {
      agentCommissionContacts: {
        '-cBdGoZVSQLRj_id_001': 'Zimann_A, Taylor_A',
        '-cBdGoZVSQLRj_id_002': 'Case_A Done_A Pool_A',
        '-cBdGoZVSQLRj_id_003': 'Bob_A Dylan_A',
        '-cBdGoZVSQLRj_id_004': 'Mark_A, Andrew_A',
      },
      compCalcContactMapById: {},
    };

    const result = Formatter.agentReportLog({
      type: 'AGENT_COMMISSION_LOG',
      contentMapByIdOrStrId,
      statementDataItem,
    });

    expect(result).not.toBeUndefined();
    expect(typeof result).toEqual('string');
    expect(result.split('\n')).toEqual([
      'Zimann_A, Taylor_A',
      '⚠️: Criteria ("contacts" Not writing agent "undefined"}) not met.',
      `Calculated at: ${Formatter.dateTimeWithTimezone(new Date(1742830358833), undefined, DATE_TIME_FORMATS.US_DATE_TIME_FORMAT_WITH_SECONDS)}`,
      'Case_A Done_A Pool_A',
      'Profile: Case managers agent override, Method: Pay a set rate',
      'Amount: -$Infinity, Effective rate: Infinity%, Payout rate: 1%, Basis: $0.00',
      'Writing agent split: 100%',
      'Hierarchy split: 100%',
      'Multiplier: 100%',
      'Agent hierarchy: Case Manager Pool',
      `Calculated at: ${Formatter.dateTimeWithTimezone(new Date(1742830339572), undefined, DATE_TIME_FORMATS.US_DATE_TIME_FORMAT_WITH_SECONDS)}`,
    ]);
  });

  it('Given AGENT_COMPENSATION_LOG, should return formatted log text for valid input', () => {
    const contentMapByIdOrStrId = {
      '001': [defaultAgentContents[0]],
      '002': [defaultAgentContents[1]],
    };

    const statementDataItem = {
      compCalcContactMapById: {
        '001': { name: 'Zimann_B, Taylor_B', type: 'agent' },
        '002': { name: 'Case_B Done_B Pool_B', type: 'agent' },
        '003': { name: 'Bob_B Dylan_B', type: 'agent' },
        '004': { name: 'Mark_B, Andrew_B', type: 'agent' },
      },
      agentCommissionContacts: {},
    };

    const result = Formatter.agentReportLog({
      type: 'AGENT_COMPENSATION_LOG',
      contentMapByIdOrStrId,
      statementDataItem,
    });

    expect(result).not.toBeUndefined();
    expect(typeof result).toEqual('string');
    expect(result.split('\n')).toEqual([
      'Zimann_B, Taylor_B',
      '⚠️: Criteria ("contacts" Not writing agent "undefined"}) not met.',
      `Calculated at: ${Formatter.dateTimeWithTimezone(new Date(1742830358833), undefined, DATE_TIME_FORMATS.US_DATE_TIME_FORMAT_WITH_SECONDS)}`,
      'Case_B Done_B Pool_B',
      'Profile: Case managers agent override, Method: Pay a set rate',
      'Amount: -$Infinity, Effective rate: Infinity%, Payout rate: 1%, Basis: $0.00',
      'Writing agent split: 100%',
      'Hierarchy split: 100%',
      'Multiplier: 100%',
      'Agent hierarchy: Case Manager Pool',
      `Calculated at: ${Formatter.dateTimeWithTimezone(new Date(1742830339572), undefined, DATE_TIME_FORMATS.US_DATE_TIME_FORMAT_WITH_SECONDS)}`,
    ]);
  });

  describe('When contentMapByIdOrStrId input value is not an object', () => {
    const invalidValues = [null, 123, 'string', [], undefined];

    it.each(invalidValues)(
      'Given %s, should return an empty string for invalid input',
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (value: any) => {
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        const input = { contentMapByIdOrStrId: value } as any;
        expect(Formatter.agentReportLog(input)).toEqual('');
      }
    );
  });
  describe('Formatter.isGrouped', () => {
    it('given grouped state should return "Yes"', () => {
      const result = Formatter.isGrouped('grouped');
      expect(result).toBe('Yes');
    });

    it('given non-grouped state should return "No"', () => {
      const result = Formatter.isGrouped('active');
      expect(result).toBe('No');
    });

    it('given undefined value should return "No"', () => {
      const result = Formatter.isGrouped(undefined);
      expect(result).toBe('No');
    });

    it('given null value should return "No"', () => {
      const result = Formatter.isGrouped(null as string | undefined);
      expect(result).toBe('No');
    });

    it('given empty string should return "No"', () => {
      const result = Formatter.isGrouped('');
      expect(result).toBe('No');
    });

    it('given uppercase grouped should return "No"', () => {
      const result = Formatter.isGrouped('GROUPED');
      expect(result).toBe('No');
    });

    it('given other DataStates values should return "No"', () => {
      const otherStates = [
        'active',
        'archived',
        'deleted',
        'duplicate',
        'allocated',
      ];

      for (const state of otherStates) {
        const result = Formatter.isGrouped(state);
        expect(result).toBe('No');
      }
    });
  });
});
