# common

## Releases on 2025-08-11

### Version 0.24.26
<details>

### Patch Changes
 - Add custom report delete feature
 - Update the base url for e2e tests
 - Add e2e tests to preview and prod.
 - Speed up login / middleware by reducing/parallelizing db queries
</details>

## Releases on 2025-08-07

### Version 0.24.25
<details>

### Patch Changes
 - Removed 'Select one' option from unit selector for date fields in data actions criteria
 - Fix the e2e tests error
 - Added new minimum width for file column
 - Add virtual_type filter and update loadFieldFilters function
 - Update divider field config to disable
 - Export alignment on Commission page
  - Move config from web to common, and add textFormatter for use in export
  - Refactor the statement_data export logic with the following steps: getData → get fields based on config and accountSetting → fetch dynamicSelect data → formatData → export CSV
 - Revamped widget spec schema and applied it across all saved widgets
 - Move the 'Is grouped' column to the Views and Fields config
</details>

## Releases on 2025-08-05

### Version 0.24.24
<details>

### Patch Changes
 - Update the notification for the e2e test
</details>

## Releases on 2025-08-04

### Version 0.24.23
<details>

### Patch Changes
 - Remove duplicated field definitions
</details>

## Releases on 2025-08-01

### Version 0.24.22
<details>

### Patch Changes
 - Bulk run receivable calc from data processing page
</details>

## Releases on 2025-07-31

### Version 0.24.21
<details>

### Patch Changes
 - Add more e2e test cases and clean up e2e test work flow.
</details>

## Releases on 2025-07-29

### Version 0.24.20
<details>

### Patch Changes
 - Dashboard improvements including data filter in chart info, reduction of implicit defaults (such as date filter type), schema validation on save, and `none` as a date type.
 - Use UTC time to display statement_month and deposit_date
 - Align export Reconciliation behavior with FE, move config from web to common, and add textFormatter for use in export.
 - Improved extraction logic to better distinguish amounts from date-like numbers.
</details>

## Releases on 2025-07-28

### Version 0.24.19
<details>

### Patch Changes
 - Added feature to edit agent receivables at policies page
</details>

## Releases on 2025-07-25

### Version 0.24.18
<details>

### Patch Changes
 - Added support to edit receivables when edited from the policies page.
 - Added document classification stats to Metrics page
</details>

## Releases on 2025-07-24

### Version 0.24.17
<details>

### Patch Changes
 - Add some missing fields in the export.
</details>

