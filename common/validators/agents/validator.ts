import { isValidDateRange } from '../../helpers/datetime';

export const hasInvalidDateRange = (data) => {
  const keyHasDateRange = [
    'contact_level',
    'parent_relationships',
    'contact_referrals',
    'contacts_agent_commission_schedule_profiles',
    'contacts_agent_commission_schedule_profiles_sets',
  ];
  for (const key of keyHasDateRange) {
    const invalidDateRange = data[key]?.find((item) => {
      return !isValidDateRange({
        start: item.start_date,
        end: item.end_date,
      });
    });

    if (invalidDateRange) {
      return key;
    }
  }

  return '';
};
