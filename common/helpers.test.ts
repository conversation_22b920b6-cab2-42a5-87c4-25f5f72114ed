import { describe, it, expect } from 'vitest';
import dayjs from 'dayjs';

import * as helpers from './helpers';
import type { Statement } from './helpers';

describe('getUpdatedFields', () => {
  it('Given different date fields, when compared, should return updated date fields', () => {
    const oldObj = { start_date: '2023-01-01' };
    const newObj = { start_date: '2023-01-02' };
    expect(helpers.getUpdatedFields(oldObj, newObj)).toContain('start_date');
  });

  it('Given different amount fields, when compared, should return updated amount fields', () => {
    const oldObj = { total_amount: 100 };
    const newObj = { total_amount: 200 };
    expect(helpers.getUpdatedFields(oldObj, newObj)).toContain('total_amount');
  });

  it('Given equal objects, when compared, should return empty array', () => {
    const obj = { foo: 1, bar: 'baz' };
    expect(helpers.getUpdatedFields(obj, { ...obj })).toEqual([]);
  });
});

describe('numberOrDefault', () => {
  it('Given a valid number, should return the number', () => {
    expect(helpers.numberOrDefault('42')).toBe(42);
  });

  it('Given an invalid number, should return the default', () => {
    expect(helpers.numberOrDefault('abc', 7)).toBe(7);
  });

  it('Given toFixed option, should return fixed number', () => {
    expect(helpers.numberOrDefault('3.14159', 0, { toFixed: 2 })).toBe(3.14);
  });
});

describe('dateOrDefault', () => {
  it('Given a valid date string, should return a Date object', () => {
    expect(helpers.dateOrDefault('2023-01-01') instanceof Date).toBe(true);
  });

  it('Given an invalid date, should return the default', () => {
    expect(helpers.dateOrDefault('not-a-date', 'default')).toBe('default');
  });
});

describe('isNill', () => {
  it('Given null or undefined, should return true', () => {
    expect(helpers.isNill(null)).toBe(true);
    expect(helpers.isNill(undefined)).toBe(true);
  });

  it('Given empty string or "NaN", should return true', () => {
    expect(helpers.isNill('')).toBe(true);
    expect(helpers.isNill('NaN')).toBe(true);
  });

  it('Given a valid value, should return false', () => {
    expect(helpers.isNill(0)).toBe(false);
    expect(helpers.isNill('foo')).toBe(false);
  });
});

describe('isValidJsonString', () => {
  it('Given a valid JSON string, should return true', () => {
    expect(helpers.isValidJsonString('{"a":1}')).toBe(true);
  });

  it('Given an invalid JSON string, should return false', () => {
    expect(helpers.isValidJsonString('{a:1}')).toBe(false);
  });
});

describe('doDateRangesOverlap', () => {
  it('Given overlapping ranges, should return true', () => {
    const ranges = [
      { start_date: new Date('2023-01-01'), end_date: new Date('2023-01-10') },
      { start_date: new Date('2023-01-05'), end_date: new Date('2023-01-15') },
    ];
    expect(helpers.doDateRangesOverlap(ranges)).toBe(true);
  });

  it('Given non-overlapping ranges, should return false', () => {
    const ranges = [
      { start_date: new Date('2023-01-01'), end_date: new Date('2023-01-10') },
      { start_date: new Date('2023-01-11'), end_date: new Date('2023-01-20') },
    ];
    expect(helpers.doDateRangesOverlap(ranges)).toBe(false);
  });
});

describe('convertDateFields', () => {
  it('Given object with _date fields, should convert to dayjs', () => {
    const obj = { foo_date: '2023-01-01', bar: 1 };
    const result = helpers.convertDateFields(obj);
    expect(dayjs.isDayjs(result.foo_date)).toBe(true);
    expect(result.bar).toBe(1);
  });
});

describe('getFilenameFromPath', () => {
  it('Given a path, should return the filename', () => {
    expect(helpers.getFilenameFromPath('/foo/bar/baz.txt')).toBe('baz.txt');
  });

  it('Given a filename with nanoid, should remove it', () => {
    expect(helpers.getFilenameFromPath('123456789012345678901-baz.txt')).toBe(
      'baz.txt'
    );
  });
});

describe('isNonEmptyArray', () => {
  it('Given a non-empty array, should return true', () => {
    expect(helpers.isNonEmptyArray([1, 2, 3])).toBe(true);
  });

  it('Given an empty array, should return false', () => {
    expect(helpers.isNonEmptyArray([])).toBe(false);
  });

  it('Given a non-array, should return false', () => {
    expect(helpers.isNonEmptyArray('foo')).toBe(false);
  });
});

describe('tryDecodeURIComponent', () => {
  it('Given an encoded string, should decode it', () => {
    expect(helpers.tryDecodeURIComponent('foo%20bar')).toBe('foo bar');
  });

  it('Given a non-encoded string, should return as is', () => {
    expect(helpers.tryDecodeURIComponent('foobar')).toBe('foobar');
  });
});

describe('generateAgentPayoutRate', () => {
  it('Given valid statement and commissions, should return correct rates', () => {
    const statement: Statement = {
      premium_amount: 1000,
      split_percentage: 100,
      commission_amount: 100,
      premium_type: 'split',
    };
    const commissions = { agent1: 50, agent2: 50 };
    const result = helpers.generateAgentPayoutRate(statement, commissions);
    expect(result?.agentPayoutRate.agent1).not.toBeUndefined();
    expect(result?.agentCommissionPayoutRate.agent1).toBe(50);
  });

  it('Given zero premium_amount, should return "n/a" for agent payout rate', () => {
    const statement: Statement = {
      premium_amount: 0,
      split_percentage: 100,
      commission_amount: 100,
      premium_type: 'split',
    };
    const commissions = { agent1: 50, agent2: 50 };
    const result = helpers.generateAgentPayoutRate(statement, commissions);
    expect(result?.agentPayoutRate.agent1).toBe('n/a');
    expect(result?.agentCommissionPayoutRate.agent1).toBe(50);
  });

  it('Given zero commission_amount, should return "n/a" for commission payout rate', () => {
    const statement: Statement = {
      premium_amount: 1000,
      split_percentage: 100,
      commission_amount: 0,
      premium_type: 'split',
    };
    const commissions = { agent1: 50, agent2: 50 };
    const result = helpers.generateAgentPayoutRate(statement, commissions);
    expect(result?.agentCommissionPayoutRate.agent1).toBe('n/a');
    expect(typeof result?.agentPayoutRate.agent1).toBe('number');
  });

  it('Given negative premium_amount, should handle gracefully', () => {
    const statement: Statement = {
      premium_amount: -1000,
      split_percentage: 100,
      commission_amount: 100,
      premium_type: 'split',
    };
    const commissions = { agent1: 50, agent2: 50 };
    const result = helpers.generateAgentPayoutRate(statement, commissions);
    expect(result).toBeDefined();
  });

  it('Given negative commission_amount, should handle gracefully', () => {
    const statement: Statement = {
      premium_amount: 1000,
      split_percentage: 100,
      commission_amount: -100,
      premium_type: 'split',
    };
    const commissions = { agent1: 50, agent2: 50 };
    const result = helpers.generateAgentPayoutRate(statement, commissions);
    expect(result).toBeDefined();
  });

  it('Given very small decimal amounts, should handle precision', () => {
    const statement: Statement = {
      premium_amount: 0.01,
      split_percentage: 0.5,
      commission_amount: 0.001,
      premium_type: 'split',
    };
    const commissions = { agent1: 0.0005, agent2: 0.0005 };
    const result = helpers.generateAgentPayoutRate(statement, commissions);
    expect(result).toBeDefined();
  });

  it('Given large numbers, should handle without overflow', () => {
    const statement: Statement = {
      premium_amount: 999999999,
      split_percentage: 100,
      commission_amount: 99999999,
      premium_type: 'split',
    };
    const commissions = { agent1: 49999999.5, agent2: 49999999.5 };
    const result = helpers.generateAgentPayoutRate(statement, commissions);
    expect(result).toBeDefined();
  });
});

describe('toArray', () => {
  it('Given a single value, should return array with value', () => {
    expect(helpers.toArray(1)).toEqual([1]);
  });

  it('Given an array, should return the same array', () => {
    expect(helpers.toArray([1, 2])).toEqual([1, 2]);
  });
});

describe('toArray', () => {
  it('Given a single value, should return array with value', () => {
    expect(helpers.toArray(1)).toEqual([1]);
  });

  it('Given an array, should return the same array', () => {
    expect(helpers.toArray([1, 2])).toEqual([1, 2]);
  });
});

describe('isValidString', () => {
  it.each(['a', '1', 'abc', '{}', '[]'])(
    'Given a valid string, should return true',
    () => {
      expect(helpers.isValidString('hello')).toBe(true);
    }
  );
  it.each([null, undefined, 123, true, ' ', '    ', [], {}])(
    'Should return false when receives an invalid string: %s',
    (input) => {
      expect(helpers.isValidString(input)).toBe(false);
    }
  );
});

describe('extractNumericValue', () => {
  it('Given a valid integer string, should return the number string', () => {
    expect(helpers.extractNumericValue('123')).toBe('123');
  });

  it('Given a valid decimal string, should return the number string', () => {
    expect(helpers.extractNumericValue('123.45')).toBe('123.45');
  });

  it('Given a string with a leading number, should extract the number part', () => {
    expect(helpers.extractNumericValue('123xyz')).toBe('123');
  });

  it('Given a string with a leading decimal number, should extract the decimal part', () => {
    expect(helpers.extractNumericValue('123.45xyz')).toBe('123.45');
  });

  it('Given a string with a trailing dot, should include the dot', () => {
    expect(helpers.extractNumericValue('123.')).toBe('123.');
  });

  it('Given a string that does not start with a number, should return undefined', () => {
    expect(helpers.extractNumericValue('abc123')).toBeUndefined();
  });

  it('Given an empty string, should return undefined', () => {
    expect(helpers.extractNumericValue('')).toBeUndefined();
  });

  it('Given null, should return undefined', () => {
    expect(helpers.extractNumericValue(null)).toBeUndefined();
  });

  it('Given undefined, should return undefined', () => {
    expect(helpers.extractNumericValue(undefined)).toBeUndefined();
  });

  it('Given the string "NaN", should return undefined', () => {
    expect(helpers.extractNumericValue('NaN')).toBeUndefined();
  });

  it('Given a string with multiple dots, should extract up to the second dot', () => {
    expect(helpers.extractNumericValue('12.34.56')).toBe('12.34');
  });

  it('Given a string containing only a dot, should return undefined', () => {
    expect(helpers.extractNumericValue('.')).toBeUndefined();
  });
});
