import { z } from 'zod';

export const DEFAULT_SORTING_FIELD_VALUE = 'Any';

export const WidgetFieldTypes = {
  COMMISSION_AMOUNT_COUNT: 'commission_amount_count',
  COMMISSION_AMOUNT_SUM: 'commission_amount_sum',
  COMMISSION_AMOUNT_NEGATIVE_COUNT: 'commission_amount_negative_count',
  COMMISSION_AMOUNT_NEGATIVE_SUM: 'commission_amount_negative_sum',
  COMMISSION_AMOUNT_POSITIVE_COUNT: 'commission_amount_positive_count',
  COMMISSION_AMOUNT_POSITIVE_SUM: 'commission_amount_positive_sum',
} as const;

export type WidgetFieldTypes =
  (typeof WidgetFieldTypes)[keyof typeof WidgetFieldTypes];

export interface DataChart {
  tooltip?: Tooltip;
  series?: Series[];
}

export interface Series {
  center?: string[];
  type?: string;
  radius?: string[];
  avoidLabelOverlap?: boolean;
  itemStyle?: ItemStyle;
  emphasis?: Emphasis;
  labelLine?: LabelLine;
  data?: Datum[];
}

export interface Datum {
  name?: string;
  value?: string;
}

export interface Emphasis {
  label?: Label;
}

export interface Label {
  show?: boolean;
  fontWeight?: string;
}

export interface ItemStyle {
  borderRadius?: number;
  borderColor?: string;
  borderWidth?: number;
}

export interface LabelLine {
  show?: boolean;
}

export interface Tooltip {
  trigger?: string;
  position?: number[];
  formatter?: string;
}

export const ResultFormatterMethod = {
  CURRENCY: 'currency',
  PERCENTAGE: 'percentage',
  NUMBER: 'number',
} as const;

export const ResultFormatterTypeLabels = {
  [ResultFormatterMethod.CURRENCY]: 'Currency',
  [ResultFormatterMethod.PERCENTAGE]: 'Percentage',
  [ResultFormatterMethod.NUMBER]: 'Number',
} as const;

export const AggregationMethodTypes = {
  SUM: 'Sum',
  SUM_ACCUMULATE: 'SumAccumulate',
  AGGREGATE: 'Aggregate',
  AVERAGE: 'Average',
  COUNT: 'Count',
  COUNT_ACCUMULATE: 'CountAccumulate',
} as const;

export type AggregationMethodType =
  (typeof AggregationMethodTypes)[keyof typeof AggregationMethodTypes];

export const AggregationMethodTypeLabels: Record<
  AggregationMethodType,
  string
> = {
  [AggregationMethodTypes.SUM]: 'Sum',
  [AggregationMethodTypes.SUM_ACCUMULATE]: 'Running sum',
  [AggregationMethodTypes.AGGREGATE]: 'Aggregate',
  [AggregationMethodTypes.AVERAGE]: 'Average',
  [AggregationMethodTypes.COUNT]: 'Count',
  [AggregationMethodTypes.COUNT_ACCUMULATE]: 'Running count',
};

export const DEFAULT_FIELD_VALUE = 'any';

export const WIDGET_FIELDS = {
  COMMISSION_AMOUNT_COUNT: 'commission_amount_count',
  COMMISSION_AMOUNT_SUM: 'commission_amount_sum',
  COMMISSION_AMOUNT_NEGATIVE_COUNT: 'commission_amount_negative_count',
  COMMISSION_AMOUNT_NEGATIVE_SUM: 'commission_amount_negative_sum',
  COMMISSION_AMOUNT_POSITIVE_COUNT: 'commission_amount_positive_count',
  COMMISSION_AMOUNT_POSITIVE_SUM: 'commission_amount_positive_sum',
} as const;

export enum ChartType {
  DONUT = 'chart-donut',
  LINE = 'chart-line',
  BAR = 'chart-bar',
  TABLE = 'table',
  BOX = 'box',
}

export const GroupFieldDefinitionSchema = z.object({
  name: z.string(),
  type: z.enum(['string', 'number', 'date']),
  displayName: z.string(),
  uniqueKey: z.string().optional(),
  fieldMatcherType: z.enum(['text', 'number', 'date']).optional(),
  availableValues: z.array(z.string()).optional(),
});

export const DataFieldDefinitionSchema = z.object({
  name: z.string(),
  type: z.enum(['string', 'number', 'date', 'obj', 'object']),
  displayName: z.string(),
  fieldMatcherType: z.enum(['text', 'number', 'date']).optional(),
  defaultFormatter: z.string().optional(),
  lookupKey: z.string().optional(),
});

export type GroupFieldDefinition = z.infer<typeof GroupFieldDefinitionSchema>;
export type DataFieldDefinition = z.infer<typeof DataFieldDefinitionSchema>;

export const DataSourceConfigSchema = z.object({
  groupFields: z.array(GroupFieldDefinitionSchema),
  dataFields: z.array(DataFieldDefinitionSchema),
});

export type DataSourceConfig = z.infer<typeof DataSourceConfigSchema>;

export const filterFieldsByDataSource: Record<string, DataSourceConfig> = {
  commissions: {
    groupFields: [
      {
        name: 'writing_carrier_name',
        type: 'string',
        displayName: 'Writing carrier name',
      },
      {
        name: 'agent_name',
        type: 'string',
        displayName: 'Agent name',
      },
      {
        name: 'contacts',
        type: 'string',
        displayName: 'Agents',
      },
      {
        name: 'customer_name',
        type: 'string',
        displayName: 'Customer name',
      },
      {
        name: 'policy_id',
        type: 'string',
        displayName: 'Policy id',
      },
      {
        name: 'product_type',
        type: 'string',
        displayName: 'Product type',
      },
      {
        name: 'product_name',
        type: 'string',
        displayName: 'Product name',
      },
      {
        name: 'processing_date',
        type: 'date',
        displayName: 'Processing date',
      },
      {
        name: 'compensation_type',
        type: 'string',
        displayName: 'Compensation type',
      },
      {
        name: 'carrier_name',
        type: 'string',
        displayName: 'Carrier name',
      },
      {
        name: 'policies.product_type',
        type: 'string',
        displayName: 'Policies -> Product type',
        fieldMatcherType: 'text',
      },
      {
        name: 'policies.customer_name',
        type: 'string',
        displayName: 'Policies -> Customer name',
        fieldMatcherType: 'text',
      },
      {
        name: 'policies.geo_state',
        type: 'string',
        displayName: 'Policies -> Geo state',
        fieldMatcherType: 'text',
      },
      {
        name: 'policies.premium_amount',
        type: 'string',
        displayName: 'Policies -> Premium amount',
        fieldMatcherType: 'number',
      },
      {
        name: 'policies.writing_carrier_name',
        type: 'string',
        displayName: 'Policies -> Writing carrier name',
        fieldMatcherType: 'text',
      },
      // String fields
      {
        name: 'account_type',
        type: 'string',
        displayName: 'Account type',
      },
      {
        name: 'bill_mode',
        type: 'string',
        displayName: 'Bill mode',
      },
      {
        name: 'carrier_rate',
        type: 'string',
        displayName: 'Carrier rate',
      },
      {
        name: 'commission_basis',
        type: 'string',
        displayName: 'Commission basis',
      },
      {
        name: 'commission_rate',
        type: 'string',
        displayName: 'Commission rate',
      },
      {
        name: 'geo_state',
        type: 'string',
        displayName: 'Geo state',
      },
      {
        name: 'group_name',
        type: 'string',
        displayName: 'Group name',
      },
      {
        name: 'notes',
        type: 'string',
        displayName: 'Notes',
      },
      {
        name: 'payment_mode',
        type: 'string',
        displayName: 'Payment mode',
      },
      {
        name: 'payment_status',
        type: 'string',
        displayName: 'Payment status',
      },
      {
        name: 'premium_type',
        type: 'string',
        displayName: 'Premium type',
      },
      {
        name: 'product_option_name',
        type: 'string',
        displayName: 'Product option name',
      },
      {
        name: 'product_sub_type',
        type: 'string',
        displayName: 'Product sub type',
      },
      {
        name: 'reconciliation_method',
        type: 'string',
        displayName: 'Reconciliation method',
      },
      {
        name: 'reconciliation_status',
        type: 'string',
        displayName: 'Reconciliation status',
      },
      {
        name: 'commissions_number',
        type: 'string',
        displayName: 'Commissions number',
      },
      {
        name: 'status',
        type: 'string',
        displayName: 'Status',
      },
      {
        name: 'type',
        type: 'string',
        displayName: 'Type',
      },
      {
        name: 'reconciliation_stats',
        type: 'string',
        displayName: 'Reconciliation stats',
      },
      {
        name: 'standardized_customer_name',
        type: 'string',
        displayName: 'Standardized customer name',
      },
      {
        name: 'transaction_type',
        type: 'string',
        displayName: 'Transaction type',
      },
      // Date fields
      {
        name: 'effective_date',
        type: 'date',
        displayName: 'Effective date',
      },
      {
        name: 'invoice_date',
        type: 'date',
        displayName: 'Invoice date',
      },
      {
        name: 'payment_date',
        type: 'date',
        displayName: 'Payment date',
      },
      {
        name: 'period_date',
        type: 'date',
        displayName: 'Period date',
      },
      // Number fields
      {
        name: 'issue_age',
        type: 'number',
        displayName: 'Issue age',
      },
      {
        name: 'member_count',
        type: 'number',
        displayName: 'Member count',
      },
      {
        name: 'commission_amount',
        type: 'number',
        displayName: 'Commission amount',
      },
      {
        name: 'commission_paid_amount',
        type: 'number',
        displayName: 'Commission paid amount',
      },
      {
        name: 'commission_rate_percent',
        type: 'number',
        displayName: 'Commission rate percent',
      },
      {
        name: 'commissionable_premium_amount',
        type: 'number',
        displayName: 'Commissionable premium amount',
      },
      {
        name: 'customer_paid_premium_amount',
        type: 'number',
        displayName: 'Customer paid premium amount',
      },
      {
        name: 'fees',
        type: 'number',
        displayName: 'Fees',
      },
      {
        name: 'new_carrier_rate',
        type: 'number',
        displayName: 'New carrier rate',
      },
      {
        name: 'new_commission_rate',
        type: 'number',
        displayName: 'New commission rate',
      },
      {
        name: 'premium_amount',
        type: 'number',
        displayName: 'Premium amount',
      },
      {
        name: 'split_percentage',
        type: 'number',
        displayName: 'Split percentage',
      },
    ],
    dataFields: [
      {
        name: 'commission_amount',
        type: 'number',
        displayName: 'Commission amount',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'agent_commissions.agents_only',
        type: 'number',
        displayName: 'Agent commissions (Agents only)',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'agent_commissions.sales_reps_only',
        type: 'number',
        displayName: 'Agent commissions (Sales reps only)',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'agent_commissions.total',
        type: 'obj',
        lookupKey: 'total',
        displayName: 'Agent commissions total',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'agent_commissions',
        type: 'obj',
        displayName: 'Agent commissions',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'policy_id',
        type: 'string',
        displayName: 'Policy number',
        defaultFormatter: ResultFormatterMethod.NUMBER,
      },
      {
        name: 'premium_amount',
        type: 'number',
        displayName: 'Premium amount',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'policies',
        type: 'object',
        displayName: 'Policy',
        defaultFormatter: ResultFormatterMethod.NUMBER,
      },
      {
        name: 'policies.premium_amount',
        type: 'number',
        displayName: 'Policy -> Premium amount',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      // Number fields
      {
        name: 'commission_paid_amount',
        type: 'number',
        displayName: 'Commission paid amount',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'commission_rate_percent',
        type: 'number',
        displayName: 'Commission rate percent',
        defaultFormatter: ResultFormatterMethod.PERCENTAGE,
      },
      {
        name: 'commissionable_premium_amount',
        type: 'number',
        displayName: 'Commissionable premium amount',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'customer_paid_premium_amount',
        type: 'number',
        displayName: 'Customer paid premium amount',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'fees',
        type: 'number',
        displayName: 'Fees',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'new_carrier_rate',
        type: 'number',
        displayName: 'New carrier rate',
        defaultFormatter: ResultFormatterMethod.PERCENTAGE,
      },
      {
        name: 'new_commission_rate',
        type: 'number',
        displayName: 'New commission rate',
        defaultFormatter: ResultFormatterMethod.PERCENTAGE,
      },
      {
        name: 'split_percentage',
        type: 'number',
        displayName: 'Split percentage',
        defaultFormatter: ResultFormatterMethod.PERCENTAGE,
      },
      {
        name: 'issue_age',
        type: 'number',
        displayName: 'Issue age',
        defaultFormatter: ResultFormatterMethod.NUMBER,
      },
      {
        name: 'member_count',
        type: 'number',
        displayName: 'Member count',
        defaultFormatter: ResultFormatterMethod.NUMBER,
      },
    ],
  },
  policies: {
    groupFields: [
      {
        name: 'effective_date',
        type: 'date',
        displayName: 'Effective date',
      },
      {
        name: 'agent_name',
        type: 'string',
        displayName: 'Agent name',
      },
      {
        name: 'customer_name',
        type: 'string',
        displayName: 'Customer name',
      },
      {
        name: 'product_type',
        type: 'string',
        displayName: 'Product type',
      },
      {
        name: 'product_name',
        type: 'string',
        displayName: 'Product name',
      },
      {
        name: 'writing_carrier_name',
        type: 'string',
        displayName: 'Writing carrier name',
      },
      {
        name: 'product_sub_type',
        type: 'string',
        displayName: 'Product sub type',
      },
      {
        name: 'policy_status',
        type: 'string',
        displayName: 'Policy status',
        availableValues: [
          'Active',
          'Inactive',
          'Active Client',
          'Inactive Client',
          'In Force',
          'Prospect/Lead',
          'duplicate',
        ],
      },
      {
        name: 'state',
        type: 'string',
        displayName: 'State',
        availableValues: ['duplicate', 'active', 'deleted'],
      },
      // Date fields
      {
        name: 'cancellation_date',
        type: 'date',
        displayName: 'Cancellation date',
      },
      {
        name: 'first_payment_date',
        type: 'date',
        displayName: 'First payment date',
      },
      {
        name: 'first_processed_date',
        type: 'date',
        displayName: 'First processed date',
      },
      {
        name: 'policy_date',
        type: 'date',
        displayName: 'Policy date',
      },
      {
        name: 'reinstatement_date',
        type: 'date',
        displayName: 'Reinstatement date',
      },
      {
        name: 'signed_date',
        type: 'date',
        displayName: 'Signed date',
      },
      // String fields
      {
        name: 'account_type',
        type: 'string',
        displayName: 'Account type',
      },
      {
        name: 'dba',
        type: 'string',
        displayName: 'DBA',
      },
      {
        name: 'geo_state',
        type: 'string',
        displayName: 'Geo state',
      },
      {
        name: 'group_name',
        type: 'string',
        displayName: 'Group name',
      },
      {
        name: 'payment_mode',
        type: 'string',
        displayName: 'Payment mode',
      },
      {
        name: 'product_option_name',
        type: 'string',
        displayName: 'Product option name',
      },
      {
        name: 'reconciliation_method',
        type: 'string',
        displayName: 'Reconciliation method',
      },
      {
        name: 'reconciliation_status',
        type: 'string',
        displayName: 'Reconciliation status',
      },
      {
        name: 'transaction_type',
        type: 'string',
        displayName: 'Transaction type',
      },
      {
        name: 'type',
        type: 'string',
        displayName: 'Type',
      },
      {
        name: 'customer_first_name',
        type: 'string',
        displayName: 'Customer first name',
      },
      {
        name: 'customer_last_name',
        type: 'string',
        displayName: 'Customer last name',
      },
      {
        name: 'notes',
        type: 'string',
        displayName: 'Notes',
      },
      // Number fields
      {
        name: 'issue_age',
        type: 'number',
        displayName: 'Issue age',
      },
      {
        name: 'policy_term_months',
        type: 'number',
        displayName: 'Policy term months',
      },
      {
        name: 'split_percentage',
        type: 'number',
        displayName: 'Split percentage',
      },
      {
        name: 'commissionable_premium_amount',
        type: 'number',
        displayName: 'Commissionable premium amount',
      },
      {
        name: 'commissions_expected',
        type: 'number',
        displayName: 'Commissions due',
      },
      {
        name: 'customer_paid_premium_amount',
        type: 'number',
        displayName: 'Customer paid premium amount',
      },
      {
        name: 'premium_amount',
        type: 'number',
        displayName: 'Premium amount',
      },
      {
        name: WIDGET_FIELDS.COMMISSION_AMOUNT_COUNT,
        type: 'number',
        displayName: 'Commission -> Commission amount (count)',
        fieldMatcherType: 'number',
      },
      {
        name: WIDGET_FIELDS.COMMISSION_AMOUNT_SUM,
        type: 'number',
        displayName: 'Commission -> Commission amount (sum)',
        fieldMatcherType: 'number',
      },
      {
        name: WIDGET_FIELDS.COMMISSION_AMOUNT_NEGATIVE_COUNT,
        type: 'number',
        displayName: 'Commission -> Negative commission amount (count)',
        fieldMatcherType: 'number',
      },
      {
        name: WIDGET_FIELDS.COMMISSION_AMOUNT_NEGATIVE_SUM,
        type: 'number',
        displayName: 'Commission -> Negative commission amount (sum)',
        fieldMatcherType: 'number',
      },
      {
        name: WIDGET_FIELDS.COMMISSION_AMOUNT_POSITIVE_COUNT,
        type: 'number',
        displayName: 'Commission -> Positive commission amount (count)',
        fieldMatcherType: 'number',
      },
      {
        name: WIDGET_FIELDS.COMMISSION_AMOUNT_POSITIVE_SUM,
        type: 'number',
        displayName: 'Commission -> Positive commission amount (sum)',
        fieldMatcherType: 'number',
      },
    ],
    dataFields: [
      {
        name: 'commissionable_premium_amount',
        type: 'number',
        displayName: 'Commissionable premium amount',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'premium_amount',
        type: 'number',
        displayName: 'Premium amount',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'commissions_expected',
        type: 'number',
        displayName: 'Commissions due',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'customer_paid_premium_amount',
        type: 'number',
        displayName: 'Customer paid premium amount',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'split_percentage',
        type: 'number',
        displayName: 'Split percentage',
        defaultFormatter: ResultFormatterMethod.PERCENTAGE,
      },
      {
        name: 'issue_age',
        type: 'number',
        displayName: 'Issue age',
        defaultFormatter: ResultFormatterMethod.NUMBER,
      },
      {
        name: 'policy_term_months',
        type: 'number',
        displayName: 'Policy term months',
        defaultFormatter: ResultFormatterMethod.NUMBER,
      },
      {
        name: 'product type',
        type: 'string',
        displayName: 'Product type',
        fieldMatcherType: 'text',
      },
      {
        name: WIDGET_FIELDS.COMMISSION_AMOUNT_COUNT,
        type: 'number',
        displayName: 'Commission -> Commission amount (count)',
        defaultFormatter: ResultFormatterMethod.NUMBER,
      },
      {
        name: WIDGET_FIELDS.COMMISSION_AMOUNT_SUM,
        type: 'number',
        displayName: 'Commission -> Commission amount (sum)',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: WIDGET_FIELDS.COMMISSION_AMOUNT_NEGATIVE_COUNT,
        type: 'number',
        displayName: 'Commission -> Negative commission amount (count)',
        defaultFormatter: ResultFormatterMethod.NUMBER,
      },
      {
        name: WIDGET_FIELDS.COMMISSION_AMOUNT_NEGATIVE_SUM,
        type: 'number',
        displayName: 'Commission -> Negative commission amount (sum)',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: WIDGET_FIELDS.COMMISSION_AMOUNT_POSITIVE_COUNT,
        type: 'number',
        displayName: 'Commission -> Positive commission amount (count)',
        defaultFormatter: ResultFormatterMethod.NUMBER,
      },
      {
        name: WIDGET_FIELDS.COMMISSION_AMOUNT_POSITIVE_SUM,
        type: 'number',
        displayName: 'Commission -> Positive commission amount (sum)',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
    ],
  },
  agentPayouts: {
    groupFields: [
      {
        name: 'contact_name',
        uniqueKey: 'contact_id',
        type: 'string',
        displayName: 'Contact',
        fieldMatcherType: 'text',
      },
      {
        name: 'date_ymd',
        type: 'date',
        displayName: 'Date',
        fieldMatcherType: 'date',
      },
      {
        name: 'notes',
        type: 'string',
        displayName: 'Notes',
        fieldMatcherType: 'text',
      },
      {
        name: 'status',
        type: 'string',
        displayName: 'Status',
        fieldMatcherType: 'text',
      },
      {
        name: 'type',
        type: 'string',
        displayName: 'Type',
        fieldMatcherType: 'text',
      },
    ],
    dataFields: [
      {
        name: 'amount',
        type: 'number',
        displayName: 'Amount',
        fieldMatcherType: 'number',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
    ],
  },
  contacts: {
    groupFields: [
      {
        name: 'name',
        type: 'string',
        displayName: 'Name',
        uniqueKey: 'id',
        fieldMatcherType: 'text',
      },
      // String fields
      {
        name: 'agent_code',
        type: 'string',
        displayName: 'Agent code',
        fieldMatcherType: 'text',
      },
      {
        name: 'bank_info',
        type: 'string',
        displayName: 'Bank info',
        fieldMatcherType: 'text',
      },
      {
        name: 'city',
        type: 'string',
        displayName: 'City',
        fieldMatcherType: 'text',
      },
      {
        name: 'company_name',
        type: 'string',
        displayName: 'Company name',
        fieldMatcherType: 'text',
      },
      {
        name: 'country',
        type: 'string',
        displayName: 'Country',
        fieldMatcherType: 'text',
      },
      {
        name: 'email',
        type: 'string',
        displayName: 'Email',
        fieldMatcherType: 'text',
      },
      {
        name: 'first_name',
        type: 'string',
        displayName: 'First name',
        fieldMatcherType: 'text',
      },
      {
        name: 'gender',
        type: 'string',
        displayName: 'Gender',
        fieldMatcherType: 'text',
      },
      {
        name: 'geo_state',
        type: 'string',
        displayName: 'Geo state',
        fieldMatcherType: 'text',
      },
      {
        name: 'last_name',
        type: 'string',
        displayName: 'Last name',
        fieldMatcherType: 'text',
      },
      {
        name: 'level',
        type: 'string',
        displayName: 'Level',
        fieldMatcherType: 'text',
      },
      {
        name: 'middle_name',
        type: 'string',
        displayName: 'Middle name',
        fieldMatcherType: 'text',
      },
      {
        name: 'nickname',
        type: 'string',
        displayName: 'Nickname',
        fieldMatcherType: 'text',
      },
      {
        name: 'notes',
        type: 'string',
        displayName: 'Notes',
        fieldMatcherType: 'text',
      },
      {
        name: 'payable_status',
        type: 'string',
        displayName: 'Payable status',
        fieldMatcherType: 'text',
      },
      {
        name: 'phone',
        type: 'string',
        displayName: 'Phone',
        fieldMatcherType: 'text',
      },
      {
        name: 'phone_type',
        type: 'string',
        displayName: 'Phone type',
        fieldMatcherType: 'text',
      },
      {
        name: 'phone2',
        type: 'string',
        displayName: 'Phone 2',
        fieldMatcherType: 'text',
      },
      {
        name: 'phone2_type',
        type: 'string',
        displayName: 'Phone 2 type',
        fieldMatcherType: 'text',
      },
      {
        name: 'status',
        type: 'string',
        displayName: 'Status',
        fieldMatcherType: 'text',
      },
      {
        name: 'title',
        type: 'string',
        displayName: 'Title',
        fieldMatcherType: 'text',
      },
      {
        name: 'type',
        type: 'string',
        displayName: 'Type',
        fieldMatcherType: 'text',
      },
      {
        name: 'zip',
        type: 'string',
        displayName: 'Zip',
        fieldMatcherType: 'text',
      },

      // Date fields
      {
        name: 'birthday',
        type: 'date',
        displayName: 'Birthday',
        fieldMatcherType: 'date',
      },
      // Number fields
      {
        name: 'balance',
        type: 'number',
        displayName: 'Balance',
        fieldMatcherType: 'number',
      },
    ],
    dataFields: [
      {
        name: 'balance',
        type: 'number',
        displayName: 'Agents balance',
        fieldMatcherType: 'number',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
    ],
  },
  customers: {
    groupFields: [
      {
        name: 'first_name',
        type: 'string',
        displayName: 'First name',
        fieldMatcherType: 'text',
      },
      {
        name: 'middle_name',
        type: 'string',
        displayName: 'Middle name',
        fieldMatcherType: 'text',
      },
      {
        name: 'last_name',
        type: 'string',
        displayName: 'Last name',
        fieldMatcherType: 'text',
      },
      {
        name: 'company_name',
        type: 'string',
        displayName: 'Company name',
        fieldMatcherType: 'text',
      },
      {
        name: 'nickname',
        type: 'string',
        displayName: 'Nickname',
        fieldMatcherType: 'text',
      },
      {
        name: 'email',
        type: 'string',
        displayName: 'Email',
        fieldMatcherType: 'text',
      },
      {
        name: 'phone',
        type: 'string',
        displayName: 'Phone',
        fieldMatcherType: 'text',
      },
      {
        name: 'website',
        type: 'string',
        displayName: 'Website',
        fieldMatcherType: 'text',
      },
      {
        name: 'dob',
        type: 'date',
        displayName: 'Date of birth',
        fieldMatcherType: 'date',
      },
      {
        name: 'gender',
        type: 'string',
        displayName: 'Gender',
        fieldMatcherType: 'text',
      },
      {
        name: 'type',
        type: 'string',
        displayName: 'Customer type',
        fieldMatcherType: 'text',
      },
      {
        name: 'status',
        type: 'string',
        displayName: 'Status',
        fieldMatcherType: 'text',
      },
      {
        name: 'start_date',
        type: 'date',
        displayName: 'Start date',
        fieldMatcherType: 'date',
      },
      {
        name: 'end_date',
        type: 'date',
        displayName: 'End date',
        fieldMatcherType: 'date',
      },
    ],
    dataFields: [
      {
        name: 'contacts_balance',
        type: 'number',
        displayName: 'Contacts balance',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'accounting_transaction_details_amount',
        type: 'number',
        displayName: 'Accounting transaction details amount',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'policies_data_commissionable_premium_amount',
        type: 'number',
        displayName: 'Policies commissionable premium amount',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'policies_data_premium_amount',
        type: 'number',
        displayName: 'Policies premium amount',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'policies_data_commissions_expected',
        type: 'number',
        displayName: 'Policies commissions due',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'policies_data_customer_paid_premium_amount',
        type: 'number',
        displayName: 'Policies customer paid premium amount',
        defaultFormatter: ResultFormatterMethod.CURRENCY,
      },
      {
        name: 'policies_data_split_percentage',
        type: 'number',
        displayName: 'Policies split percentage',
        defaultFormatter: ResultFormatterMethod.PERCENTAGE,
      },
      {
        name: 'policies_data_issue_age',
        type: 'number',
        displayName: 'Policies issue age',
        defaultFormatter: ResultFormatterMethod.NUMBER,
      },
      {
        name: 'policies_data_policy_term_months',
        type: 'number',
        displayName: 'Policies policy term months',
        defaultFormatter: ResultFormatterMethod.NUMBER,
      },
    ],
  },
};

const commissionsGroupFieldNames =
  filterFieldsByDataSource.commissions.groupFields.map((f) => f.name) as [
    string,
    ...string[],
  ];
const policiesGroupFieldNames =
  filterFieldsByDataSource.policies.groupFields.map((f) => f.name) as [
    string,
    ...string[],
  ];
const commissionsDataFieldNames =
  filterFieldsByDataSource.commissions.dataFields.map((f) => f.name) as [
    string,
    ...string[],
  ];
const policiesDataFieldNames = filterFieldsByDataSource.policies.dataFields.map(
  (f) => f.name
) as [string, ...string[]];

export const CommissionsGroupByFieldEnum = z.enum(commissionsGroupFieldNames);
export type CommissionsGroupByField = z.infer<
  typeof CommissionsGroupByFieldEnum
>;

export const PoliciesGroupByFieldEnum = z.enum(policiesGroupFieldNames);
export type PoliciesGroupByField = z.infer<typeof PoliciesGroupByFieldEnum>;

export const CommissionsDataFieldEnum = z.enum(commissionsDataFieldNames);
export type CommissionsDataField = z.infer<typeof CommissionsDataFieldEnum>;

export const PoliciesDataFieldEnum = z.enum(policiesDataFieldNames);

export const AggregationSelectorSchema = z.object({
  id: z.number().optional(),
  field: z.string(),
  formatter: z.enum(['number', 'currency', 'percent']),
  aggregation_method: z.enum([
    AggregationMethodTypes.COUNT,
    AggregationMethodTypes.SUM,
    AggregationMethodTypes.AVERAGE,
    AggregationMethodTypes.COUNT_ACCUMULATE,
    AggregationMethodTypes.SUM_ACCUMULATE,
  ]),
});

export const FilterSchema = z.object({
  field: z.string(),
  value: z.string().optional(),
  op: z.string(),
  // op: z.enum(['equals', 'not_equals', 'greater_than', 'less_than']),
  number: z.string().optional(),
  unit: z.string().optional(),
  from: z.string().optional(),
  caseSensitive: z.boolean().optional(),
  skipEmpty: z.boolean().optional(),
});

export const WidgetDataSources = {
  COMMISSIONS: 'commissions',
  POLICIES: 'policies',
  AGENT_PAYOUTS: 'agentPayouts',
  CONTACTS: 'contacts',
  CUSTOMERS: 'customers',
} as const;

export type WidgetDataSources =
  (typeof WidgetDataSources)[keyof typeof WidgetDataSources];

export const WidgetDataSourceLabels: Record<WidgetDataSources, string> = {
  [WidgetDataSources.COMMISSIONS]: 'Commissions',
  [WidgetDataSources.POLICIES]: 'Policies',
  [WidgetDataSources.AGENT_PAYOUTS]: 'Agent payouts',
  [WidgetDataSources.CONTACTS]: 'Contacts',
  [WidgetDataSources.CUSTOMERS]: 'Customers',
};

export const WidgetDataSourceEnum = z.enum([
  WidgetDataSources.COMMISSIONS,
  WidgetDataSources.POLICIES,
  WidgetDataSources.AGENT_PAYOUTS,
  WidgetDataSources.CONTACTS,
  WidgetDataSources.CUSTOMERS,
]);

export const CommissionsFilterByDateFields = {
  PROCESSING_DATE: 'processing_date',
  EFFECTIVE_DATE: 'effective_date',
  INVOICE_DATE: 'invoice_date',
  PAYMENT_DATE: 'payment_date',
  NONE: 'none',
} as const;

export type CommissionsFilterByDateFields =
  (typeof CommissionsFilterByDateFields)[keyof typeof CommissionsFilterByDateFields];

export const PoliciesFilterByDateFields = {
  EFFECTIVE_DATE: 'effective_date',
  INVOICE_DATE: 'invoice_date',
  PAYMENT_DATE: 'payment_date',
  PERIOD_DATE: 'period_date',
  CANCELLATION_DATE: 'cancellation_date',
  FIRST_PAYMENT_DATE: 'first_payment_date',
  FIRST_PROCESSED_DATE: 'first_processed_date',
  POLICY_DATE: 'policy_date',
  REINSTATEMENT_DATE: 'reinstatement_date',
  SIGNED_DATE: 'signed_date',
  COMMISSION_PROCESSING_DATE: 'commission_processing_date',
  NONE: 'none',
} as const;

export type PoliciesFilterByDateFields =
  (typeof PoliciesFilterByDateFields)[keyof typeof PoliciesFilterByDateFields];

export const CommissionsFilterByDateFieldLabels: Record<
  CommissionsFilterByDateFields,
  string
> = {
  [CommissionsFilterByDateFields.PROCESSING_DATE]: 'Processing date',
  [CommissionsFilterByDateFields.EFFECTIVE_DATE]: 'Effective date',
  [CommissionsFilterByDateFields.INVOICE_DATE]: 'Invoice date',
  [CommissionsFilterByDateFields.PAYMENT_DATE]: 'Payment date',
  [CommissionsFilterByDateFields.NONE]: 'None',
} as const;

export const PoliciesFilterByDateFieldLabels: Record<
  PoliciesFilterByDateFields,
  string
> = {
  [PoliciesFilterByDateFields.EFFECTIVE_DATE]: 'Effective date',
  [PoliciesFilterByDateFields.INVOICE_DATE]: 'Invoice date',
  [PoliciesFilterByDateFields.PAYMENT_DATE]: 'Payment date',
  [PoliciesFilterByDateFields.PERIOD_DATE]: 'Period date',
  [PoliciesFilterByDateFields.CANCELLATION_DATE]: 'Cancellation date',
  [PoliciesFilterByDateFields.FIRST_PAYMENT_DATE]: 'First payment date',
  [PoliciesFilterByDateFields.FIRST_PROCESSED_DATE]: 'First processed date',
  [PoliciesFilterByDateFields.POLICY_DATE]: 'Policy date',
  [PoliciesFilterByDateFields.REINSTATEMENT_DATE]: 'Reinstatement date',
  [PoliciesFilterByDateFields.SIGNED_DATE]: 'Signed date',
  [PoliciesFilterByDateFields.COMMISSION_PROCESSING_DATE]:
    'Commission processing date',
  [PoliciesFilterByDateFields.NONE]: 'None',
} as const;

export const WidgetFilterByDateFieldLabels: Record<string, string> = {
  effective_date: 'Effective date',
  invoice_date: 'Invoice date',
  payment_date: 'Payment date',
  period_date: 'Period date',
  cancellation_date: 'Cancellation date',
  first_payment_date: 'First payment date',
  first_processed_date: 'First processed date',
  policy_date: 'Policy date',
  reinstatement_date: 'reinstatement date',
  signed_date: 'Signed date',
  commission_processing_date: 'Commission processing date',
  processing_date: 'Processing date',
  none: 'None',
} as const;

export const WidgetFilterByDateCommissionsEnum = z.enum(
  Object.values(CommissionsFilterByDateFields) as [string, ...string[]]
);

export const WidgetFilterByDatePoliciesEnum = z.enum(
  Object.values(PoliciesFilterByDateFields) as [string, ...string[]]
);

export const WidgetTypes = {
  BOX: 'box',
  CHART_DONUT: 'chart-donut',
  CHART_LINE: 'chart-line',
  CHART_BAR: 'chart-bar',
  TABLE: 'table',
} as const;

export type WidgetTypes = (typeof WidgetTypes)[keyof typeof WidgetTypes];

export const WidgetTypeLabels: Record<WidgetTypes, string> = {
  [WidgetTypes.BOX]: 'Box',
  [WidgetTypes.CHART_DONUT]: 'Donut chart',
  [WidgetTypes.CHART_LINE]: 'Line chart',
  [WidgetTypes.CHART_BAR]: 'Bar chart',
  [WidgetTypes.TABLE]: 'Table',
} as const;

export const WidgetTimePeriods = {
  DAY: 'day',
  WEEK: 'week',
  MONTH: 'month',
  YEAR: 'year',
};

export type WidgetTimePeriods =
  (typeof WidgetTimePeriods)[keyof typeof WidgetTimePeriods];

export const WidgetTimePeriodLabels: Record<WidgetTimePeriods, string> = {
  day: 'Day',
  week: 'Week',
  month: 'Month',
  year: 'Year',
} as const;

export const WidgetTypeEnum = z.enum(
  Object.values(WidgetTypes) as [string, ...string[]]
);
export type WidgetType = z.infer<typeof WidgetTypeEnum>;

export const WidgetTimePeriodEnum = z.enum(
  Object.values(WidgetTimePeriods) as [string, ...string[]]
);
export type WidgetTimePeriod = z.infer<typeof WidgetTimePeriodEnum>;

export const WidgetAccessTypes = {
  GLOBAL: 'global',
  ACCOUNT: 'account',
  USER: 'user',
  USERLIST: 'userlist',
} as const;

export type WidgetAccessTypes =
  (typeof WidgetAccessTypes)[keyof typeof WidgetAccessTypes];

export const WidgetAccessTypeLabels: Record<WidgetAccessTypes, string> = {
  [WidgetAccessTypes.GLOBAL]: 'Global',
  [WidgetAccessTypes.ACCOUNT]: 'Account',
  [WidgetAccessTypes.USER]: 'User',
  [WidgetAccessTypes.USERLIST]: 'User List',
} as const;

export const WidgetAccessEnum = z.enum(
  Object.values(WidgetAccessTypes) as [string, ...string[]]
);
export type WidgetAccess = z.infer<typeof WidgetAccessEnum>;

const BaseWidgetDefinitionSchema = z.object({
  name: z.string().min(1, 'Widget name is required'),
  type: WidgetTypeEnum,
  groupBy: z.string().optional(),
  similarityGroupBy: z.boolean(),
  filters: z.array(FilterSchema),
  timePeriod: WidgetTimePeriodEnum.optional(),
  column: z.string().optional(),
  sortingField: z.any().nullable().optional(),
  columnLimit: z.number().nullable().optional(),
  aggregationSelectors: z
    .array(AggregationSelectorSchema)
    .min(1, 'At least one aggregation selector is required'),
  dataFieldsExpression: z.string().nullable().optional(),
  customCode: z.string().optional(),
  access: WidgetAccessEnum.optional(),
  accessRoles: z.array(z.string()).optional(),
  dataSource: WidgetDataSourceEnum,
  resultFormatter: z.enum(['currency', 'percentage']).optional(),
});

const CommissionsWidgetDefinitionSchema = BaseWidgetDefinitionSchema.extend({
  dataSource: z.literal('commissions'),
  filterByDate: WidgetFilterByDateCommissionsEnum,
});

const PoliciesWidgetDefinitionSchema = BaseWidgetDefinitionSchema.extend({
  dataSource: z.literal('policies'),
  filterByDate: WidgetFilterByDatePoliciesEnum,
});

const OtherDataSourceWidgetDefinitionSchema = BaseWidgetDefinitionSchema.extend(
  {
    dataSource: z.enum(['agentPayouts', 'contacts', 'customers']),
    filterByDate: z.enum(['none']),
  }
);

export const WidgetDefinitionSchema = z.discriminatedUnion('dataSource', [
  CommissionsWidgetDefinitionSchema,
  PoliciesWidgetDefinitionSchema,
  OtherDataSourceWidgetDefinitionSchema,
]);

export type WidgetDefinition = z.infer<typeof WidgetDefinitionSchema>;
