import { Expose } from 'class-transformer';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateCalculationMethodDTO {
  @Expose()
  @IsString()
  method: string;

  @Expose()
  @IsString()
  name: string;

  @Expose()
  @IsString()
  @IsOptional()
  formula?: string;
}

export class ListDTO {
  @Expose()
  @IsNumber()
  @IsOptional()
  page?: number;

  @Expose()
  @IsNumber()
  @IsOptional()
  limit?: number;

  @Expose()
  @IsOptional()
  @IsString()
  orderBy?: string;

  @Expose()
  @IsOptional()
  @IsString()
  sort?: string;
}
export class UpdateCalculationMethodDTO extends CreateCalculationMethodDTO {
  @Expose()
  id: number;
}

export class DeleteDTO {
  @Expose()
  @IsNumber({}, { each: true })
  ids: number[];
}
