import { toArray } from 'common/helpers';
import dayjs from 'dayjs';
import { preprocessBoolean } from 'common/helpers/zod';
import { z } from 'zod';
import { SortOrder } from 'common/globalTypes';

const toDateString = (value) => (dayjs(value).isValid() ? value : undefined);

const stringOrArray = z.union([z.string(), z.array(z.string())]);
const numberOrArray = z.union([z.number(), z.array(z.number())]);

export const StatementDataQueryDtoSchema = z
  .object({
    group_name: stringOrArray.optional().nullable(),
    agent_name: stringOrArray.optional().nullable(),
    disableFilter: preprocessBoolean(z.boolean())
      .optional()
      .nullable()
      .default(false),
    compensation_type: stringOrArray.optional().nullable(),
    contacts: stringOrArray.optional().nullable(),
    carrier_name: stringOrArray.optional().nullable(),
    flags: stringOrArray
      .optional()
      .nullable()
      .transform((value) => {
        if (value === null || value === undefined) {
          return [];
        }
        return Array.isArray(value) ? value : [value];
      }),
    id: z.string().optional().nullable(),
    incl_dupes: preprocessBoolean(z.boolean()).optional().nullable(),
    incl_linked: preprocessBoolean(z.boolean()).optional().nullable(),
    incl_zero_commissions: preprocessBoolean(z.boolean()).optional().nullable(),
    incl_zero_split: preprocessBoolean(z.boolean()).optional().nullable(),
    hide_no_payout_calc_commissions: preprocessBoolean(z.boolean())
      .optional()
      .nullable(),
    hide_payout_calc_commissions: preprocessBoolean(z.boolean())
      .optional()
      .nullable(),
    show_allocated_commissions: preprocessBoolean(z.boolean())
      .optional()
      .nullable(),
    transaction_type: stringOrArray.optional().nullable(),
    limit: z
      .union([z.number(), z.string().transform((val) => parseInt(val))])
      .optional()
      .nullable(),
    orderBy: z.string().optional().nullable().default('created_at'),
    page: z
      .union([z.number(), z.string().transform((val) => parseInt(val))])
      .optional()
      .nullable(),
    payment_date_empty: preprocessBoolean(z.boolean()).optional().nullable(),
    payment_date_end: z.string().optional().nullable().transform(toDateString),
    payment_date_start: z
      .string()
      .optional()
      .nullable()
      .transform(toDateString),
    payment_status: stringOrArray.optional().nullable(),
    processing_date_empty: preprocessBoolean(z.boolean()).optional().nullable(),
    processing_date_end: z
      .string()
      .optional()
      .nullable()
      .transform(toDateString),
    processing_date_start: z
      .string()
      .optional()
      .nullable()
      .transform(toDateString),
    invoice_date_empty: preprocessBoolean(z.boolean()).optional().nullable(),
    invoice_date_end: z.string().optional().nullable().transform(toDateString),
    invoice_date_start: z
      .string()
      .optional()
      .nullable()
      .transform(toDateString),
    effective_date_empty: preprocessBoolean(z.boolean()).optional().nullable(),
    effective_date_end: z
      .string()
      .optional()
      .nullable()
      .transform(toDateString),
    effective_date_start: z
      .string()
      .optional()
      .nullable()
      .transform(toDateString),
    product_name: stringOrArray.optional().nullable(),
    product_type: stringOrArray.optional().nullable(),
    q: z.string().optional().nullable(),
    sort: z
      .enum([SortOrder.ASC, SortOrder.DESC])
      .optional()
      .nullable()
      .default(SortOrder.DESC),
    status: stringOrArray.optional().nullable(),
    tags: stringOrArray.optional().nullable(),
    writing_carrier_name: stringOrArray.optional().nullable(),
    premium_type: stringOrArray.optional().nullable(),
    producer_view: preprocessBoolean(z.boolean())
      .optional()
      .nullable()
      .default(false),
    agent_commissions: z.string().optional().nullable(),
    document_id: stringOrArray.optional().nullable(),
    account_type: stringOrArray.optional().nullable(),
    agent_commissions_status: stringOrArray.optional().nullable(),
    agent_commissions_status2: stringOrArray.optional().nullable(),
    comp_calc_status: stringOrArray.optional().nullable(),
    is_commission_report: preprocessBoolean(z.boolean())
      .optional()
      .nullable()
      .default(false),
    import_id: stringOrArray.optional().nullable(),
    report_data_id: stringOrArray.optional().nullable(),
    comp_report_id: z.string().optional().nullable(),
    statement_data_ids: numberOrArray.optional().nullable(),
    isExportRelationship: z.coerce.boolean().optional().nullable(),
    report_processor: z.string().optional().nullable(),
    is_saved_report: z.string().optional().nullable(),
    json_only: preprocessBoolean(z.boolean()).optional().nullable(),
    virtual_type: stringOrArray.optional().nullable(),
  })
  .transform((data) => {
    // Transform incoming string arrays to actual arrays
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    Object.keys(data).forEach((key) => {
      if (
        typeof data[key] === 'string' &&
        [
          'agent_name',
          'compensation_type',
          'contacts',
          'carrier_name',
          'payment_status',
          'product_name',
          'product_type',
          'transaction_type',
          'status',
          'tags',
          'writing_carrier_name',
          'premium_type',
          'document_id',
          'account_type',
          'agent_commissions_status',
          'agent_commissions_status2',
          'comp_calc_status',
          'import_id',
          'report_data_id',
        ].includes(key)
      ) {
        data[key] = toArray(data[key]);
      }
    });
    return data;
  });

export type StatementDataQueryDto = z.infer<typeof StatementDataQueryDtoSchema>;

export const StatementDataRawCreateDTOSchema = z.object({
  document_id: z.number(),
  invoice_month: z.coerce.date().optional().nullable(),
  payment_month: z.coerce.date().optional().nullable(),
  carrier: z.string().optional().nullable(),
  carrier_id: z.number().optional().nullable(),
  file_name: z.string().optional().nullable(),
  statement_amount: z.number().optional().nullable(),
  statement_date: z.coerce.date().optional().nullable(),
  statement_type: z.string().optional().nullable(),
  policy_number: z.string().optional().nullable(),
  insured: z.string().optional().nullable(),
  commission_amount: z.number().optional().nullable(),
  premium_amount: z.number().optional().nullable(),
  transaction_type: z.string().optional().nullable(),
  compensation_type: z.string().optional().nullable(),
  writing_agent: z.string().optional().nullable(),
  agency: z.string().optional().nullable(),
  payment_method: z.string().optional().nullable(),
  batch_id: z.string().optional().nullable(),
  source: z.string().optional().nullable(),
  uid: z.string().optional().nullable(),
  account_id: z.string().optional().nullable(),
  transaction_date: z.coerce.date().optional().nullable(),
  policy_effective_date: z.coerce.date().optional().nullable(),
  premium_mode: z.string().optional().nullable(),
  commission_rate: z.number().optional().nullable(),
  plan_type: z.string().optional().nullable(),
  product: z.string().optional().nullable(),
  insured_type: z.string().optional().nullable(),
  number_of_lives: z.number().optional().nullable(),
  policy_status: z.string().optional().nullable(),
  transaction_id: z.string().optional().nullable(),
  line_of_business: z.string().optional().nullable(),
});

export type StatementDataRawCreateDTO = z.infer<
  typeof StatementDataRawCreateDTOSchema
>;

export const StatementDataRawUpdateDTOSchema =
  StatementDataRawCreateDTOSchema.extend({
    id: z.number(),
    statement_data_id: z.number().optional().nullable(),
    updated_by: z.string().optional().nullable(),
    updated_proxied_by: z.string().optional().nullable(),
  });

export type StatementDataRawUpdateDTO = z.infer<
  typeof StatementDataRawUpdateDTOSchema
>;

export const StatementDataCreateDTOSchema = z.object({
  statement_data_raw_id: z.number().optional().nullable(),
  document_id: z.number(),
  policy_id: z.number().optional().nullable(),
  insured_id: z.number().optional().nullable(),
  policy_effective_date: z.coerce.date().optional().nullable(),
  policy_number: z.string().optional().nullable(),
  insured: z.string().optional().nullable(),
  agent_id: z.number().optional().nullable(),
  agent_name: z.string().optional().nullable(),
  transaction_date: z.coerce.date().optional().nullable(),
  transaction_type: z.string().optional().nullable(),
  transaction_id: z.string().optional().nullable(),
  compensation_type: z.string().optional().nullable(),
  advanced_commission_amount: z.number().optional().nullable(),
  commission_amount: z.number().optional().nullable(),
  premium_amount: z.number().optional().nullable(),
  premium_mode: z.string().optional().nullable(),
  commission_rate: z.number().optional().nullable(),
  plan_type: z.string().optional().nullable(),
  product: z.string().optional().nullable(),
  insured_type: z.string().optional().nullable(),
  number_of_lives: z.number().optional().nullable(),
  policy_status: z.string().optional().nullable(),
  carrier_id: z.number().optional().nullable(),
  carrier: z.string().optional().nullable(),
  line_of_business: z.string().optional().nullable(),
  invoice_month: z.coerce.date().optional().nullable(),
  payment_month: z.coerce.date().optional().nullable(),
  statement_amount: z.number().optional().nullable(),
  statement_date: z.coerce.date().optional().nullable(),
  statement_type: z.string().optional().nullable(),
  payment_method: z.string().optional().nullable(),
  batch_id: z.string().optional().nullable(),
  source: z.string().optional().nullable(),
  account_id: z.string().optional().nullable(),
  uid: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
});

export type StatementDataCreateDTO = z.infer<
  typeof StatementDataCreateDTOSchema
>;

export const StatementDataUpdateDTOSchema = StatementDataCreateDTOSchema.extend(
  {
    id: z.number(),
    updated_by: z.string().optional().nullable(),
    updated_proxied_by: z.string().optional().nullable(),
  }
);

export type StatementDataUpdateDTO = z.infer<
  typeof StatementDataUpdateDTOSchema
>;

export const GenerateScheduleSchema = z.object({
  report_id: z.number().min(1).nullable(),
});

export type GenerateScheduleDTO = z.infer<typeof GenerateScheduleSchema>;

export const StopScheduleSchema = z.object({
  report_id: z.number().min(1).nullable(),
});

export type StopScheduleDTO = z.infer<typeof StopScheduleSchema>;
