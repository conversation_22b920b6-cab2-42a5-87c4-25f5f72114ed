import { IsArray, IsBoolean, IsString } from 'class-validator';
import { Expose, Transform } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { z } from 'zod';

import { States } from '../../helpers/states';
import { dayjs } from '../../helpers/datetime';

const toArray = ({ value }) =>
  Array.isArray(value) ? value : value ? [value] : undefined;

export class ReportDataQueryDto {
  @Expose()
  @Transform(toArray)
  @IsOptional()
  @IsString({ each: true })
  group_name?: string[];

  @Expose()
  @Transform(toArray)
  @IsOptional()
  @IsString({ each: true })
  transaction_type?: string[];

  @Expose()
  @IsOptional()
  @IsBoolean()
  disableFilter?: boolean;

  @Expose()
  @IsOptional()
  @IsString({ each: true })
  agent_name?: string[];

  @Expose()
  @IsOptional()
  @IsString({ each: true })
  contacts?: string[] | string;

  @Expose()
  @IsOptional()
  @IsString({ each: true })
  document_id?: string | string[];

  @Expose()
  @IsOptional()
  @IsString()
  import_id?: string;

  @Expose()
  @IsOptional()
  @IsString()
  effective_date_end?: string;

  @Expose()
  @IsOptional()
  @IsString()
  effective_date_start?: string;

  @Expose()
  @IsOptional()
  @IsString()
  id?: string;

  @Expose()
  @IsOptional()
  @IsBoolean()
  incl_dupes?: boolean;

  @Expose()
  @IsOptional()
  @IsBoolean()
  incl_linked?: boolean;

  @Expose()
  @IsOptional()
  @Transform(({ value }) =>
    value ? (Array.isArray(value) ? value[0] : value) : undefined
  )
  limit?: number;

  @Expose()
  @IsOptional()
  @IsString()
  orderBy?: string;

  @Expose()
  @IsOptional()
  @Transform(({ value }) =>
    value ? (Array.isArray(value) ? value[0] : value) : undefined
  )
  page?: number;

  @Expose()
  @IsOptional()
  @Transform(toArray)
  @IsArray()
  tags?: string[];

  @Expose()
  @IsOptional()
  @IsString({ each: true })
  policy_status?: string[];

  @Expose()
  @IsOptional()
  @IsBoolean()
  producer_view?: boolean;

  @Expose()
  @IsOptional()
  @IsString({ each: true })
  product_name?: string[];

  @Expose()
  @IsOptional()
  @IsString({ each: true })
  product_type?: string[];

  @Expose()
  @IsOptional()
  @IsString()
  q?: string;

  @Expose()
  @IsOptional()
  @IsString()
  sort?: string;

  @Expose()
  @IsOptional()
  @IsString({ each: true })
  writing_carrier_name?: string[];

  @Expose()
  @IsOptional()
  @IsString({ each: true })
  account_type?: string[];

  @Expose()
  @IsOptional()
  @IsString()
  report_processor?: string;

  @Expose()
  @IsOptional()
  @IsBoolean()
  isExportRelationship?: boolean;

  @Expose()
  @IsOptional()
  @IsString()
  is_saved_report?: string;

  @Expose()
  @IsOptional()
  @IsBoolean()
  json_only?: boolean;
}

const toNumber = (val: string | number) =>
  typeof val === 'string' ? parseInt(val, 10) : val;

const ReportDataBaseSchema = z.object({
  policy_id: z.string().optional(),
  writing_carrier_name: z.string().optional(),
  customer_name: z.string().optional(),
  effective_date: z
    .string()
    .optional()
    .refine((date) => dayjs(date).isValid(), {
      message: 'Invalid date format',
    }),
  product_type: z.string().optional(),
  product_name: z.string().optional(),
  agent_name: z.string().optional(),
  commissionable_premium_amount: z
    .union([z.string(), z.number()])
    .transform(toNumber)
    .optional(),
  policy_status: z.string().optional(),
  issue_age: z.union([z.string(), z.number()]).transform(toNumber).optional(),

  geo_state: z
    .string()
    .transform((val) => val?.toUpperCase())
    .optional()
    .refine(
      (val) => {
        // biome-ignore lint/suspicious/noDoubleEquals: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        if (val == undefined) return true;
        return States.map((state) => state.id).includes(val);
      },
      {
        message: 'Invalid state',
      }
    ),
  notes: z.string().optional(),
  document_id: z.string().optional(),
  premium_amount: z
    .union([z.string(), z.number()])
    .transform(toNumber)
    .optional(),
  internal_id: z.string().optional(),
  payment_mode: z.string().optional(),
});

export const ReportDataCreateDtoSchema = ReportDataBaseSchema;
export type ReportDataCreateDtoType = z.infer<typeof ReportDataCreateDtoSchema>;

export const ReportDataBulkCreateDtoSchema = z.array(ReportDataBaseSchema);
export type ReportDataBulkCreateDtoType = z.infer<
  typeof ReportDataBulkCreateDtoSchema
>;

export const ReportDataEditDtoSchema = ReportDataBaseSchema.extend({
  id: z.union([z.string(), z.number()]),
});
export type ReportDataEditDtoType = z.infer<typeof ReportDataEditDtoSchema>;

// Create array of ReportDataBaseSchema for bulk edit
export const ReportDataBulkEditDtoSchema = z.array(ReportDataEditDtoSchema);
export type ReportDataBulkEditDtoType = z.infer<
  typeof ReportDataBulkEditDtoSchema
>;
