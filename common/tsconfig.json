{"compilerOptions": {"target": "ES2015", "allowJs": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "incremental": true, "isolatedModules": true, "jsx": "preserve", "module": "CommonJS", "moduleResolution": "node", "noEmit": false, "noFallthroughCasesInSwitch": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "strict": false, "baseUrl": ".", "outDir": "./dist", "paths": {"@/*": ["*"]}, "sourceMap": true}, "exclude": ["node_modules"], "include": ["**/*.ts", "**/*.tsx"], "ts-node": {"compilerOptions": {"module": "commonjs"}}}