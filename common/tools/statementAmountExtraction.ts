import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';

dayjs.extend(customParseFormat);

const extractAmountFromFilename = (filename) => {
  // Remove path prefix and possible file size information
  const fileNameOnly = filename.replace(/^\.\//, '').split('/').pop();
  const nameWithoutSize = fileNameOnly.replace(/\s+-\s+[\d.]+\s+K[B]?$/, '');

  // Remove file extension and handle special case
  const nameWithoutExt = nameWithoutSize.replace(
    /\.(pdf|csv|xlsx?|xls|txt|png|jpe?g|html?|jfif)$/i,
    ''
  );

  // Special case for negative amounts: space followed by dash and number
  const negativeAmountMatch = nameWithoutExt.match(
    /\s-(\d{1,3}(?:,\d{3})*\.\d{2})/
  );
  if (negativeAmountMatch) {
    return -parseFloat(negativeAmountMatch[1].replace(/,/g, ''));
  }

  const specialPattern = /^\d+\s+[A-Za-z]+\s+\d{1,2}-\d{4}\s+(\d+)\s+(\d{2})/;
  const specialMatch = nameWithoutExt.match(specialPattern);
  if (specialMatch) {
    return parseFloat(`${specialMatch[1]}.${specialMatch[2]}`);
  }

  const isLikelyDate = (str) => {
    // Check if the string matches a date format like "MM.DD" or "M.DD"
    if (!/\b(\d{1,2})\.(\d{2})\b/.test(str)) {
      return false;
    }

    // Validate the month and day
    return (
      dayjs(str, 'M.DD', true).isValid() || dayjs(str, 'MM.DD', true).isValid()
    );
  };

  // Helper function to validate amount candidates
  const isValidAmountCandidate = (candidate, patternsToExclude) => {
    return (
      !patternsToExclude.some((p) => p.includes(candidate)) &&
      !isLikelyDate(candidate)
    );
  };

  // Define date patterns to exclude
  const excludePatterns = [
    // eslint-disable-next-line no-useless-escape
    /\d{1,2}[/.-]\d{1,2}[/.-]\d{2,4}/, // MM/DD/YYYY or DD/MM/YYYY
    // eslint-disable-next-line no-useless-escape
    /\d{1,2}[/.-]\d{4}/, // MM/YYYY or M-YYYY
    /\d{1,2}\.\d{1,2}\.\d{2,4}/, // MM.DD.YYYY
    /\d{8}/, // E.g., 10012024 (MMDDYYYY)
    /\d{6}/, // E.g., 102024 (MMYYYY) or 032125 (MMDDYY)
    // eslint-disable-next-line no-useless-escape
    /\d{5,6}-\d{6}/, // E.g., 10012024-103124 (date range)
    // eslint-disable-next-line no-useless-escape
    /\d{8}-\d{8}/, // E.g., 10012024-10312024 (date range)
    /\s-\s[\w]+\s-\s\d{6}\.pdf$/i, // E.g., "- TransGlobal - 032125.pdf"
  ];
  const patternsToExclude = [];
  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  excludePatterns.forEach((pattern) => {
    const matches = nameWithoutExt.match(new RegExp(pattern, 'g'));
    if (matches) patternsToExclude.push(...matches);
  });

  // Priority 1: $ symbol followed by amount (flexible pattern)
  // This handles cases like "$16,000.00" and also "$-12.20"
  const currencySymbolMatch = nameWithoutExt.match(
    /\$\s*[-]?\s*([0-9,]+(?:\.\d+)?)/
  );
  if (currencySymbolMatch) {
    return parseFloat(currencySymbolMatch[1].replace(/,/g, ''));
  }

  // If $ exists but we didn't match above, try to find the first number after $
  const dollarSignIndex = nameWithoutExt.indexOf('$');
  if (dollarSignIndex !== -1) {
    const afterDollarSign = nameWithoutExt.substring(dollarSignIndex + 1);
    const firstNumberMatch = afterDollarSign.match(
      /\s*[-]?\s*([0-9,]+(?:\.\d+)?)/
    );
    if (firstNumberMatch) {
      return parseFloat(firstNumberMatch[1].replace(/,/g, ''));
    }
  }

  // Priority 2: Parentheses with amount - only match content with decimal point or $ sign
  const parenthesesMatch = nameWithoutExt.match(
    /\(\s*(\$?\s*[-]?[0-9,]+\.\d+|\$\s*[-]?[0-9,]+)\s*\)/
  );
  if (parenthesesMatch) {
    const amount = parenthesesMatch[1].trim();
    // Ensure content has decimal point or $ sign
    if (amount.includes('.') || amount.includes('$')) {
      return parseFloat(amount.replace(/[$,]/g, ''));
    }
  }

  // Priority 3: Amount after dash (modified to handle test cases)
  const dashAmountMatch = nameWithoutExt.match(
    /\s-\s\$?\s*([0-9,]+\.\d{2})|\b([0-9,]+\.\d{2})\s*-\s*/
  );
  if (dashAmountMatch) {
    const amount = dashAmountMatch[1] || dashAmountMatch[2];
    return parseFloat(amount.replace(/,/g, ''));
  }

  // Priority 4: Amount keywords followed by numbers
  const amountKeywordMatch = nameWithoutExt.match(
    // eslint-disable-next-line no-useless-escape
    /\b(amount|payment|total|fee|cost|deposit|commission)s?\s+[$]?(\d+(?:[.,]\d+)?)/i
  );
  if (amountKeywordMatch) {
    return parseFloat(amountKeywordMatch[2].replace(/,/g, '.'));
  }

  // Priority 5: End of filename "number space number" format (likely dollars and cents)
  const endingSpaceNumberMatch = nameWithoutExt.match(
    /(\d+)\s+(\d{2})(?!\d)\s*$/
  );
  if (endingSpaceNumberMatch) {
    const candidate = `${endingSpaceNumberMatch[1]}.${endingSpaceNumberMatch[2]}`;
    if (
      !patternsToExclude.some((p) => p.includes(candidate.replace('.', '')))
    ) {
      return parseFloat(candidate);
    }
  }

  // Priority 6: "Number space number" format in the middle of the filename
  const spaceNumberMatch = nameWithoutExt.match(/(?!^)(\d+)\s+(\d{2})(?!\d)/);
  if (spaceNumberMatch) {
    const candidate = `${spaceNumberMatch[1]}.${spaceNumberMatch[2]}`;
    if (
      !patternsToExclude.some((p) => p.includes(candidate.replace('.', '')))
    ) {
      return parseFloat(candidate);
    }
  }

  // Priority 7: Standard amount format (e.g., 1,234.56 or 1234.56)
  const standardAmountMatch = nameWithoutExt.match(
    /[_-]?(\d{1,3}(?:,\d{3})*\.\d{2}|\d+\.\d{2})\b/
  );
  if (standardAmountMatch) {
    const candidate = standardAmountMatch[1];
    if (isValidAmountCandidate(candidate, patternsToExclude)) {
      return parseFloat(candidate.replace(/,/g, ''));
    }
  }

  // Priority 8: Number with decimal at the end of the filename
  const endingDotNumber = nameWithoutExt.match(/(\d+\.\d{2})(?:\s*)?$/);
  if (endingDotNumber) {
    const candidate = endingDotNumber[1];
    if (isValidAmountCandidate(candidate, patternsToExclude)) {
      return parseFloat(candidate);
    }
  }

  // Final attempt: Look for any number that looks like an amount
  // Be more cautious and check against exclusions
  const allAmounts = [];

  // Find all potential amounts
  const amountCandidates = nameWithoutExt.match(/\b\d+\.\d{2}\b/g);
  if (amountCandidates) {
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    amountCandidates.forEach((candidate) => {
      if (isValidAmountCandidate(candidate, patternsToExclude)) {
        allAmounts.push(parseFloat(candidate));
      }
    });
  }

  if (allAmounts.length > 0) {
    // If multiple potential amounts found, return the last one (usually the total)
    return allAmounts[allAmounts.length - 1];
  }

  return null;
};

export default extractAmountFromFilename;
