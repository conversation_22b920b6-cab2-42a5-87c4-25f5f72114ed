import BaseTextFormatter from '../shared/base/base-text-formatter';

export class CustomerTextFormatter extends BaseTextFormatter {
  // biome-ignore lint/complexity/noUselessConstructor: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  constructor() {
    super();
  }

  addressTextFormatter = (v: {
    street: string;
    city: string;
    geo_state: string;
    zipcode: string;
  }) => {
    if (!v) return '';
    const values = [v.street, v.city, v.geo_state, v.zipcode].filter(
      (v) => !!v
    );
    return values.join(', ');
  };

  reportDataTextFormatter = (val: unknown[]) => {
    return val?.length || '';
  };
}
