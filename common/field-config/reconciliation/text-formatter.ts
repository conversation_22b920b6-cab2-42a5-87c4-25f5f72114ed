import { isNill } from 'common/helpers';
import { formatCurrency } from 'common/helpers/formatCurrency';
import Formatter from 'common/Formatter';

import BaseTextFormatter from '../shared/base/base-text-formatter';
import type { IReconciliationRowData } from '../shared/types/reconciliation';

export class ReconciliationTextFormatter extends BaseTextFormatter {
  // biome-ignore lint/correctness/noUnusedPrivateClassMembers: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private additionalConfig: { account_id?: string; timezone?: string };

  constructor(additionalConfig?: { account_id?: string; timezone?: string }) {
    super();
    this.additionalConfig = additionalConfig;
  }

  contactsSplitTextFormatter = (
    _value: {
      [key: string]: number;
    },
    row: {
      contactsSplitInfo?: string[];
    }
  ) => {
    return row.contactsSplitInfo?.join('\n') || '';
  };

  contactsTextFormatter = (
    _value: string[],
    row: { contactNames?: string[] }
  ) => {
    return row.contactNames?.join(', ') || '';
  };

  balanceTextFormatter = (value?: number | string | null) => {
    if (isNill(value)) return '';
    return formatCurrency(value);
  };

  amountPaidTextFormatter = (value?: {
    amount_paid?: { amount_paid?: number | string };
  }) => {
    return formatCurrency(value?.amount_paid?.amount_paid);
  };

  profitTextFormatter = (
    _,
    rowData?: {
      amount_paid?: { amount_paid?: { amount_paid?: number } };
      agent_commission_amount?: number;
    }
  ) => {
    const profit = calculateProfit(rowData);
    if (!profit) return '';
    return formatCurrency(profit);
  };

  profitPercentageTextFormatter = (
    _,
    rowData?: {
      amount_paid?: { amount_paid?: { amount_paid?: number } };
      agent_commission_amount?: number;
    }
  ) => {
    const profitPercentage = calculateProfitPercentage(rowData);
    if (!profitPercentage) return '';
    return Formatter.percentageWithPrecision(profitPercentage);
  };

  statementsTextFormatter = (
    value?: { document?: { filename?: string } }[]
  ) => {
    if (Array.isArray(value) && value.length > 0) {
      return value
        .map((statement) => {
          if (statement?.document) {
            return statement.document.filename;
          }
          return 'Document not available';
        })
        .join('\n');
    }
    return '';
  };

  reportDataIdTextFormatter = (
    _val,
    row: { report_data?: { document?: { filename?: string } } }
  ) => {
    return row.report_data?.document?.filename || '';
  };

  statementStrIdsTextFormatter = (
    _val,
    row: { statement_str_ids?: string[] }
  ) => {
    return row.statement_str_ids?.join('\n') || '';
  };

  policyIdTextFormatter = (
    value: string | number,
    rowData: IReconciliationRowData
  ) => {
    if (rowData.report_data_parent) {
      return `${value}*`;
    }
    if (rowData.children_report_data_ids) {
      return `${value} 🗂️`;
    }
    return value;
  };

  commissionAmountMonthlyTextFormatter = (value?: {
    [key: string]: { commission_amount_monthly?: number | string };
  }) => {
    if (!value) return '';
    return Object.entries(value)
      .map(([key, val]) => {
        return `${key}: ${formatCurrency(val?.commission_amount_monthly)}`;
      })
      .join('\n');
  };

  agentCommissionAmountPctTextFormatter = (value: number | string) => {
    if (typeof value === 'number' && Number.isNaN(value)) return '';
    return Formatter.percentageWithPrecision(value);
  };
}

const calculateProfit = (rowData: {
  amount_paid?: { amount_paid?: { amount_paid?: number } };
  agent_commission_amount?: number;
}) => {
  const amountPaid = parseFloat(
    String(rowData.amount_paid?.amount_paid?.amount_paid)
  );

  const agentCommissionAmount = parseFloat(
    String(rowData.agent_commission_amount)
  );
  if (Number.isNaN(amountPaid) || Number.isNaN(agentCommissionAmount)) {
    return undefined;
  }

  const profit = amountPaid - agentCommissionAmount;
  return profit;
};

const calculateProfitPercentage = (rowData: {
  amount_paid?: { amount_paid?: { amount_paid?: number } };
  agent_commission_amount?: number;
  commissionable_premium_amount?: string;
}) => {
  const profit = calculateProfit(rowData);

  if (profit === undefined) return undefined;
  const commissionablePremiumAmount = parseFloat(
    rowData.commissionable_premium_amount as string
  );

  if (
    Number.isNaN(commissionablePremiumAmount) ||
    commissionablePremiumAmount === 0
  ) {
    return undefined;
  }

  const profitPercentage = (profit / commissionablePremiumAmount) * 100;
  return profitPercentage;
};
