import { SystemRoles } from 'common/globalTypes';
import UILabels from 'common/constants/UILabels';
import { FieldTypes } from 'common/constants';
import type { FieldConfig } from 'common/field-config/shared/types/field';
import { formatDate } from 'common/helpers/DataTransformation/formatter';

import { DocumentTextFormatter } from './text-formatter';

export const SyncStatusList = [
  {
    label: 'Synced',
    id: 'synced',
  },
  {
    label: 'Not synced',
    id: 'not_synced',
  },
];

export const getDocumentFieldConfig = ({
  mode,
  filters,
  additionalConfig,
}: {
  mode?: string | null;
  filters?: {
    [key: string]: {
      label: string;
    };
  };
  additionalConfig?: {
    timezone?: string;
  };
}) => {
  const labels = new UILabels(mode);
  const textFormatter = new DocumentTextFormatter(additionalConfig);

  return {
    useNewTable: true,
    label: labels.getLabel('documents', 'title'),
    table: 'documents',
    apiEndpoint: 'documents',
    bulkEditEndpoint: 'bulk-edit',
    filters,
    fields: {
      filename: {
        label: 'File',
        enabled: true,
        copyable: true,
        style: { minWidth: 240 },
        textFormatter: textFormatter.filenameTextFormatter,
      },
      type: {
        bulkEdit: true,
        label: 'Type',
        enabled: true,
        type: FieldTypes.CUSTOM,
        textFormatter: textFormatter.typeTextFormatter,
      },
      companies: {
        label: 'Company',
        enabled: true,
        textFormatter: textFormatter.companiesTextFormatter,
      },
      company_str_id: {
        label: 'Company ID',
        enabled: true,
        textFormatter: textFormatter.textFormatter,
        exportOnly: true,
        isRelationalField: true,
      },
      statement_data: {
        label: 'Records',
        disableSort: true,
        enabled: true,
        textFormatter: textFormatter.statementDataTextFormatter,
      },
      statement_amount: {
        label: 'Commission totals',
        enabled: true,
        disableSort: true,
        bulkEdit: true,
        type: FieldTypes.CUSTOM,
        textFormatter: textFormatter.statementAmountTextFormatter,
      },
      bank_total_amount: {
        label: 'Bank totals',
        enabled: true,
        disableSort: true,
        bulkEdit: true,
        type: FieldTypes.CUSTOM,
        textFormatter: textFormatter.bankTotalAmountTextFormatter,
      },
      created_by: {
        label: 'Imported total',
        enabled: true,
        disableSort: true,
        textFormatter: textFormatter.createdByTextFormatter,
        access: SystemRoles.ADMIN,
      },
      deposit_date: {
        label: 'Deposit date',
        bulkEdit: true,
        enabled: true,
        type: FieldTypes.DATE,
        textFormatter: formatDate,
      },
      statement_month: {
        label: 'Statement month',
        enabled: true,
        bulkEdit: true,
        type: FieldTypes.CUSTOM,
        textFormatter: textFormatter.statementMonthTextFormatter,
      },
      status: {
        label: 'Status',
        enabled: true,
        bulkEdit: true,
        type: FieldTypes.CUSTOM,
        enableNullCheckbox: false,
        textFormatter: textFormatter.statusTextFormatter,
      },
      method: {
        label: 'Method',
        enabled: true,
        access: SystemRoles.ADMIN,
        textFormatter: textFormatter.methodTextFormatter,
      },
      notes: {
        label: 'Notes',
        enabled: true,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      sync_id: {
        label: 'Synced',
        enabled: true,
        type: 'boolean',
        options: SyncStatusList,
        textFormatter: textFormatter.syncIdTextFormatter,
      },
      imported_at: {
        label: 'Imported at',
        enabled: true,
        access: SystemRoles.ADMIN,
        textFormatter: textFormatter.importedAtTextFormatter,
      },
      created_at: {
        label: 'Uploaded at',
        enabled: true,
        textFormatter: textFormatter.createdAtTextFormatter,
        readOnly: true,
      },
    } as Record<string, FieldConfig>,
  };
};

// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
export const mergeConfigs = (config: any, renderConfig: any) => {
  const mergedFields = { ...renderConfig.fields };

  for (const fieldKey of Object.keys(config.fields)) {
    if (mergedFields[fieldKey]) {
      mergedFields[fieldKey] = {
        ...mergedFields[fieldKey],
        ...config.fields[fieldKey],
      };
    }
  }

  return {
    ...renderConfig,
    ...config,
    fields: mergedFields,
  };
};
