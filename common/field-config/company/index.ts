import { FieldTypes } from 'common/constants';
import { SystemRoles } from 'common/globalTypes';

import { CompanyTextFormatter } from './text-formatter';

export const getCompanyFieldConfig = () => {
  const textFormatter = new CompanyTextFormatter();
  return {
    label: 'Companies',
    table: 'companies',
    editable: true,
    copyable: true,
    bulkAdd: true,
    validateData: (data) => !!data?.company_name,
    fields: {
      company_name: {
        label: 'Company name',
        required: true,
        enabled: true,
      },
      email: {
        label: 'Email',
        enabled: true,
      },
      website: {
        label: 'Website',
        enabled: true,
      },
      phone: {
        label: 'Phone',
        enabled: true,
      },
      address: {
        label: 'Address',
        enabled: true,
      },
      notes: {
        label: 'Notes',
        enabled: true,
      },
      type: {
        label: 'Type',
        type: FieldTypes.SELECT,
        multiple: true,
        options: [
          'Carrier',
          'MGA/MGU',
          'General agency',
          'Sub agency',
          'Customer',
          'Partner',
        ],
        strToValue: (vals) =>
          vals?.split(',').map((v) => v.trim()) || undefined,
        enabled: true,
        textFormatter: textFormatter.typeTextFormatter,
      },
      divider: {
        type: FieldTypes.DIVIDER,
        access: SystemRoles.ADMIN,
        label: 'Admin',
        enabled: false,
      },
      access: {
        label: 'Access',
        type: FieldTypes.SELECT,
        options: ['account', 'global'],
        access: SystemRoles.ADMIN,
        enabled: true,
      },
      canonical_id: {
        label: 'Fintary company',
        type: FieldTypes.CUSTOM,
        table: 'companies?admin_mode=true?all=1',
        access: SystemRoles.ADMIN,
        enabled: true,
        textFormatter: textFormatter.canonicalTextFormatter,
      },
      canonical_id_relation: {
        label: 'Fintary company ID',
        enabled: true,
        isRelationalField: true,
        exportOnly: true,
        textFormatter: textFormatter.canonicalRelationTextFormatter,
      },
      processor_str_ids: {
        label: 'Processors',
        type: FieldTypes.CUSTOM,
        table: 'processors',
        access: SystemRoles.ADMIN,
        enabled: true,
        textFormatter: textFormatter.processorTextFormatter,
      },
      processor_str_ids_relation: {
        label: 'Processor IDs',
        enabled: true,
        isRelationalField: true,
        exportOnly: true,
        textFormatter:
          textFormatter.extractIdsTextFormatter('processor_str_ids'),
      },
      profile_str_ids: {
        label: 'Document profiles',
        type: FieldTypes.CUSTOM,
        table: 'document_profiles',
        access: SystemRoles.ADMIN,
        enabled: true,
        textFormatter: textFormatter.profileTextFormatter,
      },
      profile_str_ids_relation: {
        label: 'Document profile IDs',
        enabled: true,
        isRelationalField: true,
        exportOnly: true,
        textFormatter: textFormatter.extractIdsTextFormatter('profile_str_ids'),
      },
    },
  };
};
