import { describe, it, expect, beforeEach } from 'vitest';
import { ImportStatuses } from 'common/globalTypes';
import type { Company } from 'common/types/companies';

import { CompanyTextFormatter } from './text-formatter';
import type {
  Processor,
  DocumentProfile,
  CompanyDocumentProfile,
  Mapping,
} from '../shared/types/processor';

const mockCompany: Company = {
  id: 12345,
  str_id: 'company-str-id-123',
  company_name: 'Acme Insurance Company',
  account_id: 'account-123',
  uid: 'company-uid-123',
  state: 'active',
  access: 'account',
  created_at: '2024-01-15T10:30:00.000Z',
  created_by: 'user-123',
  created_proxied_by: 'proxy-123',
  updated_at: null,
  updated_by: null,
  updated_proxied_by: null,
  address: '123 Test Street',
  alias_list: ['Acme', 'Acme Inc'],
  company_id: null,
  canonical_id: 12345,
  email: '<EMAIL>',
  group_id: null,
  sync_id: 'sync-123',
  log: null,
  notes: null,
  phone: '555-1234',
  type: ['insurance'],
  website: 'https://acme.com',
  profile_str_id: null,
  config: null,
  sync_worker: 'worker-123',
};

const mockCanonicalCompanyRow = {
  canonical_company: mockCompany,
  potential_match: null,
};

const mockPotentialMatchRow = {
  canonical_company: null,
  potential_match: {
    name: 'Potential Match Company',
  },
};

const mockNoMatchRow = {
  canonical_company: null,
  potential_match: null,
};

const mockProcessor: Processor = {
  processor_str_id: 'processor-123',
  processor: {
    name: 'Test Processor',
  },
  import_status: ImportStatuses.AUTO_IMPORT,
};

const mockProcessorWithoutProcessorObject: Processor = {
  processor_str_id: 'processor-456',
  name: 'Direct Processor Name',
  import_status: ImportStatuses.REQUEST_REVIEW,
};

const mockProcessorMinimal: Processor = {
  processor_str_id: 'processor-789',
  import_status: ImportStatuses.NONE,
};

const mockCompaniesProcessorsRow = {
  companies_processors: [
    mockProcessor,
    mockProcessorWithoutProcessorObject,
    mockProcessorMinimal,
  ],
};

const mockEmptyProcessorsRow = {
  companies_processors: [],
};

const mockNullProcessorsRow = {
  companies_processors: null,
};

const mockDocumentProfile: DocumentProfile = {
  str_id: 'profile-123',
  name: 'Test Document Profile',
};

const mockCompanyDocumentProfile: CompanyDocumentProfile = {
  document_profile: mockDocumentProfile,
  auto_mapping_id: 'mapping-123',
};

const mockCompanyDocumentProfileWithoutMapping: CompanyDocumentProfile = {
  document_profile: {
    str_id: 'profile-456',
    name: 'Profile Without Mapping',
  },
};

const mockMapping: Mapping = {
  str_id: 'mapping-123',
  name: 'Test Mapping',
};

const mockCompaniesDocumentProfilesRow = {
  companies_document_profiles: [
    mockCompanyDocumentProfile,
    mockCompanyDocumentProfileWithoutMapping,
  ],
  document_profiles: [],
};

const mockDocumentProfilesRow = {
  companies_document_profiles: [],
  document_profiles: [mockDocumentProfile],
};

const mockEmptyProfilesRow = {
  companies_document_profiles: [],
  document_profiles: [],
};

const mockMappingsArray = [mockMapping];

describe('CompanyTextFormatter', () => {
  let formatter: CompanyTextFormatter;

  beforeEach(() => {
    formatter = new CompanyTextFormatter();
  });

  describe('canonicalTextFormatter', () => {
    it('Given canonical company data, should return formatted company name', () => {
      expect(formatter.canonicalTextFormatter(1, mockCanonicalCompanyRow)).toBe(
        'Acme Insurance Company'
      );
    });

    it('Given potential match data, should return formatted string with arrow prefix', () => {
      expect(formatter.canonicalTextFormatter(1, mockPotentialMatchRow)).toBe(
        '→ Potential Match Company'
      );
    });

    it('Given no match data, should return "No matches"', () => {
      expect(formatter.canonicalTextFormatter(1, mockNoMatchRow)).toBe(
        'No matches'
      );
    });

    it('Given null canonical company data, should return "No matches"', () => {
      const rowWithNullCanonical = {
        canonical_company: null,
        potential_match: null,
      };
      expect(formatter.canonicalTextFormatter(1, rowWithNullCanonical)).toBe(
        'No matches'
      );
    });

    it('Given both canonical and potential match data, should prioritize canonical company', () => {
      const rowWithBoth = {
        canonical_company: mockCompany,
        potential_match: { name: 'Should not be shown' },
      };
      expect(formatter.canonicalTextFormatter(1, rowWithBoth)).toBe(
        'Acme Insurance Company'
      );
    });
  });

  describe('processorTextFormatter', () => {
    it('Given multiple processor data, should return formatted string with names and status labels', () => {
      const result = formatter.processorTextFormatter(
        'test',
        mockCompaniesProcessorsRow
      );

      expect(result).toContain('Test Processor Auto import');
      expect(result).toContain('Direct Processor Name Request review');
      expect(result).toContain('processor-789 None');
    });

    it('Given processor data with processor object, should return formatted string with processor name and status', () => {
      const rowWithSingleProcessor = {
        companies_processors: [mockProcessor],
      };

      const result = formatter.processorTextFormatter(
        'test',
        rowWithSingleProcessor
      );

      expect(result).toBe('Test Processor Auto import');
    });

    it('Given processor data without processor object, should return formatted string with direct name and status', () => {
      const rowWithDirectName = {
        companies_processors: [mockProcessorWithoutProcessorObject],
      };

      const result = formatter.processorTextFormatter(
        'test',
        rowWithDirectName
      );

      expect(result).toBe('Direct Processor Name Request review');
    });

    it('Given minimal processor data, should fallback to processor_str_id with status', () => {
      const rowWithMinimalProcessor = {
        companies_processors: [mockProcessorMinimal],
      };

      const result = formatter.processorTextFormatter(
        'test',
        rowWithMinimalProcessor
      );

      expect(result).toBe('processor-789 None');
    });

    it('Given empty processors array, should return empty string', () => {
      expect(
        formatter.processorTextFormatter('test', mockEmptyProcessorsRow)
      ).toBe('');
    });

    it('Given null companies_processors, should return empty string', () => {
      expect(
        formatter.processorTextFormatter('test', mockNullProcessorsRow)
      ).toBe('');
    });

    it('Given non-array companies_processors, should return empty string', () => {
      const rowWithNonArray = {
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        companies_processors: 'not-an-array' as any,
      };

      expect(formatter.processorTextFormatter('test', rowWithNonArray)).toBe(
        ''
      );
    });

    it('Given multiple processors data, should join with newlines', () => {
      const result = formatter.processorTextFormatter(
        'test',
        mockCompaniesProcessorsRow
      );

      const lines = result.split('\n');
      expect(lines).toHaveLength(3);
      expect(lines[0]).toContain('Test Processor');
      expect(lines[1]).toContain('Direct Processor Name');
      expect(lines[2]).toContain('processor-789');
    });
  });

  describe('profileTextFormatter', () => {
    it('Given companies document profiles with mapping data, should return formatted string with profile name and mapping', () => {
      const result = formatter.profileTextFormatter(
        'test',
        mockCompaniesDocumentProfilesRow,
        { companyExportMappings: mockMappingsArray }
      );

      expect(result).toContain('Test Document Profile Test Mapping');
      expect(result).toContain('Profile Without Mapping');
    });

    it('Given document profiles data, should return formatted string with profile name', () => {
      const result = formatter.profileTextFormatter(
        'test',
        { ...mockDocumentProfilesRow, companies_document_profiles: null },
        { companyExportMappings: mockMappingsArray }
      );

      expect(result).toBe('Test Document Profile');
    });

    it('Given profile data without mapping, should return formatted string with profile name only', () => {
      const result = formatter.profileTextFormatter(
        'test',
        {
          companies_document_profiles: [
            mockCompanyDocumentProfileWithoutMapping,
          ],
          document_profiles: [],
        },
        { companyExportMappings: mockMappingsArray }
      );

      expect(result).toBe('Profile Without Mapping');
    });

    it('Given profile data with unknown mapping, should return formatted string with profile name and mapping ID', () => {
      const profileWithUnknownMapping = {
        companies_document_profiles: [
          {
            document_profile: mockDocumentProfile,
            auto_mapping_id: 'unknown-mapping',
          },
        ],
        document_profiles: [],
      };

      const result = formatter.profileTextFormatter(
        'test',
        profileWithUnknownMapping,
        { companyExportMappings: mockMappingsArray }
      );

      expect(result).toBe('Test Document Profile unknown-mapping');
    });

    it('Given empty profiles data, should return empty string', () => {
      expect(
        formatter.profileTextFormatter('test', mockEmptyProfilesRow, {
          companyExportMappings: mockMappingsArray,
        })
      ).toBe('');
    });

    it('Given null profiles arrays, should return empty string', () => {
      const rowWithNullProfiles = {
        companies_document_profiles: null,
        document_profiles: null,
      };

      expect(
        formatter.profileTextFormatter('test', rowWithNullProfiles, {
          companyExportMappings: mockMappingsArray,
        })
      ).toBe('');
    });

    it('Given profile data with empty mappings array, should return formatted string with mapping ID', () => {
      const result = formatter.profileTextFormatter(
        'test',
        mockCompaniesDocumentProfilesRow,
        { companyExportMappings: [] }
      );

      expect(result).toContain('Test Document Profile mapping-123');
      expect(result).toContain('Profile Without Mapping');
    });

    it('Given multiple profiles data, should join with newlines', () => {
      const result = formatter.profileTextFormatter(
        'test',
        mockCompaniesDocumentProfilesRow,
        { companyExportMappings: mockMappingsArray }
      );

      const lines = result.split('\n');
      expect(lines).toHaveLength(2);
      expect(lines[0]).toContain('Test Document Profile');
      expect(lines[1]).toContain('Profile Without Mapping');
    });

    it('Given both companies document profiles and document profiles data, should prioritize companies document profiles', () => {
      const rowWithBoth = {
        companies_document_profiles: [mockCompanyDocumentProfile],
        document_profiles: [mockDocumentProfile],
      };

      const result = formatter.profileTextFormatter('test', rowWithBoth, {
        companyExportMappings: mockMappingsArray,
      });

      expect(result).toBe('Test Document Profile Test Mapping');
    });

    it('Given mapping data without name, should return formatted string with mapping ID', () => {
      const mappingWithoutName: Mapping = {
        str_id: 'mapping-no-name',
      };

      const profileWithMappingNoName = {
        companies_document_profiles: [
          {
            document_profile: mockDocumentProfile,
            auto_mapping_id: 'mapping-no-name',
          },
        ],
        document_profiles: [],
      };

      const result = formatter.profileTextFormatter(
        'test',
        profileWithMappingNoName,
        { companyExportMappings: [mappingWithoutName] }
      );

      expect(result).toBe('Test Document Profile mapping-no-name');
    });
  });
});
