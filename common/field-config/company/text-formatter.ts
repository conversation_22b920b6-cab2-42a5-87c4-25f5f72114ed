import type { Company } from 'common/types/companies';
import type {
  CompanyDocumentProfile,
  DocumentProfile,
  Processor,
} from 'common/field-config/shared/types/processor';
import { ImportStatusesLabels } from 'common/globalTypes';

import BaseTextFormatter from '../shared/base/base-text-formatter';
import type { TextFormatterArgs } from '../shared/types/field';

export class CompanyTextFormatter extends BaseTextFormatter {
  // biome-ignore lint/complexity/noUselessConstructor: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  constructor() {
    super();
  }

  canonicalTextFormatter = (
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    val: number,
    row: { canonical_company: Company; potential_match: { name: string } }
  ) => {
    if (row?.canonical_company) {
      return row.canonical_company.company_name;
    }

    if (row?.potential_match) {
      return `→ ${row.potential_match.name}`;
    }

    return 'No matches';
  };

  processorTextFormatter = (
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    val: string,
    row: { companies_processors: Processor[] }
  ) => {
    const processorsData: Processor[] =
      row?.companies_processors && Array.isArray(row.companies_processors)
        ? row.companies_processors
        : [];

    return processorsData
      .map((processor) => {
        const processorName =
          processor.processor?.name ||
          processor.name ||
          processor.processor_str_id;
        const statusLabel = ImportStatusesLabels[processor.import_status];
        const result = [];
        if (processorName) {
          result.push(processorName);
        }
        if (statusLabel) {
          result.push(statusLabel);
        }
        return result.join(' ');
      })
      .join('\n');
  };

  profileTextFormatter = (
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    val: string,
    row: {
      companies_document_profiles: CompanyDocumentProfile[];
      document_profiles: DocumentProfile[];
    },
    { companyExportMappings: mappingsArray }: TextFormatterArgs
  ) => {
    const profiles: (CompanyDocumentProfile | DocumentProfile)[] =
      row?.companies_document_profiles || row?.document_profiles || [];

    return profiles
      .map((profile) => {
        const profileData =
          'document_profile' in profile ? profile.document_profile : profile;
        const mappingId =
          'auto_mapping_id' in profile ? profile.auto_mapping_id : undefined;

        const mapping =
          mappingId && mappingsArray.length > 0
            ? mappingsArray.find((m) => m.str_id === mappingId)
            : null;
        const mappingName = mapping?.name || mappingId;

        const result = [profileData.name];

        if (mappingName) {
          result.push(mappingName);
        }

        return result.join(' ');
      })
      .join('\n');
  };

  canonicalRelationTextFormatter = (
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    val: string,
    row: { canonical_id: string; potential_match: { id: string } }
  ) => {
    return row.canonical_id || row.potential_match?.id || 'No matches';
  };

  extractIdsTextFormatter =
    (field: string) =>
    (
      _value: unknown,
      row: {
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        [key: string]: any;
      }
    ) => {
      if (!Array.isArray(row[field]))
        return row[field] ? String(row[field]) : '';

      return row[field]
        .map((item) => item.str_id || item.id || item)
        .join('\n');
    };

  typeTextFormatter = (val?: string[]) => {
    if (!Array.isArray(val)) return '';
    return val.join('\n');
  };
}
