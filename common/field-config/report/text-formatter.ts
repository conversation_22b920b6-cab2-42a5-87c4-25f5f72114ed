import { CustomerType } from 'common/customer/customer.constants';
import CommonFormatter, { type Contact } from 'common/Formatter';
import BaseTextFormatter from 'common/field-config/shared/base/base-text-formatter';
import type { Company } from 'common/types/companies';
import {
  formatCurrency,
  formatDate,
  formatPercentage,
} from 'common/helpers/DataTransformation/formatter';

import type { TextFormatterArgs } from '../shared/types/field';

export class ReportTextFormatter extends BaseTextFormatter {
  private additionalConfig: { account_id?: string; timezone?: string };

  constructor(additionalConfig?: { account_id?: string; timezone?: string }) {
    super();
    this.additionalConfig = additionalConfig;
  }

  customerNameTextFormatter = (
    value: string,
    row: {
      customer: {
        type: string;
        first_name: string;
        last_name: string;
        company_name: string;
      };
    }
  ) => {
    if (row?.customer) {
      const name =
        row?.customer?.type === CustomerType.individual
          ? `${row.customer.first_name} ${row.customer.last_name}`
          : row.customer.company_name;
      return name;
    }
    return value;
  };

  policyIdTextFormatter = (value: string | number) => {
    return CommonFormatter.policyNumber(value?.toString() ?? '', {
      account_id: this.additionalConfig?.account_id,
    });
  };

  effectiveDateTextFormatter = (value?: string) => {
    return formatDate(value);
  };

  signedDateTextFormatter = (value?: string) => {
    return formatDate(value);
  };

  policyDateTextFormatter = (value?: string) => {
    return formatDate(value);
  };

  writingCarrierNameTextFormatter = (
    value: string,
    _row: unknown,
    args: TextFormatterArgs
  ) => {
    const collectionVals = args?.allDynamicSelectMap?.companies;
    if (value && collectionVals instanceof Map) {
      const datum = collectionVals?.get(value) as Company;
      return datum ? datum?.company_name : `${value} (not found in Fintary)`;
    }
    return value;
  };

  writingCarrierIdTextFormatter = (
    _value: string,
    row: {
      writing_carrier_name: string;
    },
    args: TextFormatterArgs
  ) => {
    const collectionVals = args?.allDynamicSelectMap?.companies;
    if (row?.writing_carrier_name && collectionVals instanceof Map) {
      const datum = collectionVals?.get(row?.writing_carrier_name) as Company;
      return datum
        ? datum?.id
        : `${row?.writing_carrier_name} (not found in Fintary)`;
    }
    return '';
  };

  premiumAmountTextFormatter = (value: string | number) => {
    return formatCurrency(value);
  };

  excessAmountTextFormatter = (value: string | number) => {
    return formatCurrency(value);
  };

  productNameTextFormatter = (
    value: string,
    _row: unknown,
    args: TextFormatterArgs
  ) => {
    const collectionVals = args?.allDynamicSelectMap?.['companies/products'];
    if (value && collectionVals instanceof Map) {
      const datum = collectionVals?.get(value) as { product_name: string };
      return datum ? datum?.product_name : `${value} (not found in Fintary)`;
    }
    return value;
  };

  cancellationDateTextFormatter = (value: string) => {
    return formatDate(value);
  };

  reinstatementDateTextFormatter = (value: string) => {
    return formatDate(value);
  };

  commissionsExpectedTextFormatter = (value: string) => {
    return formatCurrency(value);
  };

  splitPercentageTextFormatter = (value: string) => {
    return formatPercentage(value);
  };

  commissionablePremiumAmountTextFormatter = (value: string) => {
    return formatCurrency(value);
  };

  issueAgeTextFormatter = (value: string) => {
    return value;
  };

  customerPaidPremiumAmountTextFormatter = (value: string) => {
    return formatCurrency(value);
  };

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  aggregationPrimaryTextFormatter = (value: any) => {
    return value ? 'Yes' : 'No';
  };

  statementDataTextFormatter = (value: { str_id: string }[]) => {
    if (Array.isArray(value) && value.length > 0) {
      return value.map((stmt) => stmt.str_id).join('\n');
    }
    return value;
  };

  childrenReportDataTextFormatter = (value: { str_id: string }[]) => {
    if (Array.isArray(value) && value.length > 0) {
      return value.map((child) => child.str_id).join('\n');
    }
    return value;
  };

  tagsTextFormatter = (value: string | string[]) => {
    if (Array.isArray(value) && value.length === 0) return '';
    if (typeof value === 'string')
      return (
        value
          ?.split(',')
          .map((s) => s.trim())
          .join(', ') ?? ''
      );
    if (Array.isArray(value)) return value.join(', ');
    return value;
  };

  documentIdTextFormatter = (
    value: string,
    _row: unknown,
    args: TextFormatterArgs
  ) => {
    const collectionVals = args?.allDynamicSelectMap?.documents;
    if (value && collectionVals instanceof Map) {
      const datum = collectionVals?.get(value) as { filename: string };
      return datum ? datum.filename : `${value} (not found in Fintary)`;
    }
    return value;
  };

  documentRelationshipIdTextFormatter = (
    _value?: string,
    row?: {
      document_id: string;
    }
  ) => {
    return row?.document_id ?? '';
  };

  firstPaymentDateTextFormatter = (value: string) => {
    return formatDate(value);
  };

  firstProcessedDateTextFormatter = (value: string) => {
    return formatDate(value);
  };

  commissionProfileIdTextFormatter = (
    value: string,
    _row: unknown,
    args: TextFormatterArgs
  ) => {
    const collectionVals = args?.allDynamicSelectMap?.['commission-profiles'];
    if (value && collectionVals instanceof Map) {
      const datum = collectionVals?.get(value) as { name: string };
      return datum ? datum?.name : `${value} (not found in Fintary)`;
    }
    return value;
  };

  agentPayoutRateOverrideTextFormatter = (
    value: Record<string, number>,
    _row: unknown,
    args: TextFormatterArgs
  ) => {
    const collectionVals = args?.allDynamicSelectMap?.contacts;
    if (!value || !(collectionVals instanceof Map)) return '';
    return (
      Object.entries(value)
        // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        .filter(([k, v]) => k !== 'total')
        .map(([k, v]) => {
          const contact = collectionVals?.get(k) as Contact;

          return `${CommonFormatter.contact(contact, {
            account_id: this.additionalConfig?.account_id,
          })}: ${formatPercentage(Number(v))}`;
        })
        .join(', ')
    );
  };

  contactsTextFormatter = (
    value?: string[],
    _row?: unknown,
    args?: TextFormatterArgs
  ) => {
    const collectionVals = args?.allDynamicSelectMap?.contacts;
    if (value && Array.isArray(value) && collectionVals instanceof Map) {
      return value
        .map((item) => {
          const datum = collectionVals?.get(item) as Contact;
          return datum
            ? CommonFormatter.contact(datum, {
                account_id: this.additionalConfig?.account_id,
              })
            : `${value} (not found in Fintary)`;
        })
        .join('\n');
    }
    return value;
  };

  contactsIdTextFormatter = (
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    value: string,
    row: {
      contacts: string[];
    }
  ) => {
    return row?.contacts?.join('\n') ?? '';
  };

  contactsSplitTextFormatter = (
    value: Record<string, number>,
    _row: unknown,
    args: TextFormatterArgs
  ) => {
    const collectionVals = args?.allDynamicSelectMap?.contacts;
    if (!value || !(collectionVals instanceof Map)) return '';

    if (Object.keys(value).length > 0) {
      const entries = Object.entries(value).map(([k, v]) => {
        const contact = collectionVals?.get(k) as Contact;
        const contactName = CommonFormatter.contact(contact, {
          account_id: this.additionalConfig?.account_id,
        });
        return `${contactName}: ${v}%`;
      });

      const total = Object.values(value).reduce(
        (a: number, b: number) => +a + +b,
        0
      );
      const result = entries.join('\n');

      if (total !== 100) {
        return `${result}\nTotal should be 100`;
      }

      return result;
    }

    return '';
  };

  configTextFormatter = (
    _value: unknown,
    row: {
      config: { allied_payment_rule: { mode: string; priority: string[] } };
    }
  ) => {
    const config = row.config?.allied_payment_rule;
    if (!config || !config.mode) {
      return 'None';
    }
    let output = '';
    if (config.mode === 'dental_only') {
      output = 'Dental only';
    } else if (config.mode === 'dental_vision') {
      output = `Dental + Vision`;
      if (config.priority && config.priority.length > 0) {
        output += ` (Priority: ${config.priority.join(', ')})`;
      }
    }
    return output;
  };
}
