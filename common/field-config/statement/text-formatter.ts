import CommonFormatter, { type Contact } from 'common/Formatter';
import type { Company } from 'common/types/companies';
import BaseTextFormatter from 'common/field-config/shared/base/base-text-formatter';
import {
  formatCurrency,
  formatPercentage,
} from 'common/helpers/DataTransformation/formatter';
import { getFilenameFromPath } from 'common/helpers';
import { States } from 'common/helpers/states';

import type { TextFormatterArgs } from '../shared/types/field';

export class StatementTextFormatter extends BaseTextFormatter {
  private additionalConfig: { account_id?: string; timezone?: string };

  constructor(additionalConfig?: { account_id?: string; timezone?: string }) {
    super();
    this.additionalConfig = additionalConfig;
  }

  policyIdTextFormatter = (value: string | number) => {
    return CommonFormatter.policyNumber(value?.toString() ?? '', {
      account_id: this.additionalConfig?.account_id,
    });
  };

  aggregationIdTextFormatter = (
    value: string,
    row: { report: { policy_id: string; aggregation_id: string } }
  ) => {
    if (value) return value;
    return row?.report?.policy_id && row.report?.aggregation_id
      ? `${row.report?.aggregation_id}*`
      : '';
  };

  tagsTextFormatter = (value: string | string[]) => {
    if (Array.isArray(value)) return value.join(', ');
    return value;
  };

  contactsTextFormatter = (
    value?: string[],
    _row?: unknown,
    args?: TextFormatterArgs
  ) => {
    const collectionVals = args?.allDynamicSelectMap?.contacts;

    if (value && Array.isArray(value) && collectionVals instanceof Map) {
      return value
        .map((item) => {
          const datum = collectionVals?.get(item) as Contact;
          return datum
            ? CommonFormatter.contact(datum, {
                account_id: this.additionalConfig?.account_id,
              })
            : `${item} (not found in Fintary)`;
        })
        .join('\n');
    }
    return value?.toString();
  };

  contactsIdTextFormatter = (
    _value: string,
    row: {
      contacts: string[];
    }
  ) => {
    return row?.contacts?.join('\n') ?? '';
  };

  documentIdTextFormatter = (
    value: string,
    _row: unknown,
    args?: TextFormatterArgs
  ) => {
    const collectionVals = args?.allDynamicSelectMap?.documents;
    if (value && collectionVals instanceof Map) {
      const datum = collectionVals?.get(value) as {
        filename: string;
        file_path: string;
      };
      if (datum)
        return `${datum?.file_path?.endsWith(datum.filename) ? datum.filename : `${getFilenameFromPath(datum.file_path)}`}`;

      return `${value} (not found in Fintary)`;
    }
    return value;
  };

  documentIdRelationTextFormatter = (
    _value: string,
    row: { document_id: string }
  ) => {
    return row.document_id;
  };

  agentCommissionsTextFormatter = (
    val: Record<string, number>,
    _row: unknown,
    args?: TextFormatterArgs
  ) => {
    const contactsMap = args?.allDynamicSelectMap?.contacts;
    if (!val || !(contactsMap instanceof Map)) return '';
    return Object.entries(val)
      .filter(([k]) => k !== 'total')
      .map(([k, v]) => {
        let contactName = k;
        const contact = contactsMap.get(k) as Contact;
        if (contact?.str_id)
          contactName = CommonFormatter.contact(contact, {
            account_id: this.additionalConfig?.account_id,
          });
        return `${contactName}: ${formatCurrency(v)}`;
      })
      .join('\n');
  };

  agentCommissionsLogTextFormatter = (
    val: Record<string, Record<string, unknown>[]>,
    row: {
      agentCommissionContacts: Record<string, string>;
      compCalcContactMapById: Record<string, { name: string; type: string }>;
    }
  ) => {
    return CommonFormatter.agentReportLog({
      type: 'AGENT_COMMISSION_LOG',
      contentMapByIdOrStrId: val,
      statementDataItem: row,
      timezone: this.additionalConfig?.timezone,
    });
  };

  compCalcTextFormatter = (
    val: Record<string, number>,
    _row: unknown,
    args?: TextFormatterArgs
  ) => {
    if (!val) return '';
    const contactsMap = args?.allDynamicSelectMap?.contacts;
    if (!contactsMap || !(contactsMap instanceof Map)) return '';

    return Object.entries(val)
      .filter(([k]) => k !== 'total')
      .map(([k, v]) => {
        let contactName = k;
        const contact = contactsMap.get(+k) as Contact;
        if (contact)
          contactName = CommonFormatter.contact(contact, {
            account_id: this.additionalConfig?.account_id,
          });
        return `${contactName}: ${formatCurrency(v)}`;
      })
      .join('\n');
  };

  compCalcLogTextFormatter = (
    val: Record<string, Record<string, unknown>[]>,
    row: {
      agentCommissionContacts: Record<string, string>;
      compCalcContactMapById: Record<string, { name: string; type: string }>;
    }
  ) => {
    return CommonFormatter.agentReportLog({
      type: 'AGENT_COMPENSATION_LOG',
      contentMapByIdOrStrId: val,
      statementDataItem: row,
      timezone: this.additionalConfig?.timezone,
    });
  };

  compCalcStatusTextFormatter = (
    val: Record<string, number>,
    _row: unknown,
    args?: TextFormatterArgs
  ) => {
    if (!val) return '';
    const contactsMap = args?.allDynamicSelectMap?.contacts;
    if (!contactsMap || !(contactsMap instanceof Map)) return '';

    return Object.entries(val)
      .filter(([k]) => k !== 'total')
      .map(([k, v]) => {
        let contactName = k;
        const contact = contactsMap.get(+k) as Contact;
        if (contact)
          contactName = CommonFormatter.contact(contact, {
            account_id: this.additionalConfig?.account_id,
          });
        return `${contactName}: ${v}`;
      })
      .join('\n');
  };

  agentPayoutRateTextFormatter = (
    val: Record<string, number>,
    _row: unknown,
    args?: TextFormatterArgs
  ) => {
    const contactsMap = args?.allDynamicSelectMap?.contacts;
    if (!val || !(contactsMap instanceof Map)) return '';
    return Object.entries(val)
      .filter(([k]) => k !== 'total')
      .map(([k, v]) => {
        let contactName = k;
        const contact = contactsMap.get(k) as Contact;
        if (contact)
          contactName = CommonFormatter.contact(contact, {
            account_id: this.additionalConfig?.account_id,
          });
        return `${contactName}: ${CommonFormatter.percentage(v, { isPercentage: true, addPrecisionForRateLowerThanTen: true })}`;
      })
      .join('\n');
  };

  agentPayoutRateOverrideTextFormatter = (
    val: Record<string, number>,
    row: { report: { agent_payout_rate_override: Record<string, number> } },
    args?: TextFormatterArgs
  ) => {
    const agentPayoutRateOverride =
      val ?? row?.report?.agent_payout_rate_override;
    const contactsMap = args?.allDynamicSelectMap?.contacts;
    if (!agentPayoutRateOverride || !(contactsMap instanceof Map)) return '';

    return Object.entries(agentPayoutRateOverride)
      .filter(([k]) => k !== 'total' && k !== 'config')
      .map(([k, v]) => {
        const contact = contactsMap.get(k) as Contact;
        if (contact) {
          const contactName = CommonFormatter.contact(contact, {
            account_id: this.additionalConfig?.account_id,
          });
          return `${contactName}: ${formatPercentage(Number(v))}`;
        }
        return '';
      })
      .join('\n');
  };

  agentCommissionPayoutRateTextFormatter = (
    val: Record<string, number>,
    _row: unknown,
    args?: TextFormatterArgs
  ) => {
    const contactsMap = args?.allDynamicSelectMap?.contacts;
    if (!val || !(contactsMap instanceof Map)) return '';

    return Object.entries(val)
      .filter(([k]) => k !== 'total')
      .map(([k, v]) => {
        let contactName = k;
        const contact = contactsMap.get(k) as Contact;
        if (contact) {
          contactName = CommonFormatter.contact(contact, {
            account_id: this.additionalConfig?.account_id,
          });
          return `${contactName}: ${CommonFormatter.percentage(+v, {
            isPercentage: true,
            addPrecisionForRateLowerThanTen: true,
          })}`;
        }
        return '';
      })
      .join('\n');
  };

  agentCommissionsStatus2TextFormatter = (
    val: Record<string, string>,
    _row: unknown,
    args?: TextFormatterArgs
  ) => {
    if (!val) return '';
    const contactsMap = args?.allDynamicSelectMap?.contacts;
    if (!contactsMap || !(contactsMap instanceof Map)) return '';
    return Object.entries(val)
      .map(([k, v]) => {
        let contactName = k;
        const contact = contactsMap.get(k) as Contact;
        if (contact)
          contactName = CommonFormatter.contact(contact, {
            account_id: this.additionalConfig?.account_id,
          });
        return `${contactName}: ${v}`;
      })
      .join('\n');
  };

  childrenDataTextFormatter = (vals: { str_id: string }[]) => {
    if (!Array.isArray(vals)) return '';
    return vals.map((val) => val.str_id).join('\n');
  };

  reportDataIdTextFormatter = (
    val: string,
    row: { report: { str_id: string } }
  ) => {
    return val ? row?.report?.str_id : '';
  };

  reconciliationMethodTextFormatter = (
    val: string,
    _row: unknown,
    args?: TextFormatterArgs
  ) => {
    if (val === '0') return 'Manual';
    if (
      args?.allDynamicSelectMap?.reconcilers &&
      args?.allDynamicSelectMap?.reconcilers instanceof Map
    ) {
      const datum = args?.allDynamicSelectMap?.reconcilers.get(+val) as {
        name: string;
      };
      return datum ? datum.name : `${val} (not found in Fintary)`;
    }
    return val;
  };

  flagsTextFormatter = (val: Record<string, string>) => {
    if (!val) return '';
    return Object.entries(val)
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');
  };

  flagsLogTextFormatter = (
    val: Record<string, string | { message: string }>
  ) => {
    if (!val) return '';
    return Object.entries(val)
      .map(([_key, value]) => {
        if (value && typeof value === 'object' && 'message' in value) {
          return `${value.message}`;
        }
        return `${value}`;
      })
      .join('\n');
  };

  writingCarrierNameTextFormatter = (
    value: string,
    _row: unknown,
    args?: TextFormatterArgs
  ) => {
    const collectionVals = args?.allDynamicSelectMap?.companies;
    if (value && collectionVals instanceof Map) {
      const datum = collectionVals?.get(value) as Company;
      return datum ? datum?.company_name : `${value} (not found in Fintary)`;
    }
    return value;
  };

  writingCarrierIdTextFormatter = (
    _value: string,
    row: {
      writing_carrier_name: string;
    },
    args: TextFormatterArgs
  ) => {
    const collectionVals = args?.allDynamicSelectMap?.companies;
    if (row?.writing_carrier_name && collectionVals instanceof Map) {
      const datum = collectionVals?.get(row?.writing_carrier_name) as Company;
      return datum
        ? datum?.id
        : `${row?.writing_carrier_name} (not found in Fintary)`;
    }
    return '';
  };

  geoStateTextFormatter = (value: string) => {
    const state = States.find((state) => state.id === value);
    return state ? state.label : value;
  };

  splitPercentageTextFormatter = (
    value: string,
    row: {
      report: { contacts_split: Record<string, number> };
      contacts: string[];
    }
  ) => {
    return value
      ? formatPercentage(value)
      : row.contacts?.length === 1 &&
          row.report?.contacts_split?.[row.contacts[0]]
        ? `${formatPercentage(row.report?.contacts_split?.[row.contacts[0]])}*`
        : '';
  };
}
