import { FieldTypes } from 'common/constants';
import UILabels from 'common/constants/UILabels';
import { policyDataIfEmptyTextFormatter } from 'common/helpers/policyDataIfEmptyTextFormatter';
import {
  AgentCommissionsStatusesLabels,
  SystemRoles,
  TransactionParty,
} from 'common/globalTypes';
import {
  formatCurrency,
  formatDate,
  formatPercentage,
} from 'common/helpers/DataTransformation/formatter';
import { States } from 'common/helpers/states';
import CommonFormatter from 'common/Formatter';

import { buildReceivableValueFilter } from '../report/helpers/transactions.helpers';
import { StatementTextFormatter } from './text-formatter';

export const getStatementFieldConfig = ({
  mode = 'default',
  role,
  additionalConfig,
}: {
  mode?: string | null;
  role?: SystemRoles | null;
  additionalConfig?: { account_id?: string; timezone?: string };
}) => {
  const labels = new UILabels(mode);
  const textFormatter = new StatementTextFormatter(additionalConfig);

  return {
    label: labels.getLabel('cash', 'title'),
    labelSimple: labels.getLabel('cash', 'titleSimple'),
    table: 'statement_data',
    copyable: true,
    stickyColumns: ['policy_id'],
    filters: {
      transaction_type: {
        label: 'Transaction type',
      },
      group_name: {
        label: 'Group name',
      },
      reconciliation_status: {
        label: 'Reconciliation status',
      },
      agent_name: {
        label: 'Agent name',
        sortPosition: 10,
      },
      carrier_name: {
        label: 'Paying entity',
        sortPosition: 8,
      },
      compensation_type: {
        label: 'Compensation type',
        sortPosition: 4,
      },
      contacts: {
        label: 'Agents',
        sortPosition: 5,
      },
      flags: {
        label: 'Flags',
        sortPosition: 11,
      },
      tags: {
        label: 'Tags',
      },
      document_id: {
        label: 'Document',
        sortPosition: 9,
        listContainerSx: {
          width: 400,
        },
      },
      payment_status: {
        label: 'Payment status',
      },
      product_name: {
        label: 'Product name',
        sortPosition: 7,
      },
      product_type: {
        label: 'Product type',
        sortPosition: 6,
      },
      status: {
        label: 'Status',
      },
      writing_carrier_name: {
        label: 'Carrier/MGA',
        sortPosition: 3,
      },
      premium_type: {
        label: 'Premium type',
      },
      account_type: {
        label: 'Account type',
      },
      agent_commissions_status: {
        label: 'Payout status',
        sortPosition: 1,
      },
      agent_commissions_status2: {
        label: 'Payout status*',
      },
      comp_calc_status: {
        label: 'Agent payout status',
      },
      report_data_id: {
        label: 'Reconciliation',
        sortPosition: 2,
      },
      virtual_type: {
        label: 'Virtual type',
      },
    },
    dynamicSelectsConfig: [
      {
        table: 'companies',
        queryParamName: 'companyNames',
        queryParamValue: [], // Will be collected from data
        collectDataFields: ['writing_carrier_name', 'carrier_name'],
        mapKey: 'company_name',
      },
      {
        table: 'contacts',
        queryParamName: 'str_id',
        queryParamValue: [], // Will be collected from data
        collectDataFields: [
          'contacts',
          'agent_commissions',
          'agent_commissions_v2',
          'comp_calc',
          'comp_calc_log',
          'comp_calc_status',
          'agent_commissions_log',
          'agent_commissions_status2',
          'agent_payout_rate',
          'agent_commission_payout_rate',
          'agent_payout_rate_override',
        ],
        // Will be used to collect data for queryParamValue
        dataExtractors: {
          agent_commissions: (row, totals) => {
            return [
              ...new Set(
                Object.keys(row?.agent_commissions || {})
                  .filter((key) => key !== 'total')
                  .concat(Object.keys(totals?.agent_commissions || {}))
              ),
            ];
          },
          agent_payout_rate: (row) => {
            return Object.keys(row?.agent_payout_rate || {}).filter(
              (key) => key !== 'total'
            );
          },
        },
        mapKey: ['str_id', 'id'],
      },
      {
        table: 'documents',
        queryParamName: 'str_id',
        queryParamValue: [], // Will be collected from data
        collectDataFields: ['document_id'],
        mapKey: 'str_id',
      },
    ],
    dateFilters: [
      {
        filterFieldName: 'Payment date',
        filterFieldId: 'payment_date',
        filters: [
          {
            label: 'Payment date start',
            filterKey: 'payment_date_start',
          },
          {
            label: 'Payment date end',
            filterKey: 'payment_date_end',
          },
          { filterKey: 'payment_date_empty', type: FieldTypes.BOOLEAN },
        ],
      },
      {
        filterFieldName: 'Processing date',
        filterFieldId: 'processing_date',
        filters: [
          {
            label: 'Processing date start',
            filterKey: 'processing_date_start',
          },
          {
            label: 'Processing date end',
            filterKey: 'processing_date_end',
          },
          { filterKey: 'processing_date_empty', type: FieldTypes.BOOLEAN },
        ],
      },
      {
        filterFieldName: 'Statement date',
        filterFieldId: 'invoice_date',
        filters: [
          {
            label: 'Statement date start',
            filterKey: 'invoice_date_start',
          },
          {
            label: 'Statement date end',
            filterKey: 'invoice_date_end',
          },
          { filterKey: 'invoice_date_empty', type: FieldTypes.BOOLEAN },
        ],
      },
      {
        filterFieldName: 'Effective date',
        filterFieldId: 'effective_date',
        filters: [
          {
            label: 'Effective date start',
            filterKey: 'effective_date_start',
          },
          {
            label: 'Effective date end',
            filterKey: 'effective_date_end',
          },
          { filterKey: 'effective_date_empty', type: FieldTypes.BOOLEAN },
        ],
      },
    ],
    fields: {
      policy_id: {
        sticky: 'left',
        bulkEdit: true,
        label: labels.getLabel('cash', 'transactionId'),
        matches: [
          'policy id',
          'policy no',
          'policy no.',
          'policy number',
          'policy #',
          'policy',
          'contract #',
          'policy_number',
          'policyid',
        ],
        reconciler: true,
        enabled: true,
        subPolicyDataIfEmpty: true,
        copyable: true,
        textFormatter: policyDataIfEmptyTextFormatter(
          'policy_id',
          textFormatter.policyIdTextFormatter
        ),
      },
      commission_amount: {
        label: labels.getLabel('cash', 'amount'),
        matches: [
          'commission',
          'commission amount',
          'gross commission',
          'gross comm earned',
          'commission earned',
          'commission due',
          'amount',
          'comm amount',
          'comm amt',
          'commissionamount',
        ],
        enabled: true,
        type: FieldTypes.CURRENCY,
        textFormatter: formatCurrency,
      },
      commission_paid_amount: {
        label: 'Commissions paid',
        matches: ['commissions paid out', 'agent commission'],
        enabled: true,
        type: FieldTypes.CURRENCY,
        bulkEdit: true,
        textFormatter: formatCurrency,
      },
      advanced_commission_amount: {
        label: 'Advanced commissions paid',
        enabled: true,
        type: FieldTypes.CURRENCY,
        bulkEdit: true,
        textFormatter: formatCurrency,
      },
      customer_name: {
        bulkEdit: true,
        label: labels.getLabel('cash', 'customerName'),
        matches: [
          'customer name',
          'customer',
          'insuree',
          'insured',
          'client',
          'client name',
          'insured name',
          'policy holder',
          'policy holder name',
          'subscriber',
          'name of insured',
          'consumer name contract #',
          'named insured',
          'customername',
          'policyholder name',
          'individual name',
        ],
        reconciler: true,
        enabled: true,
        style: {
          pl: 2,
        },
        textFormatter: policyDataIfEmptyTextFormatter(
          'customer_name',
          textFormatter.textFormatter
        ),
      },
      invoice_date: {
        label: labels.getLabel('cash', 'invoiceDate'),
        matches: ['invoice date', 'invoice received', 'invoiceDate'],
        enabled: true,
        global: true,
        type: FieldTypes.DATE,
        bulkEdit: true,
        textFormatter: formatDate,
      },
      payment_date: {
        bulkEdit: true,
        label: labels.getLabel('cash', 'paymentDate'),
        matches: [
          'payment date',
          'payment received',
          'statement date',
          'month billed',
          'paymentdate',
        ],
        enabled: true,
        global: true,
        type: FieldTypes.DATE,
        width: 216,
        textFormatter: formatDate,
      },
      processing_date: {
        bulkEdit: true,
        label: labels.getLabel('cash', 'processingDate'),
        matches: ['processing date', 'processingDate'],
        enabled: true,
        global: true,
        type: FieldTypes.DATE,
        textFormatter: formatDate,
      },
      agent_name: {
        label: labels.getLabel('cash', 'repName'),
        matches: [
          'assigned agent',
          'agt',
          'writing agent',
          'writing agt',
          'agent name',
          'agt name',
          'assigned_agent',
          'agent',
          'producer name',
        ],
        enabled: mode === 'insurance',
        global: true,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      statement_number: {
        label: labels.getLabel('cash', 'invoiceNumber'),
        matches: [
          'statement number',
          'commission statement number',
          'statementnumber',
        ],
        enabled: true,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      transaction_type: {
        bulkEdit: true,
        label: labels.getLabel('cash', 'transactionType'),
        matches: [
          'transaction type',
          'commission type',
          'policy type',
          'transactiontype',
        ],
        subPolicyDataIfEmpty: true,
        enabled: true,
        textFormatter: policyDataIfEmptyTextFormatter('transaction_type'),
      },
      writing_carrier_name: {
        bulkEdit: true,
        label: labels.getLabel('cash', 'entity'),
        matches: ['carrier name', 'carrier', 'writingcarriername'],
        enabled: mode === 'insurance',
        reconciler: true,
        global: true,
        width: 243,
        type: FieldTypes.CUSTOM,
        queryParamName: 'company_name',
        textFormatter: textFormatter.writingCarrierNameTextFormatter,
      },
      writing_carrier_name_relation: {
        label: `${labels.getLabel('cash', 'entity')} ID`,
        enabled: mode === 'insurance',
        ref: 'writing_carrier_name',
        exportOnly: true,
        isRelationalField: true,
        textFormatter: textFormatter.writingCarrierIdTextFormatter,
      },
      premium_type: {
        label: labels.getLabel('cash', 'premiumType'),
        type: FieldTypes.SELECT,
        matches: [],
        options: ['policy', 'split'],
        reconciler: true,
        enabled: mode === 'insurance',
        global: true,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      carrier_name: {
        bulkEdit: true,
        label: labels.getLabel('cash', 'payingEntity'),
        matches: ['carrier name', 'carrier', 'carriername'],
        enabled: true,
        global: true,
        width: 350,
        type: FieldTypes.CUSTOM,
        textFormatter: textFormatter.writingCarrierNameTextFormatter,
      },
      agent_id: {
        label: labels.getLabel('cash', 'repId'),
        matches: [
          'agent id',
          'agt id',
          'agent no',
          'agt no',
          'writing agent #',
          'writing agent no',
          'writing agent id',
          'writing agt #',
          'writing agt no',
          'writing agt id',
          'agentid',
        ],
        enabled: mode === 'insurance',
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      premium_amount: {
        label: labels.getLabel('cash', 'annualizedPremium'),
        description:
          'Annualized premium amount as recorded in commission statements',
        matches: [
          'premium amount',
          'premium amt',
          'premium paid',
          'premium',
          'premium - annualized',
          'annualized_premium',
          'annualized_premium2',
          'premiumamount',
          'policy premium',
          'Paid Prem',
        ],
        enabled: mode === 'insurance',
        type: FieldTypes.CURRENCY,
        textFormatter: formatCurrency,
      },
      expected_result: {
        label: 'Expected result',
        enabled: true,
        readOnly: true,
        textFormatter: textFormatter.textFormatter,
      },
      commission_rate: {
        // Number
        label: labels.getLabel('cash', 'rate'),
        matches: [
          'commission rate',
          'commission pct',
          'commission %',
          'commission perentage',
          'comm %',
          'commissionrate',
        ],
        enabled: mode === 'insurance',
        type: FieldTypes.PERCENTAGE,
        textFormatter: formatPercentage,
      },
      carrier_rate: {
        label: 'Carrier rate',
        matches: [],
        enabled: mode === 'insurance',
        type: FieldTypes.PERCENTAGE,
        bulkEdit: true,
        textFormatter: formatPercentage,
      },
      receivable_value_agency_rate: buildReceivableValueFilter(
        TransactionParty.AGENCY
      ),
      receivable_value_agent_rate: buildReceivableValueFilter(
        TransactionParty.AGENT
      ),
      receivable_value_override_rate: buildReceivableValueFilter(
        TransactionParty.POLICY
      ),
      // Rows - Optional
      effective_date: {
        // Date
        bulkEdit: true,
        label: labels.getLabel('cash', 'startDate'),
        matches: [
          'policy effective date',
          'effective date',
          'effective',
          'policy effective',
          'in force date',
          'eff date',
          'effective_date',
          'effective_date2',
          'effectivedate',
        ],
        enabled: true,
        type: FieldTypes.DATE,
        textFormatter: policyDataIfEmptyTextFormatter(
          'effective_date',
          formatDate
        ),
      },
      product_type: {
        label: labels.getLabel('cash', 'productType'),
        matches: ['product type', 'product line', 'producttype'],
        reconciler: true,
        enabled: mode === 'insurance',
        global: true,
        bulkEdit: true,
        textFormatter: policyDataIfEmptyTextFormatter('product_type'),
      },
      product_sub_type: {
        label: 'Product sub type',
        matches: [],
        reconciler: true,
        enabled: mode === 'insurance',
        global: true,
        bulkEdit: true,
        textFormatter: policyDataIfEmptyTextFormatter('product_sub_type'),
      },
      product_name: {
        bulkEdit: true,
        label: labels.getLabel('cash', 'productName'),
        matches: ['product name', 'product', 'policy name'],
        enabled: mode === 'insurance',
        subPolicyDataIfEmpty: true,
        textFormatter: policyDataIfEmptyTextFormatter('product_name'),
      },
      product_option_name: {
        label: 'Product option name',
        matches: ['option', 'product option'],
        enabled: mode === 'insurance',
        subPolicyDataIfEmpty: true,
        bulkEdit: true,
        textFormatter: policyDataIfEmptyTextFormatter('product_option_name'),
      },
      fees: {
        label: labels.getLabel('cash', 'fees'),
        matches: ['fees'],
        enabled: mode === 'insurance',
        type: FieldTypes.CURRENCY,
        bulkEdit: true,
        textFormatter: formatCurrency,
      },
      group_id: {
        label: labels.getLabel('cash', 'groupId'),
        matches: [
          'group id',
          'group number',
          'group no',
          'grp #',
          'grp number',
          'groupid',
        ],
        enabled: mode === 'insurance',
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      internal_id: {
        label: 'Internal ID',
        enabled: true,
        subPolicyDataIfEmpty: true,
        bulkEdit: true,
        textFormatter: policyDataIfEmptyTextFormatter('internal_id'),
      },
      period_date: {
        bulkEdit: true,
        label: labels.getLabel('cash', 'periodDate'),
        matches: ['period', 'coverage period', 'perioddate', 'due date'],
        enabled: mode === 'insurance',
        type: FieldTypes.DATE,
        global: true,
        textFormatter: formatDate,
      },
      status: {
        bulkEdit: true,
        label: 'Status',
        enabled: true,
        textFormatter: textFormatter.textFormatter,
      },
      performance_bonus: {
        label: 'Performance bonus',
        matches: ['performance bonus', 'performancebonus'],
        enabled: false,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      override: {
        label: 'Override',
        matches: ['override'],
        enabled: false,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      payee: {
        label: 'Payee',
        matches: ['payee', 'payee name'],
        enabled: false,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      payee_id: {
        label: 'Payee ID',
        matches: ['payee id', 'payee no', 'payeeid'],
        enabled: false,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      agent_level: {
        label: 'Agent level',
        matches: ['agent level', 'writing agent level', 'agentlevel'],
        enabled: false,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      agency: {
        label: 'Agency',
        matches: ['agency'],
        enabled: false,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      policy_issue_date: {
        label: 'Policy issue date',
        matches: ['policy issue date', 'issue date', 'policyissuedate'],
        type: FieldTypes.DATE,
        enabled: false,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      policy_amount: {
        label: 'Policy amount',
        matches: [
          'policy amount',
          'original face amount',
          'face amount',
          'policyamount',
        ],
        enabled: false,
        type: FieldTypes.CURRENCY,
        bulkEdit: true,
        textFormatter: formatCurrency,
      },
      policy_term: {
        label: 'Policy term',
        matches: ['policy term', 'policyterm'],
        enabled: false,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      premium_received: {
        // Number
        label: 'Premium received',
        matches: ['premium received', 'premiumreceived'],
        enabled: false,
        type: FieldTypes.CURRENCY,
        bulkEdit: true,
        textFormatter: formatCurrency,
      },
      compensation_type: {
        label: 'Compensation type',
        matches: ['compensation type', 'compensationtype'],
        enabled: mode === 'insurance',
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      income_class: {
        label: 'Income class',
        matches: ['income class', 'tax form', 'incomeclass'],
        enabled: false,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      billing_frequency: {
        label: 'Billing frequency',
        matches: ['billing frequency', 'billingfrequency'],
        enabled: false,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      premium_transaction: {
        label: 'Premium transaction',
        matches: ['premium transaction', 'premiumtransaction'],
        enabled: false,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      commissionable_premium_amount: {
        label: 'Target premium',
        matches: ['target premium', 'commissionable premium'],
        enabled: true,
        type: FieldTypes.CURRENCY,
        bulkEdit: true,
        textFormatter: policyDataIfEmptyTextFormatter(
          'commissionable_premium_amount',
          formatCurrency
        ),
      },
      account_type: {
        label: 'Account type',
        enabled: true,
        matches: ['account type', 'accounttype'],
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      issue_age: {
        label: 'Issue age',
        enabled: true,
        matches: ['issue age'],
        type: FieldTypes.INTEGER,
        subPolicyDataIfEmpty: true,
        bulkEdit: true,
        textFormatter: policyDataIfEmptyTextFormatter('issue_age'),
      },
      customer_paid_premium_amount: {
        label: 'Customer Paid Premium Amount',
        enabled: true,
        matches: ['Basis', 'customer paid premium amount'],
        bulkEdit: true,
        textFormatter: formatCurrency,
      },
      bill_mode: {
        label: 'Bill mode',
        enabled: true,
        matches: ['bill mode'],
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      geo_state: {
        label: 'State',
        type: FieldTypes.SELECT,
        options: States,
        matches: [],
        enabled: true,
        bulkEdit: true,
        textFormatter: policyDataIfEmptyTextFormatter(
          'geo_state',
          textFormatter.geoStateTextFormatter
        ),
      },
      split_percentage: {
        label: 'Split percentage',
        enabled: true,
        matches: ['split percentage'],
        type: FieldTypes.PERCENTAGE,
        bulkEdit: true,
        textFormatter: textFormatter.splitPercentageTextFormatter,
      },
      group_name: {
        label: 'Group name',
        matches: ['group name'],
        required: false,
        enabled: true,
        subPolicyDataIfEmpty: true,
        bulkEdit: true,
        textFormatter: policyDataIfEmptyTextFormatter('group_name'),
      },
      payment_mode: {
        label: 'Payment mode',
        matches: ['payment mode'],
        required: false,
        enabled: true,
        bulkEdit: true,
        textFormatter: policyDataIfEmptyTextFormatter('payment_mode'),
      },
      aggregation_id: {
        label: 'Aggregation ID',
        matches: [],
        enabled: true,
        subPolicyDataIfEmpty: true,
        copyable: true,
        bulkEdit: true,
        textFormatter: textFormatter.aggregationIdTextFormatter,
      },
      member_count: {
        label: 'Member count',
        matches: ['member count'],
        enabled: true,
        type: FieldTypes.INTEGER,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      commission_basis: {
        label: 'Commission basis',
        matches: ['commission basis'],
        enabled: true,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      standardized_customer_name: {
        label: 'Standardized customer name',
        matches: ['standardized customer name'],
        enabled: true,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      contacts: {
        bulkEdit: true,
        label: 'Agents',
        enabled: true,
        type: FieldTypes.CUSTOM,
        multiple: true,
        textFormatter: textFormatter.contactsTextFormatter,
      },
      contacts_id: {
        label: 'Agents IDs',
        exportOnly: true,
        isRelationalField: true,
        enabled: true,
        textFormatter: textFormatter.contactsIdTextFormatter,
        ref: 'contacts',
      },
      agent_commissions: {
        label: 'Agent commissions',
        enabled: true,
        type: FieldTypes.CUSTOM,
        bulkEdit: true,
        textFormatter: textFormatter.agentCommissionsTextFormatter,
      },
      agent_commissions_v2: {
        label: 'Agent commissions V2',
        enabled: true,
        type: FieldTypes.CUSTOM,
        readOnly: true,
        textFormatter: textFormatter.agentCommissionsTextFormatter,
      },
      comp_calc: {
        label: 'Agent compensation',
        enabled: true,
        type: FieldTypes.CUSTOM,
        bulkEdit: true,
        textFormatter: textFormatter.compCalcTextFormatter,
      },
      comp_calc_log: {
        label: 'Agent compensation log',
        enabled: true,
        readOnly: true,
        textFormatter: textFormatter.compCalcLogTextFormatter,
      },
      comp_calc_status: {
        label: 'Agent payout status',
        enabled: true,
        type: FieldTypes.CUSTOM,
        textFormatter: textFormatter.compCalcStatusTextFormatter,
      },
      agent_commissions_log: {
        label: 'Agent commissions log',
        enabled: true,
        readOnly: true,
        textFormatter: textFormatter.agentCommissionsLogTextFormatter,
      },
      flags: {
        label: 'Flags',
        enabled: true,
        readOnly: true,
        type: FieldTypes.CUSTOM,
        textFormatter: textFormatter.flagsTextFormatter,
      },
      flags_log: {
        label: 'Flags log',
        enabled: true,
        readOnly: true,
        type: FieldTypes.CUSTOM,
        textFormatter: textFormatter.flagsLogTextFormatter,
      },
      agent_commissions_status: {
        bulkEdit: true,
        label: 'Payout status',
        enabled: true,
        type: FieldTypes.SELECT,
        options: Object.values(AgentCommissionsStatusesLabels),
        textFormatter: textFormatter.textFormatter,
      },
      agent_commissions_status2: {
        label: 'Payout status*',
        enabled: true,
        type: FieldTypes.CUSTOM,
        textFormatter: textFormatter.agentCommissionsStatus2TextFormatter,
      },
      notes: {
        bulkEdit: true,
        label: 'Notes',
        enabled: true,
        matches: ['RevCd', 'notes'],
        subPolicyDataIfEmpty: true,
        textFormatter: policyDataIfEmptyTextFormatter('notes'),
      },
      tags: {
        label: 'Tags',
        enabled: true,
        bulkEdit: true,
        textFormatter: textFormatter.tagsTextFormatter,
      },
      document_id: {
        bulkEdit: true,
        width: 300,
        label: 'Document',
        matches: ['document id', 'document'],
        enabled: true,
        required: false,
        type: FieldTypes.CUSTOM,
        queryParamValue: 'statement',
        queryParamName: 'type',
        textFormatter: textFormatter.documentIdTextFormatter,
      },
      document_id_relation: {
        label: 'Document ID',
        enabled: true,
        ref: 'document_id',
        exportOnly: true,
        isRelationalField: true,
        textFormatter: textFormatter.documentIdRelationTextFormatter,
      },
      processing_status: {
        label: 'Processing status',
        options: ['new', 'processed', 'frozen'],
        matches: [],
        type: FieldTypes.SELECT,
        enabled: true,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      children_data: {
        label: 'Linked records',
        enabled: true,
        type: FieldTypes.CUSTOM,
        readOnly: true,
        textFormatter: textFormatter.childrenDataTextFormatter,
      },
      details: {
        label: 'Payment allocations',
        enabled: true,
        type: FieldTypes.CUSTOM,
        readOnly: true,
        textFormatter: textFormatter.childrenDataTextFormatter,
      },
      payment_status: {
        label: 'Payment status',
        enabled: true,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      agent_payout_rate: {
        label: 'Agent payout rate',
        enabled: true,
        type: FieldTypes.CUSTOM,
        bulkEdit: true,
        textFormatter: textFormatter.agentPayoutRateTextFormatter,
      },
      agent_payout_rate_override: {
        label: 'Agent payout rate override',
        enabled: true,
        type: FieldTypes.CUSTOM,
        subPolicyDataIfEmpty: true,
        bulkEdit: true,
        textFormatter: textFormatter.agentPayoutRateOverrideTextFormatter,
      },
      agent_commission_payout_rate: {
        label: 'Agent commission payout rate',
        enabled: true,
        type: FieldTypes.CUSTOM,
        textFormatter: textFormatter.agentCommissionPayoutRateTextFormatter,
      },
      report_data_id: {
        label: 'Linked policy',
        enabled: true,
        readOnly: true,
        type: FieldTypes.CUSTOM,
        textFormatter: textFormatter.reportDataIdTextFormatter,
      },
      reconciliation_status: {
        label: 'Commission status',
        enabled: true,
        readOnly: true,
        type: FieldTypes.TEXT,
      },
      is_virtual: {
        label: 'Virtual',
        enabled: true,
        readOnly: true,
        type: FieldTypes.BOOLEAN,
        textFormatter: textFormatter.booleanFormatter,
      },
      virtual_type: {
        label: 'Virtual type',
        enabled: true,
        readOnly: true,
        type: FieldTypes.TEXT,
        textFormatter: textFormatter.textFormatter,
      },
      reconciliation_method: {
        label: 'Reconciler 🔒',
        enabled: role === SystemRoles.ADMIN,
        readOnly: true,
        type: FieldTypes.DYNAMIC_SELECT,
        table: 'reconcilers',
        textFormatter: textFormatter.reconciliationMethodTextFormatter,
      },
      state: {
        label: 'Is grouped 🔒',
        enabled: role === SystemRoles.ADMIN,
        readOnly: true,
        type: FieldTypes.TEXT,
        textFormatter: CommonFormatter.isGrouped,
      },
      str_id: {
        label: 'ID',
        enabled: true,
        exportOnly: true,
        isRelationalField: true,
        textFormatter: textFormatter.textFormatter,
      },
    },
    outstandingFieldsInMobileView: [
      'policy_id',
      'commission_amount',
      'customer_name',
      'payment_date',
      'carrier_name',
      'premium_amount',
      'agent_payout_rate',
    ],
  };
};
