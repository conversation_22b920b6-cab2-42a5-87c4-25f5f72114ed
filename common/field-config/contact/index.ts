import { FieldTypes } from 'common/constants';
import { capitalize } from 'lodash-es';
import { formatCurrency } from 'common/helpers/DataTransformation/formatter';

import { ContactTextFormatter } from './text-formatter';

export const getContactFieldConfig = ({
  options,
}: {
  options?: { isAccountTransactionsEnabled?: boolean };
} = {}) => {
  const textFormatter = new ContactTextFormatter();
  return {
    label: 'Agents',
    table: 'contacts?is_detailed_view=true&hierarchy_depth=1',
    editable: true,
    copyable: true,
    bulkAdd: true,
    extraActions: [],
    fields: {
      first_name: {
        label: 'First name',
        required: true,
        queryable: true,
        enabled: true,
        textFormatter: textFormatter.textFormatter,
      },
      last_name: {
        label: 'Last name',
        queryable: true,
        enabled: true,
        textFormatter: textFormatter.textFormatter,
      },
      nickname: {
        label: 'Nickname',
        queryable: true,
        enabled: true,
        textFormatter: textFormatter.textFormatter,
      },
      email: {
        label: 'Email',
        isUnique: true,
        existingVals: [],
        queryable: true,
        enabled: true,
        textFormatter: textFormatter.textFormatter,
      },
      phone: {
        label: 'Phone',
        queryable: true,
        enabled: true,
        textFormatter: textFormatter.textFormatter,
      },
      agent_code: {
        label: 'Agent code',
        queryable: true,
        enabled: true,
        textFormatter: textFormatter.textFormatter,
      },
      bank_info: {
        label: 'Bank info',
        enabled: true,
        textFormatter: textFormatter.textFormatter,
      },
      contact_group_id: {
        label: 'Agent group',
        type: FieldTypes.DYNAMIC_SELECT,
        table: 'contacts/groups',
        field: 'id',
        nullable: true,
        queryable: true,
        tip: 'Group can only be configured for the root agent',
        readOnly: (row) => ![null, undefined].includes(row?.parent_id),
        enabled: true,
        textFormatter: textFormatter.contactGroupTextFormatter,
      },
      company_name: {
        label: 'Company name',
        queryable: true,
        nullable: true,
        enabled: true,
        textFormatter: textFormatter.textFormatter,
      },
      type: {
        label: 'Type',
        type: FieldTypes.SELECT,
        options: ['Agent', 'Sales rep', 'IMO'],
        strToValue: (vals) => vals?.split(',')?.map((v) => v.trim()),
        queryable: true,
        enabled: true,
        textFormatter: textFormatter.textFormatter,
      },
      status: {
        label: 'Status',
        type: FieldTypes.SELECT,
        queryable: true,
        enabled: true,
        textFormatter: textFormatter.textFormatter,
      },
      payable_status: {
        label: 'Payable status',
        queryable: true,
        type: FieldTypes.SELECT,
        enabled: true,
        textFormatter: capitalize,
      },
      contact_level: {
        label: 'Carrier grid levels',
        type: FieldTypes.CUSTOM,
        enabled: true,
        textFormatter: textFormatter.contactLevelTextFormatter,
      },
      contact_level_relation: {
        label: 'Carrier grid levels IDs',
        enabled: true,
        isRelationalField: true,
        exportOnly: true,
        textFormatter: textFormatter.extractIdsTextFormatter('contact_level'),
        ref: 'contact_level',
      },
      agency_contact_levels: {
        label: 'Agency grid levels',
        type: FieldTypes.CUSTOM,
        enabled: true,
        textFormatter: textFormatter.contactLevelTextFormatter,
      },
      agency_contact_levels_relation: {
        label: 'Agency grid levels IDs',
        enabled: true,
        isRelationalField: true,
        exportOnly: true,
        textFormatter: textFormatter.extractIdsTextFormatter(
          'agency_contact_levels'
        ),
        ref: 'agency_contact_levels',
      },
      parent_relationships: {
        label: 'Uplines',
        type: FieldTypes.DYNAMIC_SELECT,
        table: 'contacts',
        multiple: true,
        bulkAddUnsupported: true,
        enabled: true,
        textFormatter: textFormatter.uplinesTextFormatter,
      },
      parent_relationships_relation: {
        label: 'Uplines IDs',
        enabled: true,
        isRelationalField: true,
        exportOnly: true,
        textFormatter: textFormatter.extractIdsTextFormatter(
          'parent_relationships'
        ),
        ref: 'parent_relationships',
      },
      child_relationships: {
        label: 'Downlines',
        type: FieldTypes.SELECT,
        multiple: true,
        readOnly: true,
        bulkAddUnsupported: true,
        linker: (val) => `/agents/list?id=${val?.contact?.str_id}`,
        enabled: true,
        textFormatter: textFormatter.downlinesTextFormatter,
      },
      child_relationships_relation: {
        label: 'Downlines IDs',
        enabled: true,
        isRelationalField: true,
        exportOnly: true,
        textFormatter: textFormatter.extractIdsTextFormatter(
          'child_relationships'
        ),
        ref: 'child_relationships',
      },
      contacts_agent_commission_schedule_profiles_sets: {
        label: 'Comp profile sets',
        type: FieldTypes.DYNAMIC_SELECT,
        table: 'schedules/comp-profiles/sets',
        multiple: true,
        field: 'id',
        disableSort: true,
        linker: (val) =>
          `/schedules/comp-profile-sets?id=${val?.agent_commission_schedule_profiles_sets?.str_id}`,
        bulkAddUnsupported: true,
        enabled: true,
        textFormatter: textFormatter.compProfileSetsTextFormatter,
      },
      contacts_agent_commission_schedule_profiles_sets_relation: {
        label: 'Comp profile sets IDs',
        enabled: true,
        isRelationalField: true,
        exportOnly: true,
        textFormatter: textFormatter.extractIdsTextFormatter(
          'contacts_agent_commission_schedule_profiles_sets'
        ),
        ref: 'contacts_agent_commission_schedule_profiles_sets',
      },
      contacts_agent_commission_schedule_profiles: {
        label: 'Custom comp profiles',
        type: FieldTypes.DYNAMIC_SELECT,
        table: 'schedules/comp-profiles',
        multiple: true,
        field: 'id',
        disableSort: true,
        linker: (val) =>
          `/schedules/comp-profiles?id=${val?.agent_commission_schedule_profile?.str_id}`,
        bulkAddUnsupported: true,
        enabled: true,
        textFormatter: textFormatter.compProfilesTextFormatter,
      },
      contacts_agent_commission_schedule_profiles_relation: {
        label: 'Custom comp profiles IDs',
        enabled: true,
        isRelationalField: true,
        exportOnly: true,
        textFormatter: textFormatter.extractIdsTextFormatter(
          'contacts_agent_commission_schedule_profiles'
        ),
        ref: 'contacts_agent_commission_schedule_profiles',
      },
      contact_memos: {
        label: 'Memos',
        type: FieldTypes.CUSTOM,
        multiple: true,
        field: 'id',
        bulkAddUnsupported: true,
        enabled: true,
        textFormatter: textFormatter.displayLengthTextFormatter,
      },
      contact_referrals: {
        label: 'Referrals',
        type: FieldTypes.CUSTOM,
        multiple: true,
        field: 'id',
        bulkAddUnsupported: true,
        enabled: true,
        textFormatter: textFormatter.displayLengthTextFormatter,
      },
      notes: {
        label: 'Notes',
        queryable: true,
        enabled: true,
        textFormatter: textFormatter.textFormatter,
      },
      saved_reports: {
        label: 'Saved reports',
        readOnly: true,
        bulkAddUnsupported: true,
        type: FieldTypes.SELECT,
        multiple: true,
        enabled: true,
        textFormatter: textFormatter.savedReportsTextFormatter,
      },
      saved_reports_relation: {
        label: 'Saved reports IDs',
        enabled: true,
        isRelationalField: true,
        exportOnly: true,
        textFormatter: textFormatter.extractIdsTextFormatter('saved_reports'),
        ref: 'saved_reports',
      },
      user_contact: {
        label: 'Fintary access',
        tip: 'To grant access, go to Settings > User manager.',
        readOnly: true,
        bulkAddUnsupported: true,
        queryable: true,
        enabled: true,
        textFormatter: textFormatter.userContactTextFormatter,
      },
      str_id: {
        label: 'Agent ID',
        queryable: true,
        readOnly: true,
        condition: (val) => val.str_id,
        enabled: true,
        textFormatter: textFormatter.textFormatter,
      },
      sync_id: {
        label: 'Sync ID',
        readOnly: true,
        type: FieldTypes.TEXT,
        enabled: true,
        textFormatter: textFormatter.textFormatter,
      },
      account_role_settings_id: {
        label: 'Custom view',
        type: FieldTypes.DYNAMIC_SELECT,
        table: 'account_role_settings_producer',
        field: 'id',
        nullable: true,
        queryable: true,
        enabled: true,
        textFormatter: textFormatter.accountRoleSettingsTextFormatter,
      },
      account_role_settings_id_relation: {
        label: 'Custom view ID',
        enabled: true,
        isRelationalField: true,
        exportOnly: true,
        textFormatter: textFormatter.extractIdsTextFormatter(
          'account_role_settings_id'
        ),
        ref: 'account_role_settings_id',
      },
      accounting_transactions: {
        label: 'Transactions',
        type: FieldTypes.CUSTOM,
        enabled: options?.isAccountTransactionsEnabled,
        textFormatter: textFormatter.displayLengthTextFormatter,
      },
      balance: {
        label: 'Balance',
        type: FieldTypes.CURRENCY,
        readOnly: true,
        enabled: options?.isAccountTransactionsEnabled,
        textFormatter: formatCurrency,
      },
    },
  };
};
