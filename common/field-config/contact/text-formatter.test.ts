import { describe, it, expect, beforeEach } from 'vitest';

import { ContactTextFormatter } from './text-formatter';

const mockContact = {
  id: 123,
  str_id: 'contact_123',
  first_name: '<PERSON>',
  last_name: '<PERSON><PERSON>',
  email: '<EMAIL>',
  phone: '************',
};

const mockContactLevelData = [
  {
    level_label: 'Manager',
    product_type: 'Life Insurance',
    loa: true,
  },
  {
    level_label: 'Director',
    product_type: 'Health Insurance',
    loa: false,
  },
  {
    product_type: 'Auto Insurance',
  },
];

const mockUplinesData = [
  { parent: mockContact },
  {
    parent: {
      ...mockContact,
      id: 124,
      str_id: 'contact_124',
      first_name: '<PERSON>',
      last_name: '<PERSON>',
    },
  },
];

const mockDownlinesData = [
  { contact: mockContact },
  {
    contact: {
      ...mockContact,
      id: 125,
      str_id: 'contact_125',
      first_name: '<PERSON>',
      last_name: '<PERSON>',
    },
  },
];

const mockCompProfileSetsData = [
  {
    agent_commission_schedule_profiles_sets: {
      name: 'Standard Commission Set',
    },
  },
  {
    agent_commission_schedule_profiles_sets: {
      name: 'Premium Commission Set',
    },
  },
  {
    // Missing agent_commission_schedule_profiles_sets
  },
];

const mockCompProfilesData = [
  {
    agent_commission_schedule_profile: {
      name: 'Basic Profile',
    },
  },
  {
    agent_commission_schedule_profile: {
      name: 'Advanced Profile',
    },
  },
  {
    // Missing agent_commission_schedule_profile
  },
];

const mockSavedReportsData = [
  { name: 'Monthly Report' },
  { name: 'Quarterly Report' },
  { name: 'Annual Report' },
];

const mockRowWithUserContact = {
  account_id: 'account_123',
  user_contact: {
    state: 'active',
    account_user_roles: [
      { account_id: 'account_123', state: 'admin' },
      { account_id: 'account_456', state: 'user' },
    ],
  },
};

const mockRowWithContactLevel = {
  contact_level: [
    { id: 'level_1', str_id: 'STR_LEVEL_1' },
    { id: 'level_2', str_id: 'STR_LEVEL_2' },
    { str_id: 'STR_LEVEL_3' }, // No id
    { id: 'level_4' }, // No str_id
  ],
};

const mockRowWithAccountRoleSettings = {
  account_role_settings: {
    custom_view_name: 'Custom view',
  },
};

const mockRowWithSavedReports = {
  saved_reports: mockSavedReportsData,
};

describe('ContactTextFormatter', () => {
  let formatter: ContactTextFormatter;

  beforeEach(() => {
    formatter = new ContactTextFormatter();
  });

  describe('contactLevelTextFormatter', () => {
    it('Given contact levels with level_labels, should format as comma separated values', () => {
      const result = formatter.contactLevelTextFormatter(mockContactLevelData);
      expect(result).toBe(
        'Manager, Life Insurance, LOA, Director, Health Insurance, Auto Insurance'
      );
    });

    it('Given empty array, should return empty string', () => {
      const result = formatter.contactLevelTextFormatter([]);
      expect(result).toBe('');
    });

    it('Given non-array input, should return empty string', () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const result = formatter.contactLevelTextFormatter(null as any);
      expect(result).toBe('');
    });

    it('Given contact levels with partial fields, should format available fields only', () => {
      const data = [
        { level_label: 'Manager' },
        { product_type: 'Insurance' },
        { loa: true },
      ];
      const result = formatter.contactLevelTextFormatter(data);
      expect(result).toBe('Manager, Insurance, LOA');
    });

    it('Given contact levels with no relevant fields, should return empty string', () => {
      const data = [{ some_other_field: 'value' }, {}];
      const result = formatter.contactLevelTextFormatter(data);
      expect(result).toBe('');
    });
  });

  describe('uplinesTextFormatter', () => {
    it('Given uplines with parent contacts, should format as newline separated names', () => {
      const result = formatter.uplinesTextFormatter(mockUplinesData);
      expect(result).toContain('John Doe');
      expect(result).toContain('Jane Smith');
      expect(result).toContain('\n');
    });

    it('Given empty array of uplines, should return empty string', () => {
      const result = formatter.uplinesTextFormatter([]);
      expect(result).toBe('');
    });

    it('Given uplines with missing parent data, should format available contacts only', () => {
      const data = [{ parent: undefined }, { parent: mockContact }];
      const result = formatter.uplinesTextFormatter(data);
      expect(result).toContain('John Doe');
    });
  });

  describe('downlinesTextFormatter', () => {
    it('Given downlines with contact data, should format as newline separated names', () => {
      const result = formatter.downlinesTextFormatter(mockDownlinesData);
      expect(result).toContain('John Doe');
      expect(result).toContain('Bob Johnson');
      expect(result).toContain('\n');
    });

    it('Given empty array of downlines, should return empty string', () => {
      const result = formatter.downlinesTextFormatter([]);
      expect(result).toBe('');
    });

    it('Given downlines with missing contact data, should format available contacts only', () => {
      const data = [{ contact: undefined }, { contact: mockContact }];
      const result = formatter.downlinesTextFormatter(data);
      expect(result).toContain('John Doe');
    });
  });

  describe('compProfileSetsTextFormatter', () => {
    it('Given commission profile sets with names, should format as newline separated values', () => {
      const result = formatter.compProfileSetsTextFormatter(
        mockCompProfileSetsData
      );
      expect(result).toBe(
        'Standard Commission Set\nPremium Commission Set\nNot found'
      );
    });

    it('Given empty array of profile sets, should return empty string', () => {
      const result = formatter.compProfileSetsTextFormatter([]);
      expect(result).toBe('');
    });

    it('Given null input, should return empty string', () => {
      const result = formatter.compProfileSetsTextFormatter(null);
      expect(result).toBe('');
    });

    it('Given undefined input, should return empty string', () => {
      const result = formatter.compProfileSetsTextFormatter(undefined);
      expect(result).toBe('');
    });
  });

  describe('compProfilesTextFormatter', () => {
    it('Given commission profiles with names, should format as newline separated values', () => {
      const result = formatter.compProfilesTextFormatter(mockCompProfilesData);
      expect(result).toBe('Basic Profile\nAdvanced Profile\nNot found');
    });

    it('Given empty array of profiles, should return empty string', () => {
      const result = formatter.compProfilesTextFormatter([]);
      expect(result).toBe('');
    });

    it('Given null input, should return empty string', () => {
      const result = formatter.compProfilesTextFormatter(null);
      expect(result).toBe('');
    });

    it('Given undefined input, should return empty string', () => {
      const result = formatter.compProfilesTextFormatter(undefined);
      expect(result).toBe('');
    });
  });

  describe('displayLengthTextFormatter', () => {
    it('Given array with items, should return item count', () => {
      const result = formatter.displayLengthTextFormatter([1, 2, 3, 4, 5]);
      expect(result).toBe(5);
    });

    it('Given empty array, should return empty string', () => {
      const result = formatter.displayLengthTextFormatter([]);
      expect(result).toBe('');
    });

    it('Given null input, should return empty string', () => {
      const result = formatter.displayLengthTextFormatter(null);
      expect(result).toBe('');
    });

    it('Given undefined input, should return empty string', () => {
      const result = formatter.displayLengthTextFormatter(undefined);
      expect(result).toBe('');
    });
  });

  describe('savedReportsTextFormatter', () => {
    it('Given saved reports with names, should format as newline separated values', () => {
      const result = formatter.savedReportsTextFormatter(mockSavedReportsData);
      expect(result).toBe('Monthly Report\nQuarterly Report\nAnnual Report');
    });

    it('Given empty array of reports, should return empty string', () => {
      const result = formatter.savedReportsTextFormatter([]);
      expect(result).toBe('');
    });

    it('Given null input, should return empty string', () => {
      const result = formatter.savedReportsTextFormatter(null);
      expect(result).toBe('');
    });

    it('Given reports with missing names, should format available names only', () => {
      const data = [{ name: 'Report 1' }, {}, { name: 'Report 3' }];
      const result = formatter.savedReportsTextFormatter(data);
      expect(result).toBe('Report 1\n\nReport 3');
    });
  });

  describe('userContactTextFormatter', () => {
    it('Given user contact with matching account, should return user role state', () => {
      const result = formatter.userContactTextFormatter(
        [{}],
        mockRowWithUserContact
      );
      expect(result).toBe('admin');
    });

    it('Given user contact with non-matching account, should return "No access"', () => {
      const rowWithDifferentAccount = {
        ...mockRowWithUserContact,
        account_id: 'different_account',
      };
      const result = formatter.userContactTextFormatter(
        [{}],
        rowWithDifferentAccount
      );
      expect(result).toBe('No access');
    });

    it('Given null value, should return "No access"', () => {
      const result = formatter.userContactTextFormatter(
        null,
        mockRowWithUserContact
      );
      expect(result).toBe('No access');
    });

    it('Given row without user contact, should return "No access"', () => {
      const rowWithoutUserContact = { account_id: 'account_123' };
      const result = formatter.userContactTextFormatter(
        [{}],
        rowWithoutUserContact
      );
      expect(result).toBe('No access');
    });

    it('Given user contact without account roles, should return "No access"', () => {
      const rowWithoutRoles = {
        account_id: 'account_123',
        user_contact: { state: 'active' },
      };
      const result = formatter.userContactTextFormatter([{}], rowWithoutRoles);
      expect(result).toBe('No access');
    });
  });

  describe('contactLevelRelationTextFormatter', () => {
    it('Given contact level data with IDs, should format as newline separated values', () => {
      const result = formatter.contactLevelRelationTextFormatter(
        null,
        mockRowWithContactLevel
      );
      expect(result).toBe('STR_LEVEL_1\nSTR_LEVEL_2\nSTR_LEVEL_3\nlevel_4');
    });

    it('Given non-array contact level data, should return empty string', () => {
      const rowWithInvalidContactLevel = { contact_level: null };
      const result = formatter.contactLevelRelationTextFormatter(
        null,
        rowWithInvalidContactLevel
      );
      expect(result).toBe('');
    });

    it('Given row without contact level data, should return empty string', () => {
      const result = formatter.contactLevelRelationTextFormatter(null, {});
      expect(result).toBe('');
    });

    it('Given empty contact level array, should return empty string', () => {
      const rowWithEmptyContactLevel = { contact_level: [] };
      const result = formatter.contactLevelRelationTextFormatter(
        null,
        rowWithEmptyContactLevel
      );
      expect(result).toBe('');
    });
  });

  describe('extractIdsTextFormatter', () => {
    it('Given array field with IDs, should format as newline separated values', () => {
      const extractFormatter =
        formatter.extractIdsTextFormatter('contact_level');
      const result = extractFormatter(null, mockRowWithContactLevel);
      expect(result).toBe('STR_LEVEL_1\nSTR_LEVEL_2\nSTR_LEVEL_3\nlevel_4');
    });

    it('Given non-array field, should return field value as string', () => {
      const extractFormatter =
        formatter.extractIdsTextFormatter('single_field');
      const rowWithSingleField = { single_field: 'single_value' };
      const result = extractFormatter(null, rowWithSingleField);
      expect(result).toBe('single_value');
    });

    it('Given missing field, should return empty string', () => {
      const extractFormatter =
        formatter.extractIdsTextFormatter('missing_field');
      const result = extractFormatter(null, {});
      expect(result).toBe('');
    });

    it('Given null field, should return empty string', () => {
      const extractFormatter = formatter.extractIdsTextFormatter('null_field');
      const rowWithNullField = { null_field: null };
      const result = extractFormatter(null, rowWithNullField);
      expect(result).toBe('');
    });
  });

  describe('accountRoleSettingsTextFormatter', () => {
    it('Given account role settings with custom view, should return view name', () => {
      const result = formatter.accountRoleSettingsTextFormatter(
        null,
        mockRowWithAccountRoleSettings
      );
      expect(result).toBe('Custom view');
    });

    it('Given row without account role settings, should return empty string', () => {
      const result = formatter.accountRoleSettingsTextFormatter(null, {});
      expect(result).toBe('');
    });

    it('Given account role settings without custom view name, should return empty string', () => {
      const rowWithoutCustomViewName = {
        account_role_settings: {},
      };
      const result = formatter.accountRoleSettingsTextFormatter(
        null,
        rowWithoutCustomViewName
      );
      expect(result).toBe('');
    });

    it('Given undefined row, should return empty string', () => {
      const result = formatter.accountRoleSettingsTextFormatter(
        null,
        undefined
      );
      expect(result).toBe('');
    });
  });

  describe('accountingTransactionsTextFormatter', () => {
    it('Given row with saved reports, should return report count', () => {
      const result = formatter.accountingTransactionsTextFormatter(
        'field',
        mockRowWithSavedReports
      );
      expect(result).toBe(3);
    });

    it('Given row with non-array saved reports, should return empty string', () => {
      const rowWithInvalidSavedReports = { saved_reports: null };
      const result = formatter.accountingTransactionsTextFormatter(
        'field',
        rowWithInvalidSavedReports
      );
      expect(result).toBe('');
    });

    it('Given row without saved reports, should return empty string', () => {
      const result = formatter.accountingTransactionsTextFormatter('field', {});
      expect(result).toBe('');
    });

    it('Given null row, should return empty string', () => {
      const result = formatter.accountingTransactionsTextFormatter(
        'field',
        null
      );
      expect(result).toBe('');
    });

    it('Given empty saved reports array, should return empty string', () => {
      const rowWithEmptySavedReports = { saved_reports: [] };
      const result = formatter.accountingTransactionsTextFormatter(
        'field',
        rowWithEmptySavedReports
      );
      expect(result).toBe('');
    });
  });
});
