import Formatter, { type Contact } from 'common/Formatter';

import BaseTextFormatter from '../shared/base/base-text-formatter';

export class ContactTextFormatter extends BaseTextFormatter {
  // biome-ignore lint/complexity/noUselessConstructor: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  constructor() {
    super();
  }

  contactLevelTextFormatter = (
    value: {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      [key: string]: any;
      level_label?: string;
      product_type?: string;
      loa?: boolean;
    }[]
  ) => {
    if (!Array.isArray(value)) return '';

    const levels = [];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    value.forEach((val) => {
      if (val.level_label) {
        levels.push(val.level_label);
      }

      if (val.product_type) {
        levels.push(val.product_type);
      }

      if (val.loa) {
        levels.push('LOA');
      }
    });

    return levels.length > 0 ? levels.join(', ') : '';
  };

  uplinesTextFormatter = (value: { parent?: Contact }[]) => {
    return value.map((item) => Formatter.contact(item.parent)).join('\n');
  };

  downlinesTextFormatter = (value: { contact?: Contact }[]) => {
    return value.map((item) => Formatter.contact(item.contact)).join('\n');
  };

  compProfileSetsTextFormatter = (
    value?: {
      agent_commission_schedule_profiles_sets?: { name?: string };
    }[]
  ) => {
    if (!Array.isArray(value)) return '';

    return value
      ?.map((item) => {
        if (item.agent_commission_schedule_profiles_sets) {
          return item.agent_commission_schedule_profiles_sets.name;
        }
        return 'Not found';
      })
      .join('\n');
  };

  compProfilesTextFormatter = (
    value?: {
      agent_commission_schedule_profile?: { name?: string };
    }[]
  ) => {
    if (!Array.isArray(value)) return '';

    return value
      ?.map((item) => {
        if (item.agent_commission_schedule_profile) {
          return item.agent_commission_schedule_profile.name;
        }
        return 'Not found';
      })
      .join('\n');
  };

  displayLengthTextFormatter = (value?: Array<unknown>) => {
    if (!Array.isArray(value)) return '';

    return value.length || '';
  };

  savedReportsTextFormatter = (value?: Partial<{ name: string }>[]) => {
    if (!Array.isArray(value)) return '';

    return value.map((item) => item.name).join('\n');
  };

  userContactTextFormatter = (
    value?: Partial<{ state: string }>[],
    row?: Partial<{ account_id: string }> & {
      user_contact?: Partial<{ state: string }> & {
        account_user_roles?: Partial<{ account_id: string; state: string }>[];
      };
    }
  ) => {
    if (value) {
      const account = row?.user_contact?.account_user_roles?.find(
        (account) => account.account_id === row?.account_id
      );
      if (account) {
        return account.state;
      } else {
        return 'No access';
      }
    } else {
      return 'No access';
    }
  };

  contactLevelRelationTextFormatter = (
    _value: unknown,
    row: {
      contact_level?: {
        id?: string;
        str_id?: string;
      }[];
    }
  ) => {
    if (!Array.isArray(row.contact_level)) return '';
    return row.contact_level.map((item) => item.str_id || item.id).join('\n');
  };

  extractIdsTextFormatter =
    (field: string) =>
    (
      _value: unknown,
      row: {
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        [key: string]: any;
      }
    ) => {
      if (!Array.isArray(row[field]))
        return row[field] ? String(row[field]) : '';

      return row[field].map((item) => item.str_id || item.id).join('\n');
    };

  accountRoleSettingsTextFormatter = (
    _value: unknown,
    row?: Partial<{
      account_role_settings?: Partial<{ custom_view_name: string }>;
    }>
  ) => {
    return row?.account_role_settings?.custom_view_name || '';
  };

  // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  accountingTransactionsTextFormatter = (field, row) => {
    if (row && Array.isArray(row.saved_reports)) {
      return row.saved_reports.length || '';
    }
    return '';
  };

  contactGroupTextFormatter = (
    _value: number,
    row: {
      contact_group?: { name?: string };
    }
  ) => {
    return row?.contact_group?.name || '';
  };
}
