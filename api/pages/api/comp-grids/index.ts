import * as Sentry from '@sentry/nextjs';
import type { NextApiRequest, NextApiResponse } from 'next';
import {
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
  createHandler,
} from 'next-api-decorators';
import * as dto from 'common/dto/comp_grids/dto';

import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { ZodBody } from '@/lib/decorators';

class Handler extends BaseHandler {
  @Get()
  async get(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      const data = await _getData(req);
      res.json(data);
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`An error occurred when getting data: ${error.message}`);
      Sentry.captureException(error);
      res.status(500).json({ error: error.message });
    }
  }

  @Post()
  async post(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridCreateDTOSchema)()) body: dto.CompGridCreateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      const data = await createOne(req, body);
      res.status(201).json(data);
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`Error creating new record: ${error.message}`);
      Sentry.captureException(error);
      res.status(500).json({ error: error.message });
    }
  }

  @Patch()
  async patch(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridUpdateDTOSchema)()) body: dto.CompGridUpdateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      const data = await updateOne(req, body);
      res.status(200).json(data);
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`Error updating data: ${error}`);
      Sentry.captureException(error);
      res.status(500).json({ error: error.message });
    }
  }

  @Delete()
  async delete(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridDeleteDTOSchema)()) body: dto.CompGridDeleteDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      await deleteMany(req, body);
      res.status(200).json({ status: 'OK' });
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`Error deleting data: ${error}`);
      Sentry.captureException(error);
      res.status(500).json({ error: error.message });
    }
  }
}

export default withAuth(createHandler(Handler));

export const _getData = async (req: ExtNextApiRequest & NextApiRequest) => {
  let companyId = req.query?.company_id ? +req.query?.company_id : undefined;
  if (
    !companyId &&
    Array.isArray(req.query?.companies) &&
    req.query.companies.length > 0
  ) {
    const companies = req.query.companies as unknown as Array<number>;
    companyId = companies[0];
  }
  const where = {
    account_id: req.account_id,
    state: 'active',
    str_id: req.query?.id,
    company_id: companyId,
  };
  const data = await prisma.comp_grids.findMany({
    where,
  });
  return data;
};

const createOne = async (
  req: ExtNextApiRequest,
  body: dto.CompGridCreateDTO
) => {
  const { rate_fields, ..._body } = body;
  const data = await prisma.comp_grids.create({
    data: {
      ..._body,
      account_id: req.account_id,
      created_by: req.uid,
      created_proxied_by: req.ouid,
      rate_fields: rate_fields && rate_fields.length > 0 ? rate_fields : null,
    },
  });
  return data;
};

const updateOne = async (
  req: ExtNextApiRequest,
  body: dto.CompGridUpdateDTO
) => {
  const { id, company_id, rate_fields, ..._body } = body;
  if (!id) throw new Error('Missing id');
  let data = {};
  data = await prisma.comp_grids.update({
    where: { id: Number(body.id), account_id: String(req.account_id) },
    data: {
      ..._body,
      updated_at: new Date(),
      updated_by: req.uid,
      updated_proxied_by: req.ouid,
      company: company_id ? { connect: { id: company_id } } : undefined,
      rate_fields: rate_fields && rate_fields.length > 0 ? rate_fields : null,
    },
  });
  return data;
};

const deleteMany = async (
  req: ExtNextApiRequest,
  body: dto.CompGridDeleteDTO
) => {
  const { ids } = body;
  if (!Array.isArray(ids) || ids.length === 0) throw new Error('Missing ids');
  // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
    throw new Error('Invalid ids');
  const promises = ids.map((_id) => {
    return prisma.comp_grids.update({
      where: { id: Number(_id), account_id: String(req.account_id) },
      data: {
        state: 'deleted',
        updated_at: new Date(),
        updated_by: req.uid,
        updated_proxied_by: req.ouid,
      },
    });
  });
  const data = await prisma.$transaction(promises);
  return data;
};
