import type { NextApiRequest, NextApiResponse } from 'next';
import { Delete, Patch, Req, Res, createHandler } from 'next-api-decorators';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import {
  CompGridRatesService,
  type ICompGridRatesService,
} from '@/services/comp-grids/rates';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';

class Handler extends BaseHandler {
  private compGridRatesService: CompGridRatesService;

  constructor() {
    super();
    this.compGridRatesService =
      container.get<CompGridRatesService>(CompGridRatesService);
  }

  @Patch()
  async bulkUpdateRatesPost(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await bulkUpdateRates(req, res, this.compGridRatesService);
  }

  @Delete()
  async bulkUpdateRatesDelete(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await bulkDeleteRates(req, res, this.compGridRatesService);
  }
}

export default withAuth(createHandler(Handler));

const bulkDeleteRates = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  compGridRatesService: ICompGridRatesService
) => {
  const { ids: rateIds } = req.body;
  const numericRateIds = Array.isArray(rateIds) ? rateIds.map(Number) : [];

  const resp = await compGridRatesService.bulkDeleteRates(
    numericRateIds,
    req.account_id,
    req.uid,
    req.ouid
  );

  const errors = resp.filter((r) => r.error);

  if (errors.length > 0) {
    res.status(500).json({ errors });
    return;
  }

  res.status(201).json(resp);
};

const bulkUpdateRates = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  compGridRatesService: ICompGridRatesService
) => {
  const { ids: rateIds, date_ranges, ..._body } = req.body;
  const numericRateIds = Array.isArray(rateIds) ? rateIds.map(Number) : [];

  const resp = await compGridRatesService.bulkUpdateRatesWithNewDateRange(
    numericRateIds,
    date_ranges,
    req.account_id,
    req.uid,
    req.ouid
  );

  const errors = resp.filter((r) => r.error);

  if (errors.length > 0) {
    res.status(500).json({ errors });
    return;
  }

  res.status(201).json(resp);
};
