import * as dto from 'common/dto/comp_grid_rates/dto';
import { doDateRangesOverlap } from 'common/helpers';
import type { NextApiRequest, NextApiResponse } from 'next';
import { Post, Req, Res, createHandler } from 'next-api-decorators';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import {
  CompGridRatesService,
  type ICompGridRatesService,
} from '@/services/comp-grids/rates';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { ZodBody } from '@/lib/decorators';

class Handler extends BaseHandler {
  private compGridRatesService: CompGridRatesService;

  constructor() {
    super();
    this.compGridRatesService =
      container.get<CompGridRatesService>(CompGridRatesService);
  }

  @Post()
  async copyRatesPost(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridRateCreateDTOSchema)())
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    body: dto.CompGridRateCreateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await copyRates(req, res, this.compGridRatesService);
  }
}

export default withAuth(createHandler(Handler));

const copyRates = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  compGridRatesService: ICompGridRatesService
) => {
  const { ids: rateIds, date_ranges, ..._body } = req.body;

  if (doDateRangesOverlap(date_ranges)) {
    res.status(500).json({ error: 'Date ranges can not overlap' });
    return;
  }

  const numericRateIds = Array.isArray(rateIds) ? rateIds.map(Number) : [];

  const resp = await compGridRatesService.copyRatesWithNewDateRange(
    numericRateIds,
    date_ranges,
    req.account_id,
    req.uid,
    req.ouid
  );

  const errors = resp.filter((r) => r.error);

  if (errors.length > 0) {
    res.json({ errors });
    return;
  }

  res.json(resp);
};
