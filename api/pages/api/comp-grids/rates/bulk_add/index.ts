import * as Sentry from '@sentry/nextjs';
import type { NextApiRequest, NextApiResponse } from 'next';
import { Post, Req, Res, createHandler } from 'next-api-decorators';
import * as dto from 'common/dto/comp_grid_rates/dto';

import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { ZodBody } from '@/lib/decorators';

class Handler extends BaseHandler {
  @Post()
  async post(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.BulkCompGridRateCreateDTOSchema)())
    body: dto.BulkCompGridRateCreateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      const data = await createMany(req, body);
      res
        .status(201)
        .json({ stats: { current_length: data.count }, statusText: 'ok' });
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`An error occurred: ${error.message}`);
      Sentry.captureException(error);
      res.status(500).json({ error: error.message });
    }
  }
}

export default withAuth(createHandler(Handler));

const idify = (s) => {
  const trimmed = typeof s === 'string' ? s.trim() : s;
  if (trimmed) return +trimmed;
  else return null;
};

const toPct = (s) => {
  const trimmed = typeof s === 'string' ? s.trim() : s;
  if (trimmed) return +trimmed;
  else return null;
};

const createMany = async (
  req: ExtNextApiRequest & NextApiRequest,
  body: dto.BulkCompGridRateCreateDTO
) => {
  const { data } = body;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const newData = data.map((datum: any) => ({
    ...datum,
    comp_grid_level_id: idify(datum.comp_grid_level_id),
    comp_grid_criterion_id: idify(datum.comp_grid_criterion_id),
    carrier_rate: toPct(datum.carrier_rate),
    house_rate: toPct(datum.house_rate),
    rate: toPct(datum.rate),
    account_id: req.account_id,
    created_by: req.uid,
    created_proxied_by: req.ouid,
  }));

  const results = await prisma.comp_grid_rates.createMany({
    data: newData,
  });

  return results;
};
