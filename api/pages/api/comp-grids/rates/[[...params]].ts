import * as Sentry from '@sentry/nextjs';
import * as dto from 'common/dto/comp_grid_rates/dto';
import type { NextApiRequest, NextApiResponse } from 'next';
import {
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
  createHandler,
} from 'next-api-decorators';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { getSearchWhere } from '@/lib/helpers';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import {
  CompGridRatesService,
  type ICompGridRatesService,
} from '@/services/comp-grids/rates';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { calculateSkipAndTake } from '@/prisma';
import { ZodBody } from '@/lib/decorators';

class Handler extends BaseHandler {
  private compGridRatesService: CompGridRatesService;

  constructor() {
    super();
    this.compGridRatesService =
      container.get<CompGridRatesService>(CompGridRatesService);
  }

  @Get()
  async get(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      const data = await _getData(req);
      res.json(data);
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`An error occurred when getting data: ${error.message}`);
      Sentry.captureException(error);
      res.status(500).json({ error: error.message });
    }
  }

  @Post()
  async post(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridRateCreateDTOSchema)())
    body: dto.CompGridRateCreateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await createOne(req, res, body, this.compGridRatesService);
  }

  @Patch()
  async patch(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridRateUpdateDTOSchema)())
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    body: dto.CompGridRateUpdateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await updateOne(req, res, this.compGridRatesService);
  }

  @Delete()
  async delete(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridDeleteDTOSchema)()) body: dto.CompGridDeleteDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await deleteMany(req, res, body);
  }
}

export default withAuth(createHandler(Handler));

export const _getData = async (req: ExtNextApiRequest & NextApiRequest) => {
  const {
    page,
    limit,
    orderBy = 'created_at',
    sort = 'desc',
    q = '',
  } = req.query;

  const { take, skip } = calculateSkipAndTake({ page, limit });

  const searchWhere = getSearchWhere(q, [
    { comp_grid_criterion: { compensation_type: 'string' } },
    { comp_grid_criterion: { compensation_type_alternative: 'string' } },
    { comp_grid_criterion: { comp_grid_product: { name: 'string' } } },
    { comp_grid_criterion: { comp_grid_product: { type: 'string' } } },
    { comp_grid_criterion: { policy_year_end: 'number' } },
    { comp_grid_criterion: { policy_year_start: 'number' } },
    { comp_grid_criterion: { issue_age_end: 'number' } },
    { comp_grid_criterion: { issue_age_start: 'number' } },
    { comp_grid_criterion: { comp_grid: { name: 'string' } } },
    { comp_grid_criterion: { company: { company_name: 'string' } } },
  ]);

  const where = {
    account_id: req.account_id,
    state: 'active',
    str_id: req.query?.id,
    comp_grid_criterion: {
      state: 'active',
      id: undefined,
      comp_grid: {
        state: 'active',
        id: undefined,
        str_id: undefined,
        company_id: undefined,
      },
    },
    OR: searchWhere,
  };

  if (req.query.company_id)
    where.comp_grid_criterion.comp_grid.company_id = +req.query.company_id;
  if (req.query?.comp_grid_id) {
    const ids = Array.isArray(req.query?.comp_grid_id)
      ? req.query?.comp_grid_id
      : [req.query?.comp_grid_id];
    const isNumeric = /^\d+$/.test(ids[0]);
    if (isNumeric)
      where.comp_grid_criterion.comp_grid.id = { in: ids.map(Number) };
    else where.comp_grid_criterion.comp_grid.str_id = { in: ids };
  }
  if (req.query.comp_grid_level_id)
    where.comp_grid_criterion.id = +req.query.comp_grid_level_id;

  const [data, count] = await Promise.all([
    prisma.comp_grid_rates.findMany({
      where,
      skip,
      take,
      include: {
        comp_grid_criterion: {
          include: {
            comp_grid: true,
          },
        },
        comp_grid_level: {
          include: {
            comp_grid: true,
          },
        },
        date_ranges: {
          where: { state: 'active' },
        },
      },
      orderBy:
        orderBy === 'comp_grid_criterion_id'
          ? [
              { comp_grid_criterion: { company: { company_name: sort } } },
              { comp_grid_criterion: { comp_grid_product: { type: sort } } },
              { comp_grid_criterion: { comp_grid_product: { name: sort } } },
              { comp_grid_criterion: { policy_year_start: sort } },
              { comp_grid_criterion: { policy_year_end: sort } },
              { comp_grid_criterion: { issue_age_start: sort } },
              { comp_grid_criterion: { issue_age_end: sort } },
              { comp_grid_criterion: { compensation_type: sort } },
            ]
          : undefined,
    }),
    prisma.comp_grid_rates.count({
      where,
    }),
  ]);
  const _data = data.map((d) => ({
    ...d,
    comp_grid_id: d.comp_grid_criterion?.comp_grid.id,
  }));

  // Sort by date_ranges.name sorting by one to many nested relation not supported yet https://github.com/prisma/prisma/discussions/22954
  if (orderBy === 'date_ranges') {
    _data.sort((a, b) => {
      const aName = a.date_ranges[0]?.name || '';
      const bName = b.date_ranges[0]?.name || '';
      return sort === 'asc'
        ? aName.localeCompare(bName)
        : bName.localeCompare(aName);
    });
  }
  return { data: _data, count: count };
};

const createOne = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  body: dto.CompGridRateCreateDTO,
  compGridRatesService: ICompGridRatesService
) => {
  try {
    if (!body.comp_grid_criterion_id)
      throw new Error('Comp grid criterion id is required');
    if (!body.comp_grid_level_id)
      throw new Error('Comp grid level id is required');
    if (!body.comp_grid_id) throw new Error('Comp grid id is required');

    const data = await compGridRatesService.createCompGridRate(req);
    res.status(201).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error creating new record: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
};

const updateOne = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  compGridRatesService: ICompGridRatesService
) => {
  try {
    const data = await compGridRatesService.updateCompGridRates(req);
    res.status(200).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error updating data: ${error}`);
    res.status(500).json({ error: error.message });
  }
};

const deleteMany = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  body: dto.CompGridDeleteDTO
) => {
  const { ids } = body;
  try {
    if (!Array.isArray(ids) || ids.length === 0) throw new Error('Missing ids');
    // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
      throw new Error('Invalid ids');
    const promises = ids.map((_id) => {
      return prisma.comp_grid_rates.update({
        where: { id: Number(_id), account_id: String(req.account_id) },
        data: {
          state: 'deleted',
          updated_at: new Date(),
          updated_by: req.uid,
          updated_proxied_by: req.ouid,
        },
      });
    });
    await prisma.$transaction(promises);
    res.status(200).json({ status: 'OK' });
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error deleting data: ${error}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};
