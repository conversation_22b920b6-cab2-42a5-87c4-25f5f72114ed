import type { NextApiRequest, NextApiResponse } from 'next';
import { create<PERSON><PERSON><PERSON>, <PERSON>, Req, Re<PERSON> } from 'next-api-decorators';
import { GetCompGridOptionsSchema } from 'common/dto/comp_grids/dto';
import type { GetCompGridOptionsDto } from 'common/dto/comp_grids/dto';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { CompGridsOptionsService } from '@/services/comp-grids/options';
import { ZodQuery } from '@/lib/decorators';

class CompGridsOptionsHandler extends BaseHandler {
  private compGridsOptionsService: CompGridsOptionsService;
  constructor() {
    super();
    this.compGridsOptionsService = container.get<CompGridsOptionsService>(
      CompGridsOptionsService
    );
  }

  @Get()
  async getCompGridsOptions(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    @Res() res: ExtNextApiResponse & NextApiResponse,
    // biome-ignore format: compound decorator
    @(ZodQuery(GetCompGridOptionsSchema)())
    query: GetCompGridOptionsDto
  ) {
    const { company_str_ids } = query;

    const options = await this.compGridsOptionsService.getCompGridsOptions(
      req.account_id,
      company_str_ids
    );
    return options;
  }
}

export default withAuth(createHandler(CompGridsOptionsHandler));
