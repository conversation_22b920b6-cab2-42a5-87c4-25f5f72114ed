import type { comp_grid_criteria, comp_grid_rates } from '@prisma/client';
import { CompGridRateFields } from 'common/globalTypes';
import { dateOrDefault, isValidString, numberOrDefault } from 'common/helpers';
import type { NextApiRequest, NextApiResponse } from 'next';
import { Get, <PERSON>, Req, Re<PERSON>, createHand<PERSON> } from 'next-api-decorators';
import uniq from 'lodash-es/uniq';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { runInBatch, fetchCompGridRatesWithRawQuery } from '@/lib/helpers';
import { withAuth } from '@/lib/middlewares';
import { prismaClient } from '@/lib/prisma';
import {
  DirectDownlineDataAccessCompGridRatesOptions,
  ExtendedDownlineDataAccessCompGridRatesOptions,
} from '@/pages/api/accounts/settings/defaults';
import { CompGridCriteriaService } from '@/services/comp-grids/criteria';
import { CompGridRatesService } from '@/services/comp-grids/rates';
import { ContactService } from '@/services/contact';
import { SettingsService } from '@/services/settings';
import {
  DataStates,
  type ExtNextApiRequest,
  type ExtNextApiResponse,
  Roles,
} from '@/types';
import { Guard } from '@/services/permission/decorator';
import { CrudAction, EntityType } from '@/services/permission/interface';
import { CompGridLevelsService } from '@/services/comp-grids/levels';
import CompGridsService from '@/services/comp-grids/CompGridsService';

// TransGlobal level mappings
const TG_LEVEL_MAPPINGS = {
  'Agency Top': 'AT',
  'Agency 0': 'A0',
  'Agency 1': 'A1',
  'Agency 2': 'A2',
  'Agency 3': 'A3',
  'Level S': 'S',
  'Level A': 'A',
  'Level B': 'B',
  UNI: 'UNI',
  'Mindy Gong': 'Mindy Gong',
  'Li Luo': 'Li Luo',
  'July Li': 'July Li',
};

class Handler extends BaseHandler {
  private settingsService: SettingsService;
  private contactService: ContactService;
  private compGridRatesService: CompGridRatesService;
  private compGridService: CompGridsService;
  private compGridCriteriaService: CompGridCriteriaService;
  private compGridLevelsService: CompGridLevelsService;

  constructor() {
    super();
    this.settingsService = container.get<SettingsService>(SettingsService);
    this.contactService = container.get<ContactService>(ContactService);
    this.compGridLevelsService = container.get<CompGridLevelsService>(
      CompGridLevelsService
    );
    this.compGridRatesService =
      container.get<CompGridRatesService>(CompGridRatesService);
    this.compGridCriteriaService = container.get<CompGridCriteriaService>(
      CompGridCriteriaService
    );
    this.compGridService = container.get<CompGridsService>(CompGridsService);
  }

  @Get()
  @Guard(CrudAction.READ, EntityType.COMP_GRIDS_SCHEDULES)
  async get(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const {
      effective_date: _effectiveDateFilter,
      range_date: _rangeDateFilter,
      q: textSearch = '',
      comp_grids: _compGrids,
      compensation_types: _compensationTypes,
      page = 0,
      limit = 100,
    } = req.query;

    const effectiveDateFilter = dateOrDefault(_effectiveDateFilter, undefined);
    const rangeDateFilter = numberOrDefault(_rangeDateFilter, 'undefined');
    const compensationTypes = (
      Array.isArray(_compensationTypes)
        ? _compensationTypes
        : [_compensationTypes]
    ).filter(isValidString);
    const comp_grids = (
      Array.isArray(_compGrids) ? _compGrids.map(Number) : [+_compGrids]
    ).filter(Boolean);

    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let levelNames;
    const accountLevelMappings =
      await this.compGridLevelsService.getAccountOrderedCompGridLevels(
        req.account_id
      );

    if (parseInt(req.role_id) === Roles.PRODUCER) {
      const producerContact = await this.contactService.getContactByUserUid(
        req.uid,
        req.account_id
      );

      const agentSettings = (
        await this.settingsService.getRoleSettingsByAccountAndRole(
          req.account_id,
          Roles.PRODUCER
        )
      ).agent_settings;

      let hierarchyDepth = 0;
      if (
        agentSettings?.directDownlineDataAccess?.compGridRates ===
        DirectDownlineDataAccessCompGridRatesOptions.YES
      ) {
        hierarchyDepth = 1;
      }
      if (
        agentSettings?.extendedDownlineDataAccess?.compGridRates ===
        ExtendedDownlineDataAccessCompGridRatesOptions.EXTENDED_DOWNLINES
      ) {
        hierarchyDepth = 9;
      }
      const allowedContactIds =
        await this.contactService.getChildrenContactIdListByDepth(
          producerContact.id,
          hierarchyDepth,
          req.account_id
        );

      const contactsLevels = await this.contactService.getContactDataByIdList(
        allowedContactIds,
        false,
        true
      );

      levelNames = contactsLevels.flatMap(
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        (producerContact: any) =>
          producerContact?.agency_contact_levels?.map(
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            (level: any) => level?.level_label
          ) ?? []
      );

      const extendedDownlineDataAccess =
        agentSettings?.extendedDownlineDataAccess?.compGridRates;

      if (
        extendedDownlineDataAccess ===
        ExtendedDownlineDataAccessCompGridRatesOptions.LEVELS_LOWER
      ) {
        levelNames = mapAndSliceLevels(contactsLevels, TG_LEVEL_MAPPINGS);
      } else if (
        extendedDownlineDataAccess ===
        ExtendedDownlineDataAccessCompGridRatesOptions.GRID_LEVELS_LOWER
      ) {
        levelNames = mapAndSliceLevels(contactsLevels, accountLevelMappings);
      }
    }

    const filtersResult = await this.compGridService.getCompGrids({
      accountId: req.account_id,
      strId: req.query.id,
      textSearch,
      compensationTypes,
      select: {
        id: true,
        str_id: true,
        name: true,

        comp_grid_criteria: {
          select: {
            compensation_type: true,
          },
        },
      },
    });

    const filtersData = filtersResult.map((value) => {
      return {
        id: value.id,
        str_id: value.str_id,
        name: value.name,
        compensationTypes: uniq(
          value.comp_grid_criteria.map((criteria) => criteria.compensation_type)
        ),
      };
    });

    const select = {
      id: true,
      str_id: true,
      company_id: true,
      name: true,
      rate_fields: true,
      company: {
        select: {
          id: true,
          str_id: true,
          company_name: true,
        },
      },
      comp_grid_levels: {
        where: createCompGridLevelsFilter(levelNames),
        select: {
          id: true,
          str_id: true,
          order_index: true,
          name: true,
        },
      },
      related_comp_grid_levels: {
        where: createCompGridLevelsFilter(levelNames),
        select: {
          id: true,
          str_id: true,
          order_index: true,
          name: true,
        },
      },
      comp_grid_criteria: {
        where: createCompGridRateCriteriaFilter(
          effectiveDateFilter,
          rangeDateFilter
        ),
        select: {
          id: true,
          str_id: true,
          comp_grid_id: true,
          compensation_type: true,
          filter_date_field: true,
          issue_age_end: true,
          issue_age_start: true,
          premium_min: true,
          premium_max: true,
          policy_year_end: true,
          policy_year_start: true,
          transaction_type: true,
          payment_mode: true,
          comp_grid_product: {
            where: { state: DataStates.ACTIVE },
            select: {
              id: true,
              name: true,
              type: true,
              company_products: {
                where: { state: DataStates.ACTIVE },
                select: {
                  id: true,
                  product_name: true,
                  product_type: true,
                },
              },
            },
          },
          date_ranges: {
            where: createDateRangeFilter(effectiveDateFilter, rangeDateFilter),
            select: {
              id: true,
              start_date: true,
              end_date: true,
              name: true,
            },
          },
          company: {
            select: {
              id: true,
              str_id: true,
              company_name: true,
            },
          },
        },
      },
      comp_grid_products: {
        where: { state: DataStates.ACTIVE },
        select: {
          id: true,
          name: true,
          type: true,
        },
      },
    };

    const data = await this.compGridService.getCompGrids({
      accountId: req.account_id,
      textSearch,
      compensationTypes,
      select,
      ids:
        Array.isArray(comp_grids) && comp_grids.length ? comp_grids : undefined,
    });

    const allCriteriaIds = data.flatMap((compGrid) =>
      compGrid.comp_grid_criteria.map((criteria) => criteria.id)
    );
    const gridCriteriaIds = allCriteriaIds.slice(
      +page * +limit,
      +page * +limit + +limit
    );

    const gridCriteriaRate = await runInBatch({
      items: gridCriteriaIds,
      batchSize: 100,
      name: 'grid-criteria-rate',
      onBatch: async (ids) => {
        const effectiveDate = effectiveDateFilter
          ? new Date(effectiveDateFilter)
          : new Date();

        return fetchCompGridRatesWithRawQuery({
          prismaClient,
          accountId: req.account_id,
          criteriaIds: ids,
          effectiveDate,
          rangeDateFilter,
        });
      },
    });

    const gridCriteriaRateMap = new Map();
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    gridCriteriaRate.forEach((rate) => {
      const rates = gridCriteriaRateMap.get(rate.comp_grid_criterion_id) || [];
      // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      let newRates;
      const existingRate = rates.find(
        (r) => r.comp_grid_level.id === rate.comp_grid_level.id
      );
      // If existing rate is older, replace it with new one
      if (existingRate) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.warn(
          `Duplicate comp grid rates (overlapping date ranges) ${existingRate.id} and ${rate.id}.`
        );
        if (existingRate.id < rate.id) {
          newRates = rates.map((r) => (r.id === existingRate.id ? rate : r));
        } else {
          newRates = rates;
        }
      } else {
        newRates = [...rates, rate];
      }
      gridCriteriaRateMap.set(rate.comp_grid_criterion_id, newRates);
    });

    // Check comp grid rate fields to filter out unwanted rates fields per comp grid and return only criteria with existing rates
    const filteredData = data.map((compGrid) => {
      // Merge comp_grid_levels and related_comp_grid_levels without duplicates
      const uniqueLevels = new Map();
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      [
        ...compGrid.comp_grid_levels,
        ...compGrid.related_comp_grid_levels,
      ].forEach((level) => {
        uniqueLevels.set(level.id, level);
      });

      compGrid.comp_grid_levels = Array.from(uniqueLevels.values()).sort(
        (a, b) => {
          const orderA = a.order_index ?? Number.MAX_SAFE_INTEGER;
          const orderB = b.order_index ?? Number.MAX_SAFE_INTEGER;
          return orderA - orderB;
        }
      );
      const rateFieldsToKeep = new Set(
        (compGrid.rate_fields as Iterable<unknown>) || []
      );
      compGrid.comp_grid_criteria = compGrid.comp_grid_criteria.map(
        // biome-ignore lint/suspicious/noExplicitAny: this code should be refactored to use proper types, its really confusing here
        (criteria: any) => {
          criteria.comp_grid_rates = gridCriteriaRateMap.get(criteria.id) || [];
          if (rateFieldsToKeep.size > 0) {
            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            criteria.comp_grid_rates.forEach((rate) => {
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              Object.keys(rate).forEach((field) => {
                if (
                  (field === 'house_rate' ||
                    field === 'carrier_rate' ||
                    field === 'rate') &&
                  !rateFieldsToKeep.has(field) &&
                  !(
                    field === 'rate' &&
                    rateFieldsToKeep.has(CompGridRateFields.TOTAL_RATE)
                  )
                ) {
                  rate[field] = null;
                }
              });
            });
          }
          return criteria;
        }
      );
      return compGrid;
    });

    res.json({
      criteria_count: allCriteriaIds.length,
      filters: filtersData,
      data: filteredData,
    });
  }

  @Patch()
  async patch(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    if (!req.account_id) throw new Error('Account id is required');

    const { body } = req;
    // TODO: Define DTOs for request body
    const { rates, criteria, new_rates } = body;

    let ratesResponse: comp_grid_rates[];
    let criteriaResponse: comp_grid_criteria[];
    let newRatesResponse: comp_grid_rates[];

    if (rates.length > 0) {
      ratesResponse = await this.compGridRatesService.updateCompGridRates({
        body: rates,
        account_id: req.account_id,
        uid: req.uid,
        ouid: req.ouid,
      });
    }

    if (criteria.length > 0) {
      criteriaResponse = await this.compGridCriteriaService.updateCriteria(
        criteria,
        req.account_id,
        req.uid,
        req.ouid
      );
    }

    if (new_rates.length > 0) {
      newRatesResponse =
        await this.compGridRatesService.createNewRatesFromViewer(
          new_rates,
          req.account_id,
          req.uid,
          req.ouid
        );
    }

    res.json({
      rates: ratesResponse,
      criteria: criteriaResponse,
      new_rates: newRatesResponse,
    });
  }
}

const createDateRangeFilter = (
  effectiveDateFilter?: string,
  rangeDateFilter?: number
) => {
  const currentDate = new Date();
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const filters: any = {
    state: DataStates.ACTIVE,
  };

  if (effectiveDateFilter) {
    filters.OR = [
      {
        start_date: { lte: effectiveDateFilter },
        end_date: { gte: effectiveDateFilter },
      },
      {
        start_date: null,
        end_date: null,
      },
      {
        start_date: { lte: effectiveDateFilter },
        end_date: null,
      },
      {
        start_date: null,
        end_date: { gte: effectiveDateFilter },
      },
    ];
  } else if (rangeDateFilter) {
    filters.OR = [
      { id: rangeDateFilter },
      {
        start_date: null,
        end_date: null,
      },
    ];
  } else {
    filters.OR = [
      {
        start_date: { lte: currentDate },
        end_date: { gte: currentDate },
      },
      {
        start_date: null,
        end_date: null,
      },
      {
        start_date: { lte: currentDate },
        end_date: null,
      },
      {
        start_date: null,
        end_date: { gte: currentDate },
      },
    ];
  }

  return filters;
};

const createCompGridLevelsFilter = (levelNames?: string | string[]) => ({
  name: levelNames
    ? {
        in: Array.isArray(levelNames) ? levelNames : [levelNames],
      }
    : undefined,
  state: DataStates.ACTIVE,
});

const createCompGridRateCriteriaFilter = (
  effectiveDateFilter?: string,
  rangeDateFilter?: number
) => {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const filter: any = {
    state: DataStates.ACTIVE,
  };

  filter.OR = [
    {
      date_ranges: {
        none: {},
      },
    },
    {
      date_ranges: {
        some: createDateRangeFilter(effectiveDateFilter, rangeDateFilter),
      },
    },
  ];

  return filter;
};

export default withAuth(createHandler(Handler));

const mapAndSliceLevels = (
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  contactsLevels: any[],
  levelMappings: Record<string, string> | string[]
): string[] => {
  const mappedLevels = contactsLevels.flatMap(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (producerContact: any) =>
      producerContact?.agency_contact_levels?.map(
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        (level: any) => levelMappings[level?.level_label] ?? level?.level_label
      ) ?? []
  );

  const currentLevel = mappedLevels[0];
  const levels = Object.values(levelMappings);
  const currentIndex = levels.indexOf(currentLevel);

  return currentIndex !== -1 ? levels.slice(currentIndex) : mappedLevels;
};
