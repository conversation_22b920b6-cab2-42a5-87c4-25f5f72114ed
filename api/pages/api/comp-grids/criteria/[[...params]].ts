import * as Sentry from '@sentry/nextjs';
import type { NextApiRequest, NextApiResponse } from 'next';
import {
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
  createHandler,
} from 'next-api-decorators';
import * as dto from 'common/dto/comp_grid_criteria/dto';
import { doDateRangesOverlap, mergeAndProcessDateRanges } from 'common/helpers';

import { BaseHandler } from '@/lib/baseHandler';
import { Zod<PERSON><PERSON> } from '@/lib/decorators';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import {
  DataStates,
  type ExtNextApiRequest,
  type ExtNextApiResponse,
} from '@/types';
import { calculateSkipAndTake } from '@/prisma';

class Handler extends BaseHandler {
  // biome-ignore lint/complexity/noUselessConstructor: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  constructor() {
    super();
  }

  @Get()
  async get(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      const data = await _getData(req);
      res.json(data);
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`An error occurred when getting data: ${error.message}`);
      Sentry.captureException(error);
      res.status(500).json({ error: error.message });
    }
  }

  @Post()
  async post(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridCriteriaCreateDTOSchema)())
    body: dto.CompGridCriteriaCreateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await createOne(req, res, body);
  }

  @Patch()
  async patch(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridCriteriaUpdateDTOSchema)())
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    body: dto.CompGridCriteriaUpdateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await updateOne(req, res);
  }

  @Delete()
  async delete(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridCriteriaDeleteDTOSchema)())
    body: dto.CompGridCriteriaDeleteDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await deleteMany(req, res, body);
  }
}

export default withAuth(createHandler(Handler));

export const _getData = async (req: ExtNextApiRequest & NextApiRequest) => {
  const { page, limit, q = '', product_type = undefined } = req.query;

  const productTypeArray =
    typeof product_type === 'string' ? [product_type] : product_type;

  const { take, skip } = calculateSkipAndTake({ page, limit });

  const where = {
    account_id: req.account_id,
    state: 'active',
    str_id: req.query?.id,
    comp_grid_product: undefined,
    comp_grid: {
      state: 'active',
      company_id: undefined,
      id: undefined,
      str_id: undefined,
    },
    OR: [
      { compensation_type: { contains: q, mode: 'insensitive' } },
      { comp_grid: { name: { contains: q, mode: 'insensitive' } } },
      { comp_grid_product: { name: { contains: q, mode: 'insensitive' } } },
      { company: { company_name: { contains: q, mode: 'insensitive' } } },
    ],
  };

  if (productTypeArray?.length > 0) {
    where.comp_grid_product = {
      ...where.comp_grid_product,
      type: { in: productTypeArray },
    };
  }

  if (req.query.company_id) where.comp_grid.company_id = +req.query.company_id;
  if (req.query?.comp_grid_id) {
    const ids = Array.isArray(req.query?.comp_grid_id)
      ? req.query?.comp_grid_id
      : [req.query?.comp_grid_id];
    const isNumeric = /^\d+$/.test(ids[0]);
    if (isNumeric) where.comp_grid.id = { in: ids.map(Number) };
    else where.comp_grid.str_id = { in: ids };
  }

  const findManyOperation = prisma.comp_grid_criteria.findMany(
    req.query.is_dynamic_select
      ? {
          where,
          select: {
            id: true,
            comp_grid_id: true,
            company_id: true,
            compensation_type: true,
            compensation_type_alternative: true,
            grid_product_id: true,
            issue_age_end: true,
            issue_age_start: true,
            premium_min: true,
            premium_max: true,
            payment_mode: true,
            policy_year_end: true,
            policy_year_start: true,
            transaction_type: true,
            comp_grid_product: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
            company: {
              select: {
                id: true,
                company_name: true,
              },
            },
            date_ranges: {
              select: {
                id: true,
                start_date: true,
                end_date: true,
                type: true,
                notes: true,
              },
            },
          },
        }
      : {
          where,
          skip,
          take,
          include: {
            company: {
              where: { state: 'active' },
            },
            comp_grid_product: {
              where: { state: 'active' },
            },
            date_ranges: {
              where: { state: 'active' },
            },
          },
        }
  );

  const countOperation = prisma.comp_grid_criteria.count({
    where,
  });

  const [data, count] = await Promise.all([findManyOperation, countOperation]);
  return { data: data, count: count };
};

const createOne = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  body: dto.CompGridCriteriaCreateDTO
) => {
  try {
    const data = await prisma.comp_grid_criteria.create({
      data: {
        ...body,
        account_id: req.account_id,
        created_by: req.uid,
        created_proxied_by: req.ouid,
      },
    });
    res.status(201).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error creating new record: ${error.message}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const updateOne = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  try {
    const {
      id,
      comp_grid_id,
      grid_product_id,
      company_id,
      date_ranges,
      ..._body
    } = req.body;
    if (!id) throw new Error('Missing id');

    const existingCriteria = await prisma.comp_grid_criteria.findUnique({
      where: {
        id,
        account_id: String(req.account_id),
      },
      select: { date_ranges: { where: { state: DataStates.ACTIVE } } },
    });

    const existingDateRanges = existingCriteria?.date_ranges || [];
    const uniqueDateRanges = mergeAndProcessDateRanges(
      date_ranges,
      existingDateRanges
    );

    if (doDateRangesOverlap(uniqueDateRanges)) {
      res.status(500).json({ error: 'Date ranges can not overlap' });
      return;
    }

    const newDateRangeIds = (date_ranges || []).map((dr) => dr.id);
    const existingDateRangeIds = existingDateRanges.map((dr) => dr.id);

    const dateRangesToConnect = (date_ranges || []).filter(
      (dr) => !existingDateRangeIds.includes(dr.id)
    );
    const dateRangesToDisconnect = existingDateRanges.filter(
      (dr) => !newDateRangeIds.includes(dr.id)
    );

    let data = {};
    data = await prisma.comp_grid_criteria.update({
      where: { id, account_id: String(req.account_id) },
      data: {
        ..._body,
        updated_at: new Date(),
        updated_by: req.uid,
        updated_proxied_by: req.ouid,
        company: company_id
          ? { connect: { id: company_id } }
          : { disconnect: true },
        comp_grid: comp_grid_id
          ? { connect: { id: comp_grid_id } }
          : { disconnect: true },
        comp_grid_product: grid_product_id
          ? { connect: { id: grid_product_id } }
          : { disconnect: true },
        date_ranges: {
          connect: dateRangesToConnect.map((dr) => ({ id: dr.id })),
          disconnect: dateRangesToDisconnect.map((dr) => ({ id: dr.id })),
        },
      },
    });
    res.status(200).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error updating data: ${error}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const deleteMany = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  body: dto.CompGridCriteriaDeleteDTO
) => {
  const { ids } = body;
  try {
    if (!Array.isArray(ids) || ids.length === 0) throw new Error('Missing ids');
    // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
      throw new Error('Invalid ids');
    const promises = ids.map((_id) => {
      return prisma.comp_grid_criteria.update({
        where: { id: Number(_id), account_id: String(req.account_id) },
        data: {
          state: 'deleted',
          updated_at: new Date(),
          updated_by: req.uid,
          updated_proxied_by: req.ouid,
        },
      });
    });
    await prisma.$transaction(promises);
    res.status(200).json({ status: 'OK' });
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error deleting data: ${error}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};
