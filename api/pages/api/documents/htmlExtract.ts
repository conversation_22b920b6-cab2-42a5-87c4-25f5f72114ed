import { create<PERSON><PERSON><PERSON>, <PERSON>, Req, <PERSON><PERSON> } from 'next-api-decorators';
import type { NextApiRequest, NextApiResponse } from 'next';
import * as Sentry from '@sentry/nextjs';

import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { HtmlExtractService } from '@/services/htmlExtract/service';
import prisma from '@/lib/prisma';

class Handler extends BaseHandler {
  private htmlExtractService: HtmlExtractService;

  constructor() {
    super();
    this.htmlExtractService =
      container.get<HtmlExtractService>(HtmlExtractService);
  }

  @Post()
  async post(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      const accountId = req.account_id;
      if (!accountId) {
        req.logger.error('No account specified');
        res.status(401).end('No account specified');
        return res;
      }

      const { document_id } = req.body;
      if (!document_id) {
        req.logger.error('Document ID is required');
        res.status(400).end('Document ID is required');
        return res;
      }

      req.logger.info('Starting HTML processing', { document_id });

      const result = await this.htmlExtractService.processDocument({
        documentId: document_id,
        accountId,
      });

      const extraction = await prisma.extractions.create({
        data: {
          document_id: document_id,
          account_id: accountId,
          uid: req.uid,
          method: 'htmlExtract',
          result: 'Success',
          result_id: null,
          output: JSON.stringify(result),
          created_by: req.uid,
          created_proxied_by: req.ouid,
        },
      });

      req.logger.info('HTML processing completed', {
        document_id,
      });

      res.status(200).json({
        status: 'OK',
        extractionId: extraction.id,
        result: result,
      });
    } catch (error) {
      req.logger.error('Error in HTML processing', {
        error: error.message,
        stack: error.stack,
      });
      Sentry.captureException(error);
      res.status(500).json({
        error: error.message,
      });
    }
  }
}

export default withAuth(createHandler(Handler));
