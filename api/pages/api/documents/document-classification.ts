import { create<PERSON><PERSON><PERSON>, <PERSON>, Req, <PERSON><PERSON> } from 'next-api-decorators';
import type { NextApiRequest, NextApiResponse } from 'next';
import * as Sentry from '@sentry/nextjs';
import { ProcessorSelectorStatuses } from 'common/globalTypes';

import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { BaseHandler } from '@/lib/baseHandler';
import { ClassificationService } from '@/services/document-classification/service';
import { container } from '@/ioc';

class Handler extends BaseHandler {
  private service: ClassificationService;

  constructor() {
    super();
    this.service = container.get(ClassificationService);
  }

  @Post()
  async post(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      const accountId = req.account_id;
      if (!accountId) {
        req.logger.error('No account specified');
        res.status(401).end('No account specified');
        return res;
      }

      const { file } = req.body;
      if (!file) {
        req.logger.warn('File is not exist');
        res.status(400).end('File is not exist');
        return res;
      }

      req.logger.info('Starting document classification', {
        filename: file.name,
      });

      const result = await this.service.classifyDocument(file, accountId);

      req.logger.info('Document classification completed', {
        filename: file.name,
        result: result.result,
        typeResult: result.typeResult,
      });

      return res.status(200).json(result);
    } catch (error) {
      req.logger.error('Unexpected error in document classification', {
        error: error.message,
        stack: error.stack,
      });
      Sentry.captureException(error);
      return res
        .status(500)
        .json({ status: ProcessorSelectorStatuses.ERROR, result: null });
    }
  }
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '25mb',
    },
  },
};

export default withAuth(createHandler(Handler));
