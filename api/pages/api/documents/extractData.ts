import * as Sentry from '@sentry/nextjs';
import { removeLeadingTrailingChar } from 'common/helpers';
import { sha256 } from 'crypto-hash';
import { nanoid } from 'nanoid';

import { container } from '@/ioc';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import { isValidId } from '@/lib/prismaUtils';
import { EmailerService } from '@/services/emailer';
import { FirebaseStorageSignedUrlService } from '@/services/firebase-storage/signedUrl';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';

const EXTRACT_TABLE_TRIGGER = 'https://trigger.extracttable.com';
const EXTRACT_TABLE_GET_RESULT = 'https://getresult.extracttable.com';
const GHOST_SCRIPT_OPTIMIZER =
  'https://pdf-reader-965507431827.us-central1.run.app/optimize_pdf';
// let isPolling = false;

const handler = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  switch (req.method) {
    case 'GET':
      await getExtractDatum(req, res);
      break;
    case 'POST':
      await extractData(req, res);
      break;
    default:
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
};

export const config = {
  api: {
    responseLimit: false,
  },
};

interface IExtractData {
  url: string;
  filename: string;
  file?: File;
  account_id: string;
  uid: string;
  document_id?: number;
  extract_job_id?: string;
  update?: boolean;
  extractionId?: number;
}

interface IExtractDataResponse {
  JobId: string;
  JobStatus: string;
  Pages: number;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  Tables: any[];
  DownloadUrl?: string;
  ProTip?: string;
  message?: string;
}
interface CreditsResponse {
  usage: {
    credits: number;
    used: number;
    queued: number;
  };
}

const firebaseStorageSignedUrlService =
  container.get<FirebaseStorageSignedUrlService>(
    FirebaseStorageSignedUrlService
  );

const emailerService = container.get<EmailerService>(EmailerService);

const extractData = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const body = {
    ...req.body,
    uid: req.uid,
    account_id: req.account_id,
  } as IExtractData;
  const { extract_job_id, document_id, update } = body;
  try {
    if (!extract_job_id || update) {
      const document = await prisma.documents.findFirst({
        where: { id: document_id, account_id: req.account_id },
      });

      if (!document) {
        throw new Error('Document not found');
      }

      try {
        const creditsResponse = await fetch(
          process.env.EXTRACT_TABLE_VALIDATOR,
          {
            headers: {
              'x-api-key': process.env.EXTRACT_TABLE_KEY,
            },
          }
        );

        const creditsData = (await creditsResponse.json()) as CreditsResponse;
        const totalCredits = creditsData?.usage.credits;
        const usedCredits = creditsData?.usage.used;
        const remainingCredits = totalCredits - usedCredits;
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.log(`Current extractTable credits are ${remainingCredits}`);

        if (remainingCredits < 250) {
          await emailerService.sendEmail(
            '<EMAIL>',
            `ExtractTable credits running low (${remainingCredits} remaining)`,
            `ExtractTable is running low on credits. ${remainingCredits} credits remain.<br/><br/>Purchase more credits soon<br/>https://www.extracttable.com/v2/index.html#pricing<br/><br/>And transfer credits to existing key<br/>https://extracttable.com/transfer-api-credits.html<br/>`
          );
          // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          console.warn(
            `ExtractTable credits running low: ${remainingCredits} remaining`
          );
        }
      } catch (creditsError) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.error(
          `Error checking ExtractTable credits: ${creditsError.message}`
        );
        Sentry.captureException(creditsError);
      }

      const filePath = document.file_path || document.override_file_path;
      const filename = document.filename || document.override_filename;
      const _filename = removeLeadingTrailingChar(
        filename.split('/').pop(),
        '"'
      );

      let file = await firebaseStorageSignedUrlService.getStorageFile(filePath);
      let form = new FormData();
      form.append('input', file);

      // Trigger the extraction
      let triggerData = await triggerExtractTable(form);
      // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      let optimizedFile;

      // Check if extraction failed, and attempt optimization
      if (triggerData.status >= 400) {
        form = new FormData();
        form.append('file', file);
        optimizedFile = await optimizeFile(form);
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.warn(
          `ExtractTable failed with file ${_filename}. Retrying with optimized file.`
        );

        if (optimizedFile.status === 200) {
          const fileBlob = await optimizedFile.blob();
          const optimizedFilename = _filename.replace(
            /(\.[^.]+)$/,
            '-optimized$1'
          );

          file = new File([fileBlob], optimizedFilename, {
            type: fileBlob.type,
          });
          form = new FormData();
          form.append('input', file);
          triggerData = await triggerExtractTable(form);

          await saveOptimizedDocument(
            req,
            file,
            document_id,
            optimizedFilename
          );
        }
      }

      if (triggerData.status >= 400) {
        let error = `Error processing document (${triggerData.status}:${triggerData.statusText})`;
        if (optimizedFile && optimizedFile.status >= 400) {
          error += `\nError optimizing document (${optimizedFile.status}:${optimizedFile.statusText})`;
        }
        // Notify via email if HTTP 402: Payment Required
        if (triggerData.status === 402) {
          await emailerService.sendEmail(
            '<EMAIL>',
            'ExtractTable out of credits - document processing blocked',
            'ExtractTable is out of credits. This blocks document processing.<br/><br/>Purchase more credits<br/>https://www.extracttable.com/v2/index.html#pricing<br/><br/>And transfer credits to existing key<br/>https://extracttable.com/transfer-api-credits.html<br/>'
          );
        }
        throw new Error(error);
      }
      const extractRes = (await triggerData.json()) as IExtractDataResponse;
      const jobId = extractRes.JobId;
      if (!jobId || extractRes.JobStatus === 'Failed') {
        throw new Error('No job id found');
      }

      let data = null;

      // Wait up to ~10 seconds for result
      if (['Processing', 'Incomplete'].includes(extractRes.JobStatus)) {
        await new Promise((r) => setTimeout(r, 10000));
      }
      data = await pollingExtractData(jobId);
      await createOrUpdateExtractData(body, data);

      res.status(200).json(data);
    } else {
      // TODO change query extractions table
      const result = (await queryExtractDatas(document_id)).output;
      const data = JSON.parse(result) as IExtractDataResponse;
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.log(Object.keys(data), 'lalala');
      res.status(200).json(data);
    }
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(
      `Error in extracting data with ExtractTable: ${error.message}`
    );
    Sentry.captureException(error);
    res
      .status(500)
      .json({ error: 'Error in extracting data', message: error.message });
  }
};

const getExtractDatum = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const { jobId, uploadId } = req.query;
  try {
    const result = await getExtractData(jobId);
    let data = (await result.json()) as IExtractDataResponse;
    if (data.JobStatus === 'Success') {
      // If result has DownloadUrl, then download file
      if (data.DownloadUrl) {
        const fileData = await gotFileDataByUrl(data.DownloadUrl);
        data = fileData;
      }
    }

    const extractionId = +req.query.extractionId;

    if (!isValidId(extractionId)) {
      return res
        .status(400)
        .json({ message: 'Found invalid id before performing the operation' });
    }

    if (data.JobStatus) {
      const params = {
        document_id: +uploadId,
        account_id: req.account_id,
        uid: req.uid,
        extractionId,
      };
      await createOrUpdateExtractData(params, data);
      res.status(200).json(data);
    } else if (data.ProTip || data.message) {
      // Delete the extract record by id
      await prisma.extractions.update({
        where: { id: extractionId },
        data: {
          state: 'deleted',
        },
      });
      res.status(200).json({ message: data.ProTip, type: 'expired' });
    }
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error in getting extract data: ${error.message}`);
    Sentry.captureException(error);
    res.status(500).json({ error: 'Error in getting extract data' });
  }
};

async function pollingExtractData(jobId: string, times = 30) {
  try {
    let data = null;
    for (let i = 0; i < times; i++) {
      const result = await getExtractData(jobId);
      data = (await result.json()) as IExtractDataResponse;
      if (data.JobStatus === 'Success') {
        // If result has DownloadUrl, then download file
        if (data.DownloadUrl) {
          const fileData = await gotFileDataByUrl(data.DownloadUrl);
          data = fileData;
        }
        break;
      }
      await new Promise((r) => setTimeout(r, 1000));
    }
    return data;
  } catch (error) {
    throw new Error(error);
  }
}

// Save the optimized document into the database
const saveOptimizedDocument = async (
  req: ExtNextApiRequest,
  file: File,
  document_id: number,
  optimizedFilename: string
) => {
  try {
    const fileContent = await file.arrayBuffer();
    const fileHash = await sha256(fileContent);
    const filePath = `uploads/${req.account_id}/${nanoid()}-${optimizedFilename}`;

    await prisma.documents.update({
      where: { id: document_id, account_id: req.account_id },
      data: {
        optimized_filename: optimizedFilename,
        optimized_file_path: filePath,
        optimized_file_hash: fileHash,
        updated_at: new Date(),
        updated_by: req.uid,
        updated_proxied_by: req.ouid,
      },
    });

    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.log(
      `Optimized file (${optimizedFilename}) saved successfully as ${filePath}`
    );
  } catch (err) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(
      'Error updating optimized document in the database:',
      err.message
    );
    Sentry.captureException(err);
  }
};

/**
 * Create the extract data
 */
async function createOrUpdateExtractData(
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  body: IExtractData | any,
  datas: IExtractDataResponse
) {
  let result = null;
  if (!['Success', 'SUCCEEDED'].includes(datas.JobStatus)) return;

  const exist = await prisma.extractions.findFirst({
    where: { result_id: datas.JobId },
  });

  if (!exist) {
    result = await prisma.extractions.create({
      data: {
        document_id: body.document_id,
        account_id: body.account_id,
        uid: body.uid,
        method: 'extractTable',
        output: JSON.stringify(datas),
        result: datas.JobStatus,
        result_id: datas.JobId,
        str_id: nanoid(),
      },
    });
  } else {
    if (!exist?.extractionId) {
      throw new Error(`Extraction id not found, investigation required!`);
    }

    result = await prisma.extractions.update({
      where: { id: exist.extractionId },
      data: {
        output: JSON.stringify(datas),
        result: datas.JobStatus,
        result_id: datas.JobId,
      },
    });
  }
  return result;
}

async function queryExtractDatas(id: number) {
  const datas = await prisma.extractions.findFirst({
    where: { document_id: id },
    select: {
      output: true,
      result_id: true,
    },
  });
  return datas;
}

async function gotFileDataByUrl(url: string) {
  try {
    const fileResponse = await fetch(url);
    const res = await fileResponse.json();
    return res as IExtractDataResponse;
  } catch (error) {
    throw new Error(error);
  }
}

/**
 * Post the result of the extraction
 * @param form params
 * @returns
 */
async function triggerExtractTable(form: FormData) {
  try {
    const extractData = await fetch(EXTRACT_TABLE_TRIGGER, {
      method: 'POST',
      body: form,
      headers: {
        'x-api-key': process.env.EXTRACT_TABLE_KEY,
      },
    });
    return extractData;
    // Return extractData.json() as unknown as IExtractDataResponse;
  } catch (error) {
    throw new Error(error);
  }
}

/**
 * Post the file the optimize
 * @param form params
 * @returns
 */
const optimizeFile = async (form: FormData) => {
  const optmizedFile = await fetch(GHOST_SCRIPT_OPTIMIZER, {
    method: 'POST',
    body: form,
    headers: {
      token: process.env.PDF_OPTIMIZER_TOKEN,
    },
  });
  return optmizedFile;
};

/**
 * Get the result of the extraction
 * @param jobId jobId
 * @returns
 */
export async function getExtractData(jobId: string) {
  try {
    const res = await fetch(`${EXTRACT_TABLE_GET_RESULT}/?JobId=${jobId}`, {
      method: 'GET',
      headers: {
        'x-api-key': process.env.EXTRACT_TABLE_KEY,
      },
    });
    return res;
  } catch (error) {
    throw new Error(error);
  }
}

export default withAuth(handler);
