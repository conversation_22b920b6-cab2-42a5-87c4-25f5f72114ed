import type { NextApiRequest } from 'next';
import { createHandler, Get, Req } from 'next-api-decorators';
import { GROUP_BY_VALUES } from 'common/documents/documents.constants';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import { DocumentsService } from '@/services/documents';
import type { ExtNextApiRequest } from '@/types';

class Handler extends BaseHandler {
  private service: DocumentsService;
  constructor() {
    super();
    this.service = container.get(DocumentsService);
  }

  @Get()
  async get(@Req() req: ExtNextApiRequest & NextApiRequest) {
    const { group_by, companies, ...rest } = req.query;
    if (group_by === GROUP_BY_VALUES.COMPANY)
      return await this.service.getDocumentsGroupByCompanies({
        account_id: req.account_id,
        companies: companies ? companies.split(',') : undefined,
        group_by: group_by as GROUP_BY_VALUES,
        ...rest,
      });

    return await this.service.getMonthList({
      account_id: req.account_id,
      companies: companies ? companies.split(',') : undefined,
      group_by: group_by as GROUP_BY_VALUES,
      ...rest,
    });
  }
}

export default withAuth(createHandler(Handler));
