import { create<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'next-api-decorators';
import type { NextApiRequest, NextApiResponse } from 'next';
import * as Sentry from '@sentry/nextjs';

import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { BaseHandler } from '@/lib/baseHandler';
import { DocumentsService } from '@/services/documents';
import { container } from '@/ioc';
import { QueueService } from '@/services/queue';
import { Queue } from '@/services/queue/types';
import { DataProcessingTypes } from '@/types';
import { DocumentStatuses } from 'common/globalTypes';
import type { DocumentProcessingDTO } from 'common/dto/document_processing/dto';
import prisma from '@/lib/prisma';
import { DataStates } from '@/types';

class Handler extends BaseHandler {
  documentsService: DocumentsService;
  constructor() {
    super();
    this.documentsService = container.get(DocumentsService);
  }

  @Patch()
  async bulkEdit(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      const { ids, company_str_id, type } = req.body;

      const shouldCheckAutoProcessing = company_str_id || type;
      let documentsToAutoProcess = [];

      if (shouldCheckAutoProcessing) {
        const existingDocuments = await prisma.documents.findMany({
          where: {
            id: { in: ids },
            account_id: req.account_id,
            state: DataStates.ACTIVE,
          },
          select: {
            id: true,
            str_id: true,
            filename: true,
            file_path: true,
            file_type: true,
            company_str_id: true,
            type: true,
            status: true,
            statement_amount: true,
            companies: {
              where: { state: DataStates.ACTIVE },
            },
          },
        });

        documentsToAutoProcess = existingDocuments.filter((doc) => {
          if (doc.status !== DocumentStatuses.NEW) {
            return false;
          }
          const previouslyMissingCompany =
            !doc.company_str_id && company_str_id;

          const previouslyMissingType = !doc.type && type;

          if (previouslyMissingCompany || previouslyMissingType) {
            const finalCompanyId = company_str_id || doc.company_str_id;
            const finalType = type || doc.type;

            return finalCompanyId && finalType;
          }

          return false;
        });
      }

      await this.documentsService.bulkEdit({
        account_id: req.account_id,
        clearDataFields: req.query.clearDataFields?.split(',') || [],
        ...req.body,
        uid: req.uid,
        ouid: req.ouid,
      });

      if (documentsToAutoProcess.length > 0) {
        const queueService = container.get<QueueService>(QueueService);
        let successfulQueueCount = 0;

        for (const doc of documentsToAutoProcess) {
          try {
            await queueService.createTask<DocumentProcessingDTO>({
              account: {
                account_id: req.account_id,
                uid: req.uid,
                ouid: req.ouid,
                role_id: req.role_id,
              },
              queue: Queue.DOCUMENT_PROCESSING,
              type: DataProcessingTypes.document_processing,
              url: process.env.CLOUD_TASKS_URL,
              payload: {
                account_id: req.account_id,
                uid: req.uid,
                document_id: doc.id,
                document_str_id: doc.str_id,
                file_name: doc.filename,
                file_path: doc.file_path,
                file_type: doc.file_type,
                type: type || doc.type,
                companies: doc.companies || [],
                statement_amount: doc.statement_amount,
              },
            });

            successfulQueueCount++;

            req.logger.info(
              'Auto-processing triggered for document via bulk edit',
              {
                document_id: doc.str_id,
                reason: 'bulk_edit_fields_completed',
                company_id: company_str_id || doc.company_str_id,
                type: type || doc.type,
              }
            );
          } catch (queueError) {
            req.logger.error('Failed to queue auto-processing for document', {
              document_id: doc.str_id,
              error: queueError,
            });
          }
        }

        req.logger.info('Bulk edit completed with auto-processing', {
          total_updated: ids.length,
          auto_processed: successfulQueueCount,
          failed_queue: documentsToAutoProcess.length - successfulQueueCount,
        });

        res.status(200).json({
          message: 'Documents updated successfully',
          auto_processed_count: successfulQueueCount,
          failed_queue_count:
            documentsToAutoProcess.length - successfulQueueCount,
        });
      } else {
        res.status(200).json({
          message: 'Documents updated successfully',
          auto_processed_count: 0,
        });
      }
    } catch (ex) {
      if (ex.validationErr) {
        return res
          .status(400)
          .json({ message: 'Validation error', errors: ex.errors });
      }
      req.logger.error('Error processing bulk edit', { error: ex });
      Sentry.captureException(ex);
      res.status(500).end('Internal Server Error');
    }
  }
}

export default withAuth(createHandler(Handler));
