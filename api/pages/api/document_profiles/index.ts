import * as Sentry from '@sentry/nextjs';
import { nanoid } from 'nanoid';
import { numberOrDefault } from 'common/helpers';
import {
  createHandler,
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
} from 'next-api-decorators';
import type { NextApiRequest, NextApiResponse } from 'next';
import { isNill } from 'common/helpers';

import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { DocumentProfileService } from '@/services/document-profiles/service';
import { container } from '@/ioc';

class Handler extends BaseHandler {
  private service: DocumentProfileService;

  constructor() {
    super();
    this.service = container.get(DocumentProfileService);
  }

  @Get()
  async getData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await getDocumentProfiles(req, res);
  }

  @Post()
  async createData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await postDocumentProfile(req, res, this.service);
  }

  @Patch()
  async updateData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await patchDocumentProfile(req, res, this.service);
  }

  @Delete()
  async deleteData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await deleteDocumentProfile(req, res, this.service);
  }
}

export const _getDocumentProfiles = async (req) => {
  const service = container.get(DocumentProfileService);

  try {
    const {
      limit = undefined,
      orderBy,
      sort,
      page = '0',
      q: _q = '',
      status,
      priority,
      owner,
    } = req.query;

    const isDynamicSelect = req.query.is_dynamic_select === 'true';

    const pageNum = isDynamicSelect ? 0 : Math.max(0, numberOrDefault(page, 0));
    const limitNum: number | undefined = isDynamicSelect
      ? undefined
      : numberOrDefault(limit, 'undefined');
    const q = typeof _q === 'string' ? _q.trim() : '';

    let parsedPriority: number | undefined;
    if (!isNill(priority)) {
      if (Array.isArray(priority)) {
        const firstValidPriority = priority.find((p) => !isNill(p));
        if (firstValidPriority) {
          const priorityNum = parseInt(firstValidPriority, 10);
          // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          if (!isNaN(priorityNum) && priorityNum >= 0 && priorityNum <= 3) {
            parsedPriority = priorityNum;
          }
        }
      } else {
        const priorityNum =
          typeof priority === 'string' ? parseInt(priority, 10) : priority;
        // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        if (!isNaN(priorityNum) && priorityNum >= 0 && priorityNum <= 3) {
          parsedPriority = priorityNum;
        }
      }
    }

    let parsedOwner: string | undefined;
    if (!isNill(owner)) {
      parsedOwner = typeof owner === 'string' ? owner.trim() : owner;
    }

    const filters: {
      q: string;
      status?: string;
      priority?: number;
      owner?: string;
      order_by?: string;
      order?: string;
      ids?: string[];
    } = {
      q,
      status: typeof status === 'string' ? status : undefined,
      priority: parsedPriority,
      owner: parsedOwner,
      order_by: typeof orderBy === 'string' ? orderBy : undefined,
      order: typeof sort === 'string' ? sort : undefined,
    };

    const pagination = {
      page: pageNum,
      limit: limitNum,
    };

    const result = await service.getDocumentProfiles(filters, pagination);

    return isDynamicSelect ? result.data : result;
  } catch (error) {
    req.logger.error(
      `An error occurred when getting document profiles: ${error.message}`
    );
    throw error;
  }
};

const getDocumentProfiles = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  try {
    const result = await _getDocumentProfiles(req);
    res.json(result);
  } catch (error) {
    req.logger.error(
      `An error occurred when getting document profiles: ${error.message}`
    );
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const postDocumentProfile = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  service: DocumentProfileService
) => {
  try {
    const profilePayload = {
      ...req.body,
      str_id: nanoid(),
      name: req.body.name?.trim(),
    };

    const account = {
      account_id: req.account_id,
      uid: req.uid,
      ouid: req.ouid,
    };

    const data = await service.createProfileWithAssociations(
      profilePayload,
      req.body,
      account
    );

    res.status(201).json(data);
  } catch (error) {
    req.logger.error(
      `An error occurred when creating document profile: ${error.message}`
    );
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const patchDocumentProfile = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  service: DocumentProfileService
) => {
  try {
    const { id: profileId, str_id: profileStrId } = req.body;

    if (!profileId || !Number.isInteger(profileId) || profileId <= 0) {
      return res.status(400).json({ error: 'Valid profile ID is required' });
    }

    const updateData = {
      ...req.body,
      name: req.body.name?.trim(),
    };

    const account = {
      account_id: req.account_id,
      uid: req.uid,
      ouid: req.ouid,
    };

    const data = await service.updateProfileWithAssociations(
      Number(profileId),
      profileStrId,
      updateData,
      req.body,
      account
    );

    res.status(200).json(data);
  } catch (error) {
    req.logger.error(`Error updating document profile: ${error.message}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const deleteDocumentProfile = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  service: DocumentProfileService
) => {
  try {
    const { ids } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return res
        .status(400)
        .json({ error: 'IDs array is required and cannot be empty' });
    }

    const account = {
      account_id: req.account_id,
      uid: req.uid,
      ouid: req.ouid,
    };

    const result = await service.softDeleteProfilesWithAssociations(
      ids,
      account
    );

    res.status(200).json({
      status: 'OK',
      deleted_count: result.count,
      message: `Successfully deleted ${result.count} document profile(s)`,
    });
  } catch (error) {
    req.logger.error(`Error deleting document profile: ${error.message}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

export default withAuth(createHandler(Handler), { adminOnly: true });
