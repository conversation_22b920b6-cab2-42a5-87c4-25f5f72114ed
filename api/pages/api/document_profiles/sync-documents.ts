import { create<PERSON><PERSON><PERSON>, <PERSON>, Req } from 'next-api-decorators';
import type { NextApiRequest } from 'next';

import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest } from '@/types';
import { DocumentProfileService } from '@/services/document-profiles/service';
import { container } from '@/ioc';

class SyncHandler extends BaseHandler {
  private service: DocumentProfileService;

  constructor() {
    super();
    this.service = container.get(DocumentProfileService);
  }

  @Post()
  async syncDocuments(@Req() req: ExtNextApiRequest & NextApiRequest) {
    const { profileStrId } = req.body;

    if (!profileStrId) {
      throw new Error('Profile ID is required');
    }

    const account = {
      uid: req.uid,
      ouid: req.ouid,
    };

    const result = await this.service.syncDocuments(profileStrId, account);
    return result;
  }
}

export default with<PERSON>uth(create<PERSON>and<PERSON>(SyncHandler), { adminOnly: true });
