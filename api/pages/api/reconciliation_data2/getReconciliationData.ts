import type { Prisma, report_data, statement_data } from '@prisma/client';
import { isNill, numberOrDefault } from 'common/helpers';
import dayjs from 'dayjs';
import { isEqual } from 'lodash-es';
import { customViewDefault } from 'common/constants/account_role_settings';
import { DEFAULT_FILTER } from 'common/constants';

import FieldValuesCache from '@/lib/field-values/FieldValuesCache';
import normalizeFieldValues from '@/lib/field-values/normalizeFieldValues';
import {
  chunkArray,
  filterFieldOptions,
  limitConcurrency,
} from '@/lib/helpers';
import { prismaClient } from '@/lib/prisma';
import { getFieldConfigs } from '@/lib/reconciliationV2';
import { DataStates, type ExtNextApiRequest, Roles } from '@/types';
import {
  buildReconciliationQuery,
  loadFilterOption,
} from './buildReconciliationQuery';
import { container } from '@/ioc';
import { AccountConfigService } from '@/services/account/config';
import { processContacts } from '@/lib/helpers/processContacts';
import { getContactsOption } from './getContactsOption';

const filterFieldsCache = new FieldValuesCache('reconciliation_data');
export const getReconciliationsDataV2 = async (
  req: ExtNextApiRequest,
  isExportData = false
) => {
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.log('[getReconciliationData] Starting function execution');
  console.time('[getReconciliationData] Total execution time');
  const account_id = req.account_id;

  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.log('[getReconciliationData] Building query');
  console.time('[getReconciliationData] Building query');
  const {
    where,
    skip,
    take,
    filterList,
    filtersWhere,
    userData,
    additionalFilterFields,
    statementDataWhere,
    canQueryFromReportData,
    canQueryFromStatementData,
  } = await buildReconciliationQuery(req);
  console.timeEnd('[getReconciliationData] Building query');

  // GET DATA
  const allData = [];

  // Get total counts for pagination
  let reportDataCount = 0;
  let reportDataTake: number | undefined;

  if (canQueryFromReportData) {
    reportDataCount = await prismaClient.report_data.count({
      where,
    });

    if (typeof take === 'number') {
      reportDataTake = Math.max(0, Math.min(take, reportDataCount - skip));
    }

    // Fetch report data with pagination
    // Data for ✅ Reconciled, ⚠️ Missing commissions data
    // If reportDataTake is undefined, we fetch all data
    if (reportDataTake > 0 || reportDataTake === undefined) {
      const _reportData = await prismaClient.report_data.findManyWithPagination(
        {
          where,
          include: {
            statement_data: {
              where: {
                state: DataStates.ACTIVE,
                account_id,
              },
              include: {
                document: {
                  select: {
                    filename: true,
                    str_id: true,
                  },
                },
              },
            },
            document: {
              select: {
                filename: true,
                str_id: true,
              },
            },
          },
          take: reportDataTake,
          skip: skip < reportDataCount ? skip : undefined,
          orderBy: [{ id: 'asc' }],
        }
      );

      allData.push(..._reportData);
    }
  }

  let statementDataWithoutReportDataCount = 0;
  if (canQueryFromStatementData) {
    statementDataWithoutReportDataCount =
      await prismaClient.statement_data.count({
        where: statementDataWhere,
      });

    // biome-ignore lint/suspicious/noImplicitAnyLet: Initial?
    let statementDataTake;

    if (typeof take === 'number') {
      statementDataTake = Math.min(
        take - (reportDataTake ?? 0),
        statementDataWithoutReportDataCount
      );
    }
    const statementDataSkip =
      skip > reportDataCount ? skip - reportDataCount : 0;

    // Undefined means no limit
    if (statementDataTake > 0 || statementDataTake === undefined) {
      // Fetch statement data with pagination
      // Data for ⚠️ Missing policy data
      const _statementDataWithoutReportData =
        await prismaClient.statement_data.findManyWithPagination({
          where: statementDataWhere,
          include: {
            document: true,
          },
          take: statementDataTake,
          skip: statementDataSkip,
          orderBy: [{ id: 'asc' }],
        });

      allData.push(
        ..._statementDataWithoutReportData.map((item) => {
          // Statement data missing policy data
          return {
            statement_data: [item],
            policy_id: item.policy_id,
          };
        })
      );
    }
  }

  // Calculate pagination for both tables
  const totalItems = reportDataCount + statementDataWithoutReportDataCount;

  const [statementPaymentDateFirst, statementPaymentDateLast] =
    await Promise.all([
      prismaClient.statement_data.findFirst({
        where: {
          report: loadFilterOption(req),
          payment_date: {
            not: null,
          },
          state: DataStates.ACTIVE,
          account_id,
        },
        orderBy: {
          payment_date: 'asc',
        },
        select: {
          payment_date: true,
        },
      }),
      prismaClient.statement_data.findFirst({
        where: {
          report: loadFilterOption(req),
          payment_date: {
            not: null,
          },
          state: DataStates.ACTIVE,
          account_id,
        },
        orderBy: {
          payment_date: 'desc',
        },
        select: {
          payment_date: true,
        },
      }),
    ]);

  const accountSettings = await prismaClient.accounts.findFirst({
    where: { str_id: account_id, state: DataStates.ACTIVE },
  });
  const mode = accountSettings?.mode ?? 'insurance'; // Default to insurance

  const fieldConfigs = getFieldConfigs(mode);

  const account = {
    uid: req.uid,
    ouid: req.ouid,
    account_id,
    role_id: req.role_id,
  };

  let result = [];

  // Process each report
  for (const report of allData) {
    const reconciliations = convertToReconciliationStatus(
      report,
      fieldConfigs,
      account
    );
    result.push(...reconciliations);
  }

  const includeCompTypeField = true;

  // Add compensation_type field to response
  if (includeCompTypeField) {
    result = result.map((item) => {
      if (!Array.isArray(item.statements) || item.statements.length === 0) {
        item.compensation_type = null;
      } else {
        const compensation_types = item.statements
          .map((statement: statement_data) => statement.compensation_type)
          .filter(Boolean);
        item.compensation_type = [...new Set(compensation_types)];
      }

      return item;
    });
  }

  if (+req.role_id === Roles.PRODUCER) {
    result = result.map((item) => {
      if (item.report_data?.contacts_split) {
        item.report_data.contacts_split = Object.fromEntries(
          Object.entries(item.report_data.contacts_split).filter(
            // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            ([k, v]) => k === userData.user_contact[0].str_id
          )
        );
      }
      // TODO: Calculate commission amount using joins with statement relations and/or separate commissions table when avaialble.
      if (Array.isArray(item.statement_ids) && item.statement_ids.length > 0) {
        const statementData = item.statements;
        const commissionAmount = statementData.reduce((acc, cur) => {
          return (
            acc +
            +(cur?.agent_commissions?.[userData.user_contact[0].str_id] ?? 0)
          );
        }, 0);
        item.agent_commission_amount = commissionAmount;
        item.agent_commission_amount_pct = item.commissionable_premium_amount
          ? +commissionAmount.toFixed(2) / item.commissionable_premium_amount
          : null;
      }
      return item;
    });
  } else {
    const processItem = (item) => {
      if (Array.isArray(item.statement_ids) && item.statement_ids.length > 0) {
        const statementData = item.statements;
        // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        const commissionAmount = statementData.reduce((acc, cur) => {
          return Object.entries(cur?.agent_commissions ?? {}).reduce(
            (acc, [k, v]) => (+acc + k === 'total' ? 0 : +v),
            0
          );
        }, 0);

        item.agent_commission_amount = commissionAmount;
        item.agent_commission_amount_pct = item.commissionable_premium_amount
          ? +commissionAmount.toFixed(2) / item.commissionable_premium_amount
          : null;
      }
      return item;
    };

    result = result.map((item) => {
      return processItem(item);
    });
  }

  // Add contact names and contact split info to the response for export data
  if (isExportData) {
    await processContacts(result);
  }

  const availableFields = await prismaClient.account_role_settings.findUnique({
    where: {
      account_id_role_id_custom_view_name: {
        account_id: String(account_id),
        role_id: parseInt(req.role_id),
        custom_view_name: customViewDefault,
      },
    },
    select: {
      reconciliation_view: true,
    },
  });

  // Check if the account has the policy_document and statements fields enabled
  let policyDocumentFilter = true;
  let statementDocumentFilter = true;
  let compensationTypeFilter = true;
  let contactsFilter = true;
  if (
    Array.isArray(availableFields?.reconciliation_view) &&
    availableFields.reconciliation_view.length > 0
  ) {
    policyDocumentFilter =
      availableFields.reconciliation_view.includes('report_data_id');
    statementDocumentFilter =
      availableFields.reconciliation_view.includes('statements');
    compensationTypeFilter =
      availableFields.reconciliation_view.includes('compensation_type');
    contactsFilter = availableFields.reconciliation_view.includes('contacts');
  }

  const filterWhere = {
    account_id,
    AND: [],
  };

  if (req.query.id) {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (filterWhere.AND as any).push({ str_id: req.query.id });
  } else {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (filterWhere.AND as any).push({ state: { in: ['active', 'grouped'] } });
  }

  const _effectiveDateStart = await prismaClient.report_data.findFirst({
    where: loadFilterOption(req, { effective_date: { not: null } }),
    select: {
      effective_date: true,
    },
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    orderBy: [{ ['effective_date']: 'asc' }],
  });

  const _effectiveDateEnd = await prismaClient.report_data.findFirst({
    where: loadFilterOption(req, { effective_date: { not: null } }),
    select: {
      effective_date: true,
    },
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    orderBy: [{ ['effective_date']: 'desc' }],
  });

  let _fieldOptions = await filterFieldValues(
    account_id,
    filterList,
    where,
    statementDataWhere,
    canQueryFromReportData,
    canQueryFromStatementData
  );

  if (additionalFilterFields.length > 0) {
    const filterOptionsAdditional = await filterFieldValues(
      account_id,
      additionalFilterFields,
      filtersWhere,
      statementDataWhere,
      canQueryFromReportData,
      canQueryFromStatementData
    );
    _fieldOptions = Object.assign({}, _fieldOptions, filterOptionsAdditional);
  }

  let fieldOptions: Record<
    string,
    string | Date | undefined | { id: string; name: string }[] | string[][]
  > = {
    report_data_id: undefined,
    statements: undefined,
    compensation_type: undefined,
    effective_date_start: _effectiveDateStart?.effective_date,
    effective_date_end: _effectiveDateEnd?.effective_date,
    payment_date_first: statementPaymentDateFirst?.payment_date,
    payment_date_last: statementPaymentDateLast?.payment_date,
    ..._fieldOptions,
  };

  if (!policyDocumentFilter) {
    delete fieldOptions.report_data_id;
  }
  if (!statementDocumentFilter) {
    delete fieldOptions.statements;
  }
  if (!compensationTypeFilter) {
    delete fieldOptions.compensation_type;
  }

  const augmentRequestRecords = async () => {
    // Check if the account has the policies_show_grouped setting enabled
    const accountData = await prismaClient.accounts.findUnique({
      where: { str_id: account_id, state: DataStates.ACTIVE },
      select: {
        policies_show_grouped: true,
      },
    });

    // If the account has the policies_show_grouped setting enabled, we need to get the parent report data id for each reconciled policy
    if (accountData.policies_show_grouped) {
      const reportStrIds = result
        .map((item) => item.report_str_id)
        .filter(Boolean);
      const chunkData = chunkArray(reportStrIds, 10000);

      const _policyData = await limitConcurrency(
        async (chunk) => {
          const policyData = await prismaClient.report_data.findMany({
            where: { str_id: { in: chunk } },
            select: {
              str_id: true,
              parent_id: true,
              children_report_data: {
                select: {
                  str_id: true,
                  policy_id: true,
                },
              },
            },
          });
          return policyData;
        },
        chunkData,
        10
      );

      const policyData = _policyData.flat();
      const policyDataMap = new Map(
        policyData.map((item) => [item.str_id, item])
      );

      const parentReportDataIds = policyData
        .map((item) => item.parent_id)
        .filter(Boolean);
      const chunkedParentReportDataIds = chunkArray(parentReportDataIds, 10000);

      const _responsePolicyData = await limitConcurrency(
        async (chunk) => {
          const responsePolicyData = await prismaClient.report_data.findMany({
            where: { id: { in: chunk } },
            select: {
              id: true,
              str_id: true,
              policy_id: true,
            },
          });
          return responsePolicyData;
        },
        chunkedParentReportDataIds,
        10
      );

      const responsePolicyData = _responsePolicyData.flat();

      const responsePolicyDataMap = new Map(
        responsePolicyData.map((item) => [
          item.id,
          { str_id: item.str_id, policy_id: item.policy_id },
        ])
      );

      for (const item of result) {
        if (item.report_str_id) {
          const policy = policyDataMap.get(item.report_str_id);
          // If the policy has a parent report data id, we add a new field to the response with the parent report data id
          // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          if (policy && policy.parent_id) {
            item.report_data_parent = responsePolicyDataMap.get(
              policy.parent_id
            );
          }
          // If the policy has children report data, we add a new field to the response with the children report data ids
          if (
            // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            policy &&
            policy.children_report_data &&
            policy.children_report_data.length > 0
          ) {
            item.children_report_data_ids = policy.children_report_data.map(
              (child) => ({
                str_id: child.str_id,
                policy_id: child.policy_id,
              })
            );
            item.children_report_data = policy.children_report_data;
          }
        }
      }
    }
  };

  const augmentRequestRecordsPromise = augmentRequestRecords();

  // Add items to compensation_type filter only if the statements field exists in the response
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let getCompensationTypeFieldValuesPromise: Promise<any> | undefined;
  if (compensationTypeFilter) {
    const filterWhereStatementDocs = structuredClone(filterWhere);
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (filterWhereStatementDocs.AND as any).push({
      statement_ids: { not: null },
    });
    const getCompensationTypeFieldValues = async () => {
      const OrConditions = [];

      if (canQueryFromStatementData) {
        OrConditions.push({ ...statementDataWhere });
      }

      if (canQueryFromReportData) {
        OrConditions.push({ report: where });
      }

      const compTypeResults = await prismaClient.statement_data.findMany({
        where: {
          state: DataStates.ACTIVE,
          account_id,
          OR: OrConditions,
        },
        select: {
          compensation_type: true,
        },
        distinct: ['compensation_type'],
      });

      const compensation_type = compTypeResults
        .map<string>(({ compensation_type }) => compensation_type)
        .filter((compType) => !!compType);

      compensation_type.sort((a, b) => a.localeCompare(b));

      return compensation_type;
    };

    getCompensationTypeFieldValuesPromise = getCompensationTypeFieldValues();
  }

  // Add items to Policy document filter only if the report_data_id field exists in the response
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let getReportDataIdPromise: Promise<any> | undefined;
  if (policyDocumentFilter) {
    const getReportDataId = async () => {
      const rawQuery = () =>
        prismaClient.$queryRaw`select d.filename from report_data rd inner join documents d on d.str_id = rd.document_id where rd.account_id = ${account_id} and rd.state in ('active', 'grouped') and d.state = 'active' group by 1`;

      const filterWherePolicyDocs = structuredClone(filterWhere);
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (filterWherePolicyDocs.AND as any).push({
        report_data: {
          document: { isNot: null },
        },
      });

      const policyDocuments = await rawQuery();
      const documents = Array.isArray(policyDocuments)
        ? policyDocuments.map((doc) => doc.filename)
        : [];

      const report_data_id = [DEFAULT_FILTER.BLANK_OPTION, ...documents];
      report_data_id.sort((a, b) => a.localeCompare(b));

      return report_data_id;
    };

    getReportDataIdPromise = getReportDataId();
  }

  //   Add items to statement documents filter only if the statements field exists in the response
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let getStatementFieldValuesPromise: Promise<any> | undefined;
  if (statementDocumentFilter) {
    const getStatementFieldValues = async () => {
      const statementsDocuments = await prismaClient.statement_data.findMany({
        where: {
          account_id,
          state: DataStates.ACTIVE,
          report: where,
        },
        include: {
          document: {
            select: {
              filename: true,
            },
          },
        },
      });

      // Map documents and remove duplicates using Set with composite key
      const documents = Array.from(
        new Map(
          statementsDocuments.map((item) => [
            `${item.id}-${item.document?.filename}`,
            {
              id: item.id,
              name: item.document?.filename,
            },
          ])
        ).values()
      );
      const statements = [
        { id: '-1', name: DEFAULT_FILTER.BLANK_OPTION },
        ...documents,
      ];
      statements.sort((a, b) => {
        if (a.id === '-1') return -1;
        if (!a?.name) return -1;
        if (!b?.name) return 1;
        return a.name.localeCompare(b.name);
      });

      return statements;
    };

    getStatementFieldValuesPromise = getStatementFieldValues();
  }
  let getContactsPromise: Promise<{ id: string; name: string }[]> | undefined;
  if (contactsFilter && Array.isArray(fieldOptions.contacts)) {
    getContactsPromise = getContactsOption(fieldOptions.contacts as string[][]);
  }

  await augmentRequestRecordsPromise;

  if (getReportDataIdPromise !== undefined) {
    fieldOptions.report_data_id = await getReportDataIdPromise;
  }

  if (getStatementFieldValuesPromise !== undefined) {
    fieldOptions.statements = await getStatementFieldValuesPromise;
  }

  if (getCompensationTypeFieldValuesPromise !== undefined) {
    fieldOptions.compensation_type =
      await getCompensationTypeFieldValuesPromise;
  }

  if (getContactsPromise !== undefined) {
    fieldOptions.contacts = await getContactsPromise;
  }

  let excludedKeys = [];
  if (parseInt(req.role_id) !== Roles.ACCOUNT_ADMIN) {
    excludedKeys = [
      'effective_date_start',
      'effective_date_end',
      'payment_date_first',
      'payment_date_last',
    ];
  } else {
    excludedKeys = [
      'effective_date_start',
      'effective_date_end',
      'payment_date_first',
      'payment_date_last',
      'report_data_id',
      'statement_documents',
    ];
  }
  const dataObject = result ? result[0] : null;
  fieldOptions = filterFieldOptions(dataObject, fieldOptions, excludedKeys);

  const statementsCommissionAmount =
    await prismaClient.statement_data.aggregate({
      _sum: {
        commissionable_premium_amount: true,
      },
      where: {
        state: DataStates.ACTIVE,
        account_id,
        report: where,
      },
    });

  const sum = await prismaClient.report_data.aggregate({
    where,
    _sum: {
      commissionable_premium_amount: true,
      commissions_expected: true,
      premium_amount: true,
      customer_paid_premium_amount: true,
    },
  });

  const balance =
    (sum?._sum?.commissions_expected?.toNumber() ?? 0) -
    (statementsCommissionAmount?._sum?.commissionable_premium_amount?.toNumber() ??
      0);

  return {
    count: totalItems,
    fieldOptions,
    totals: {
      commissionable_premium_amount: sum?._sum?.commissionable_premium_amount,
      balance,
      commissions_expected: sum?._sum?.commissions_expected,
      premium_amount: sum?._sum?.premium_amount,
      customer_paid_premium_amount: sum?._sum?.customer_paid_premium_amount,
    },
    data: result,
  };
};

const filterFieldValues = async (
  account_id: string,
  filterFields: string[],
  where: Prisma.report_dataWhereInput,
  statementWhere: Prisma.statement_dataWhereInput,
  canQueryFromReportData: boolean,
  canQueryFromStatementData: boolean
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
): Promise<Record<string, any[]>> => {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const queries = filterFields.map<Promise<[string, any[]]>>(
    async (fieldName) => {
      const values = await filterFieldsCache.get(
        account_id,
        fieldName,
        {
          ...where,
          ...statementWhere,
          canQueryFromReportData,
          canQueryFromStatementData,
        }, // Key cache
        async () => {
          const result = [];
          if (canQueryFromReportData) {
            if (fieldName === 'carrier_name') {
              const records = await prismaClient.statement_data.findMany({
                select: {
                  carrier_name: true,
                },
                distinct: ['carrier_name'],
                where: {
                  report: where,
                },
              });
              result.push(
                ...normalizeFieldValues(
                  records.map((record) => record.carrier_name)
                )
              );
            } else {
              // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              const records: any[] = await prismaClient.report_data.findMany({
                select: { [fieldName]: true },
                where,
                distinct: [fieldName as keyof report_data],
              });
              result.push(
                ...normalizeFieldValues(
                  records.map((record) => record[fieldName])
                )
              );
            }
          }

          if (
            canQueryFromStatementData &&
            [
              'carrier_name',
              'writing_carrier_name',
              'product_name',
              'product_type',
              'agent_name',
              'contacts',
            ].includes(fieldName)
          ) {
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            const records: any[] = await prismaClient.statement_data.findMany({
              select: { [fieldName]: true },
              distinct: [fieldName as keyof statement_data],
              where: statementWhere,
            });
            result.push(
              ...normalizeFieldValues(
                records.map((record) => record[fieldName])
              )
            );
          }
          return normalizeFieldValues(result);
        }
      );

      return [fieldName, values];
    }
  );

  const resultEntries = await Promise.all(queries);

  return Object.fromEntries(resultEntries);
};

const convertToReconciliationStatus = (report, fieldConfigs, account) => {
  const reportData = [report];
  const statementIds = report.statement_data?.map((item) => item.id);
  const statementData = report.statement_data || [];
  const reportDataMap: Record<string, report_data | undefined> =
    reportData.reduce((acc, cur) => {
      acc[cur.id] = cur;
      acc[cur.str_id] = cur;
      return acc;
    }, {});

  const statementDataMap: Record<string, statement_data | undefined> =
    statementData.reduce((acc, cur) => {
      acc[cur.id] = cur;
      acc[cur.str_id] = cur;
      return acc;
    }, {});

  const processedData = {};
  const processedDataLogs = {};

  const key = report.id ?? report?.statement_data?.[0]?.id;
  if (!key) return [];

  processedData[key] = {
    ...(processedData[key] ?? {}),
    statement_ids: statementIds,
    statement_str_ids: statementIds.map((id) => statementDataMap[id].str_id),
    policy_id: report.policy_id,
    report_id: report.id,
    report_data: report,
    report_str_id: report.str_id,
    reconciliation_status:
      report.id && statementIds.length > 0 ? 'matched' : 'unmatched',
    statements: report.statement_data,
    str_id: report.str_id,
  };

  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  statementIds.forEach((statementId) => {
    processedDataLogs[report.id] = {
      ...processedDataLogs[report.id],
      [statementId]: {
        contacts: reportDataMap[report.id].contacts ?? [],
      },
    };
  });

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  Object.entries(processedData).forEach(([_key, row]: [any, any], i) => {
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    fieldConfigs.forEach((header) => {
      // Skip ones that we already have
      if (header.fieldId in row) return;

      if (['field', 'invisifield'].includes(header.type)) {
        let source = header.source;
        if (!['reports', 'statements'].includes(header.source)) {
          throw new Error(
            `Unsupported source ${header.source} for reconciliation data`
          );
        }

        // If unmatched, use its type as source (since no other option)
        if (row.reconciliation_status === 'unmatched') {
          source = row.report_id ? 'reports' : 'statements';
        } else if (row.reconciliation_status === 'matched' && !row.report_id) {
          source = 'statements';
        }
        const [dataSource1, dataSource2] =
          source === 'reports'
            ? [reportDataMap, statementDataMap]
            : [statementDataMap, reportDataMap];
        const [dataKey1, dataKey2] =
          source === 'reports'
            ? [row.report_id, row.statement_ids?.[0]]
            : [row.statement_ids?.[0], row.report_id];

        const colVal1 = dataSource1?.[dataKey1]?.[header.fieldId];
        const colVal2 = dataSource2?.[dataKey2]?.[header.fieldId];
        const colVal = colVal1 ?? colVal2 ?? null; // Use colVal2 if colVal1 is null or undefined

        const colKey = header.keyAs ?? header.fieldId;
        row[colKey] = colVal;

        // Check for conflicts
        if (!['id'].includes(header.fieldId)) {
          // Skip id col
          if (row.reconciliation_status === 'matched' && source === 'reports') {
            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            row.statement_ids.forEach((statementId) => {
              if (isNill(statementDataMap[statementId][header.fieldId])) return;
              let isDiff =
                colVal !== statementDataMap[statementId][header.fieldId];
              if (header.fieldId.endsWith('_amount')) {
                isDiff =
                  +statementDataMap[statementId][header.fieldId] !== +colVal;
              }
              if (isDiff) {
                row.log = [
                  ...(row.log ?? []),
                  `diff[${header.fieldId}]: report: "${colVal}" / statement(${
                    row.statementId
                  }): "${row[header.fieldId]}"`,
                ];
              }
            });
          } else if (
            row.reconciliation_status === 'matched' &&
            source === 'statements'
          ) {
            if (
              reportDataMap[row.report_id] &&
              !isNill(reportDataMap[row.report_id][header.fieldId])
            ) {
              let isDiff =
                reportDataMap[row.report_id][header.fieldId] !== colVal;
              if (header.fieldId.endsWith('_amount')) {
                isDiff =
                  +reportDataMap[row.report_id][header.fieldId] !== +colVal;
              }
              if (isDiff) {
                row.log = [
                  ...(row.log ?? []),
                  `diff[${header.fieldId}]: report: "${
                    reportDataMap[row.report_id][header.fieldId]
                  }" / statement(${row.statement_ids[0]}): "${colVal}"`,
                ];
              }
            }
            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            row.statement_ids.forEach((statementId) => {
              if (isNill(statementDataMap[statementId][header.fieldId])) return;
              let isDiff =
                statementDataMap[statementId][header.fieldId] !== colVal;
              if (header.fieldId.endsWith('_amount')) {
                isDiff =
                  +statementDataMap[statementId][header.fieldId] !== +colVal;
              }
              if (isDiff) {
                row.log = [
                  ...(row.log ?? []),
                  `diff[${header.fieldId}]: statement (${
                    row.statement_ids[0]
                  }): "${colVal}" / statement(${statementId}): "${
                    statementDataMap[statementId][header.fieldId]
                  }"`,
                ];
              }
            });
          }
        }
      } else if (header.type === 'aggregate') {
        if (
          (header.source === 'reports' && row.report_id) ||
          (header.source === 'statements' &&
            row.statement_ids &&
            row.statement_ids.length > 0)
        ) {
          const dataSource =
            header.source === 'reports' ? reportDataMap : statementDataMap;
          const dataSourceData =
            header.source === 'reports'
              ? dataSource[row.report_id]
              : row.statement_ids.map((id) => dataSource[id]);

          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          dataSourceData.forEach((datum) => {
            if (!row[header.keyAs]) {
              row[header.keyAs] = {
                [header.getKey(datum)]: {
                  [header.keyAs]:
                    header.method && typeof header.method === 'function'
                      ? datum[header.fieldId]
                      : numberOrDefault(datum[header.fieldId]),
                },
              };
            } else {
              const newVal = {
                [header.keyAs]: row[header.keyAs][header.getKey(datum)]
                  ? header.method && typeof header.method === 'function'
                    ? header.method(
                        row[header.keyAs][header.getKey(datum)][header.keyAs],
                        datum[header.fieldId]
                      )
                    : row[header.keyAs][header.getKey(datum)][header.keyAs] +
                      numberOrDefault(datum[header.fieldId])
                  : numberOrDefault(datum[header.fieldId]), // Does this ever get hit?
              };
              row[header.keyAs] = {
                ...row[header.keyAs],
                [header.getKey(datum)]: newVal,
              };
            }
          });
        }
      }
    });
  });

  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  Object.values(processedData).forEach((row: any) => {
    row.payment_date_first =
      row.payment_date_first?.payment_date_first?.payment_date_first;
    row.payment_date_last =
      row.payment_date_last?.payment_date_last?.payment_date_last;
  });

  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  fieldConfigs
    .filter((header) => header.type === 'computed')
    .forEach((header) => {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      Object.entries(processedData).forEach(([k, v]) => {
        processedData[k] = {
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          ...(v as any),
          [header.fieldId]: header.compute(v),
        };
      });
    });

  const reportIdsToUpdate: Record<
    string,
    {
      first_payment_date: Date | null;
      first_processed_date: Date | null;
    }
  > = {};
  const statementIdsToUpdate = {};
  const statementToClear = [];
  const statementCommissionToClear = [];

  const reconciliations = Object.entries(processedData).map(([k, v]) => {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const reportState = reportDataMap[(v as any).report_id]?.state; // 'active' or 'grouped'
    const result = {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      ...(v as any),
      normalized_id: k,
      account_id: account.account_id,
      uid: account.uid,
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      contacts: reportDataMap[(v as any).report_id]?.contacts ?? [],
      created_by: account.uid,
      created_proxied_by: account.ouid,
      state: reportState ?? 'active',
    };
    if (
      result.report_id &&
      result.statement_ids &&
      result.statement_ids.length > 0
    ) {
      // Each statement record should only be matched to one policy record
      // biome-ignore lint/complexity/noForEach: Initial?
      result.statement_ids.forEach((statementId) => {
        if (
          statementDataMap[statementId]?.report_data_id !==
          processedDataLogs[k][statementId]?.report_data_id
        ) {
          statementCommissionToClear.push(statementId);
        }
        if (
          !isEqual(
            statementDataMap[statementId]?.contacts,
            processedDataLogs[k][statementId]?.contacts
          ) ||
          statementDataMap[statementId]?.report_data_id !== result.report_id
        ) {
          const previousPolicy =
            reportDataMap[statementIdsToUpdate[statementId]?.report_data_id];
          const currentPolicy = reportDataMap[result.report_id];

          statementIdsToUpdate[statementId] = {
            contacts: processedDataLogs[k][statementId]?.contacts,
            report_data_id:
              previousPolicy?.effective_date &&
              currentPolicy?.effective_date &&
              dayjs(previousPolicy?.effective_date).isBefore(
                dayjs(currentPolicy?.effective_date)
              )
                ? result.report_id
                : statementIdsToUpdate[statementId]?.report_data_id ||
                  result.report_id,
          };
        }
      });

      // Calculate first payment date
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const first_payment_date = (result.statement_ids as any[])
        .map((statementId) => {
          const { payment_date = undefined } =
            statementDataMap[statementId] ?? {};
          return payment_date;
        })
        .filter((payment_date): payment_date is Date => !!payment_date)
        .reduce((acc, cur) => {
          if (acc === undefined) {
            return cur;
          }

          return cur < acc ? cur : acc;
        }, undefined);

      // Calculate first processed date
      const first_processed_date = result.statement_ids.reduce(
        (acc, statementId) => {
          const { processing_date: cur = undefined } =
            statementDataMap[statementId] ?? {};
          if (cur === undefined) {
            return acc;
          }
          if (acc === undefined) {
            return cur;
          }

          return cur < acc ? cur : acc;
        },
        undefined
      );

      reportIdsToUpdate[result.report_id] = {
        first_payment_date: first_payment_date ?? null,
        first_processed_date: first_processed_date ?? null,
      };
    } else if (!result.report_id) {
      if (result.statement_ids.length > 0) {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        result.statement_ids.forEach((statementId) => {
          if (
            statementDataMap[statementId]?.report_data_id !==
            processedData[k][statementId]?.report_data_id
          ) {
            statementToClear.push(statementId);
          }
        });
      }
    }
    delete result.id;
    // TODO: Convert report_id to string...eventually make int
    if (result.report_id) {
      result.report_data_id = +result.report_id;
      result.report_id = String(result.report_id);
    }
    return result;
  });
  return reconciliations;
};

export const checkReconciliationVersion = async (account_id: string) => {
  const accountConfigService =
    container.get<AccountConfigService>(AccountConfigService);
  const accountConfig = (await accountConfigService.getConfigByType(
    account_id,
    'reconciliation'
  )) as { version: string };
  return accountConfig?.version;
};
