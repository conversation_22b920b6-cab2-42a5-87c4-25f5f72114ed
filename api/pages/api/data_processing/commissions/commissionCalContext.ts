import BigNumber from 'bignumber.js';
import type * as commissions from 'common/dto/data_processing/commissions';
import type { CustomMethodCondition } from 'common/interface';
import type { comp_grid_rates } from '@prisma/client';

import { LRUCacheService } from '@/services/cache/lru';

export interface AgentPayoutRateOverride {
  [contactStrId: string]:
    | number
    | {
        config?: Record<string, { conditions: CustomMethodCondition[] }>;
      };
}
export interface AgentCommissionCalc {
  [statementId: number]: {
    [agentStrId: string]: BigNumber | number;
    total: BigNumber | number;
  };
}

export interface AgentCommissionCalcLogItem {
  agencyRates?: comp_grid_rates;
  agentRates?: comp_grid_rates;
  agentSplit?: BigNumber;
  agentUplines?: string[]; // Upline agent's name
  alerts?: string;
  calcBasis?: BigNumber | number;
  calcMethod?: string;
  calculatedAt?: number;
  commissionAmount?: BigNumber | number;
  commissionRate?: BigNumber | number;
  contactStrId?: string;
  criterionId?: number;
  criterionStr?: string;
  hierarchySplit?: BigNumber | number;
  multiplier?: BigNumber | number;
  calcMethodMultiplier?: BigNumber | number;
  notes?: string;
  payeeRates?: BigNumber[];
  payerCommissionRate?: BigNumber | number;
  payerRates?: BigNumber[];
  payoutRate?: BigNumber | number;
  policyCommissionsUsed?: BigNumber;
  profile_id?: number;
  profile_name?: string;
  profile_str_id?: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  rule?: any;
  scope?: string;
  splitPercentage?: BigNumber;
}
export interface AgentCommissionCalcLog {
  [statementId: number]: {
    [agentStrId: string]: AgentCommissionCalcLogItem[];
  };
}

interface Flags {
  [statementId: number]: {
    [ruleStrId: string]: string;
  };
}

interface Rule {
  label: string;
  value: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  condition: (statement: any) => boolean;
}

export class CommissionCalcContext {
  agentCommissionCalc: AgentCommissionCalc = {};
  agentCommissionCalcLog: AgentCommissionCalcLog = {};
  errors: string[] = [];
  failedTasks: number[] = [];
  flags: Flags = {};
  isWorkerJob: boolean;
  profileCache: LRUCacheService = new LRUCacheService({ max: 300 });
  ancestryProfilesMap: LRUCacheService = new LRUCacheService({ max: 300 });
  compGridCache: LRUCacheService = new LRUCacheService({ max: 300 });
  params: commissions.CommissionCalcDTO;
  regressionTestMode = false;
  regressionAccount: string;

  policyCommissionsSeenAmount: { [id: number]: number | BigNumber } = {};
  policyCommissionsSeenRecords: {
    [statementId: number]: boolean;
    [statementStrId: string]: boolean;
  } = {};
  policyCommissionsUsedAmount: { [id: number]: number | BigNumber } = {};
  policyCommissionsUsedRecords: { [key: string]: boolean } = {};

  constructor(params: commissions.CommissionCalcDTO) {
    this.isWorkerJob = !!params.master_str_id;
    this.params = params;
    this.regressionTestMode = !!params.regressionTestMode;
    this.regressionAccount = params.regressionAccount;
  }

  static init(params: commissions.CommissionCalcDTO) {
    return new CommissionCalcContext(params);
  }

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  updateFlags(statement: any, rule: Rule) {
    this.flags[statement.id] ??= {};
    this.flags[statement.id][rule.label] ??= rule.value;
  }

  addResult(params: {
    statementId: number;
    payeeStrId: string;
    amount: BigNumber | number;
    logs?: AgentCommissionCalcLogItem;
  }) {
    const { statementId, payeeStrId, amount, logs } = params;
    this.agentCommissionCalc[statementId] = {
      ...(this.agentCommissionCalc[statementId] ?? {}),
      [payeeStrId]: BigNumber(
        this.agentCommissionCalc[statementId]?.[payeeStrId] ?? 0
      )
        .plus(amount)
        .toNumber(),
      total: BigNumber(this.agentCommissionCalc[statementId]?.total ?? 0)
        .plus(amount)
        .toNumber(),
    };
    this.agentCommissionCalcLog[statementId] = {
      ...(this.agentCommissionCalcLog[statementId] ?? {}),
      [payeeStrId]: [
        ...(this.agentCommissionCalcLog[statementId]?.[payeeStrId] ?? []),
        // biome-ignore lint/complexity/useDateNow: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        { ...logs, calculatedAt: new Date().getTime() },
      ],
    };
  }
  // Later will be extracted to a util class, now keep it here for now
  getAgentSplitFromEffectiveSplit(
    effectiveSplit: { [contactStrId: string]: number },
    statementContactStrId: string
  ) {
    const splitValue = effectiveSplit?.[statementContactStrId];

    return (
      splitValue == null || Number.isNaN(+splitValue)
        ? BigNumber(100)
        : BigNumber(splitValue)
    ).div(100);
  }
}
