import type { Prisma } from '@prisma/client';
import type * as commissions from 'common/dto/data_processing/commissions';
import { CompensationTypes } from 'common/globalTypes';
import { injectable } from 'inversify';
import keyBy from 'lodash-es/keyBy';

import { prismaClient as prisma } from '@/lib/prisma';
import {
  COMMISSION_STATUS_EDITABLE,
  type StatementDataWithReport,
} from '@/pages/api/data_processing/commissions/commissions.constant';
import { DataStates } from '@/types';

@injectable()
export class CommissionRetrieval {
  fetchAccountSettings = async (account_id, regressionTestMode) => {
    return await prisma.accounts.findFirst({
      where: { str_id: account_id, state: 'active' },
      accountInject: !regressionTestMode,
    });
  };

  fetchCompanies = async (account_id, nameArray, regressionTestMode) => {
    return await prisma.companies.findMany({
      where: {
        account_id,
        state: DataStates.ACTIVE,
        OR: nameArray.map((name) => ({
          company_name: { equals: name, mode: 'insensitive' },
        })),
      },
      accountInject: !regressionTestMode,
    });
  };

  fetchCompanyProducts = async (account_id, companyIds, regressionTestMode) => {
    return await prisma.company_products.findMany({
      where: {
        account_id,
        company_id: companyIds?.length > 0 ? { in: companyIds } : undefined,
        state: DataStates.ACTIVE,
      },
      accountInject: !regressionTestMode,
    });
  };

  fetchProductOptions = async (account_id, productIds, regressionTestMode) => {
    return await prisma.company_product_options.findMany({
      where: {
        account_id,
        ...(productIds?.length > 0 && { product_id: { in: productIds } }),
        state: DataStates.ACTIVE,
      },
      accountInject: !regressionTestMode,
    });
  };

  fetchCompGridCriteria = async (
    account_id,
    companyIds,
    regressionTestMode
  ) => {
    return await prisma.comp_grid_criteria.findMany({
      where: {
        account_id,
        company_id: companyIds?.length > 0 ? { in: companyIds } : undefined,
        state: DataStates.ACTIVE,
      },
      accountInject: !regressionTestMode,
      include: {
        comp_grid_product: { where: { state: DataStates.ACTIVE } },
        comp_grid: {
          where: { state: DataStates.ACTIVE },
          select: { id: true },
        },
      },
    });
  };

  fetchCompGridProducts = async (
    account_id,
    compGridIds,
    regressionTestMode
  ) => {
    return await prisma.comp_grid_products.findMany({
      where: {
        account_id,
        comp_grid_id: compGridIds?.length > 0 ? { in: compGridIds } : undefined,
        state: DataStates.ACTIVE,
      },
      accountInject: !regressionTestMode,
      include: {
        company_products: { where: { state: DataStates.ACTIVE } },
      },
    });
  };

  getAllData = async (params) => {
    const {
      account_id,
      contactIds,
      documentIds,
      startDate,
      endDate,
      payingEntity,
      policyId,
      statementIds,
      id,
      ids,
      regressionTestMode,
      useGroupedCommissions,
      regressionAccount,
    } = params;

    const accountSettings = await this.fetchAccountSettings(
      account_id,
      regressionTestMode
    );
    const useCompGrids = accountSettings?.comp_grids_enabled;

    const statementData = await this.getTaskStatements(
      {
        id,
        ids,
        payingEntity,
        startDate,
        endDate,
        statementIds,
        contactIds,
        documentIds,
        policyId,
        useGroupedCommissions,
        regressionTestMode,
        regressionAccount,
      },
      account_id
    );

    if (statementData.length === 0) {
      return {
        companies: [],
        companyProducts: [],
        companyProductOptions: [],
        statementData: [],
        useCompGrids,
        compGridCriteria: [],
        compGridProducts: [],
      };
    }

    const companyNameSet = new Set(
      statementData
        .flatMap((s) => [s.carrier_name, s.writing_carrier_name])
        .filter(Boolean)
        .map((n) => n.toLowerCase())
    );
    const companyNameArray = Array.from(companyNameSet);

    const companies = await this.fetchCompanies(
      account_id,
      companyNameArray,
      regressionTestMode
    );
    const companyIds = companies.map((c) => c.id);
    const companyProducts = await this.fetchCompanyProducts(
      account_id,
      companyIds,
      regressionTestMode
    );
    const companyProductIds = companyProducts.map((p) => p.id);
    const companyProductOptions = await this.fetchProductOptions(
      account_id,
      companyProductIds,
      regressionTestMode
    );
    const compGridCriteria = await this.fetchCompGridCriteria(
      account_id,
      companyIds,
      regressionTestMode
    );
    const compGridIds = [
      ...new Set(compGridCriteria.map((p) => p.comp_grid?.id).filter(Boolean)),
    ];
    const compGridProducts = await this.fetchCompGridProducts(
      account_id,
      compGridIds,
      regressionTestMode
    );

    const companiesMap = keyBy(companies, 'id');
    const companyProductsMap = keyBy(companyProducts, 'id');
    const companyProductOptionsMap = keyBy(companyProductOptions, 'id');
    const compGridCriteriaMap = keyBy(compGridCriteria, 'id');
    const compGridProductsMap = keyBy(compGridProducts, 'id');
    const statementMap = keyBy(statementData, 'id');
    const lookupData = {
      companies: companiesMap,
      companyProducts: companyProductsMap,
      companyProductOptions: companyProductOptionsMap,
      compGridCriteria: compGridCriteriaMap,
      compGridProducts: compGridProductsMap,
      statements: statementMap,
    };

    return {
      lookupData,
      statementData,
      useCompGrids,
    };
  };

  getTaskStatements = async (
    params: Omit<
      commissions.CommissionCalcDTO,
      'isSync' | 'onlyGetProfilesRates'
    >,
    accountId: string
  ) => {
    const {
      id,
      ids,
      payingEntity,
      startDate,
      endDate,
      statementIds,
      contactIds,
      documentIds,
      policyId,
      useGroupedCommissions,
      regressionTestMode,
    } = params;

    let idCondition: number | Prisma.IntFilter<'statement_data'> = +id
      ? +id
      : undefined;
    let idsCondition: { in: number[] } | undefined;
    if (ids && ids.length > 0) {
      idsCondition = { in: Array.isArray(ids) ? ids : [ids] };
    }
    if (statementIds?.length) {
      idCondition = { in: statementIds };
    }
    const pageSize = 10000;
    let page = 0;

    const whereCondition: Prisma.statement_dataWhereInput = {
      id: idCondition ?? idsCondition,
      account_id: accountId,
      contacts:
        contactIds?.length > 0 ? { hasSome: contactIds } : { isEmpty: false },
      carrier_name: payingEntity ? { in: payingEntity.split(',') } : undefined,
      document_id:
        documentIds?.length > 0
          ? { in: Array.isArray(documentIds) ? documentIds : [documentIds] }
          : undefined,
      AND: [
        {
          OR: [
            { compensation_type: null },
            { compensation_type: { not: CompensationTypes.ADVANCED } },
          ],
        },
      ],
      processing_date: {
        gte: startDate ?? undefined,
        lte: endDate ?? undefined,
      },
      policy_id: policyId ? policyId : undefined,
      state: { in: [DataStates.ACTIVE, DataStates.GROUPED] },
    };
    if (regressionTestMode) {
      delete whereCondition.OR;
      delete whereCondition.account_id;
      whereCondition.account_id = params.regressionAccount;
    } else {
      (whereCondition.AND as Prisma.statement_dataWhereInput[]).push({
        OR: COMMISSION_STATUS_EDITABLE,
      });
    }
    let statementData: StatementDataWithReport[] = [];
    let hasNext = true;
    while (hasNext) {
      const title = `fetching statement data page ${page}, cursor id: ${(whereCondition.id as { gt: number })?.gt}`;
      console.time(title);
      let items = (await prisma.statement_data.findMany({
        where: whereCondition,
        // biome-ignore lint/complexity/noUselessTernary: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        accountInject: regressionTestMode ? false : true,
        include: {
          report: {
            where: { state: { in: ['active', 'grouped'] } },
            include: {
              statement_data: {
                where: {
                  state: { in: [DataStates.ACTIVE, DataStates.GROUPED] },
                  OR: COMMISSION_STATUS_EDITABLE,
                },
                select: {
                  id: true,
                  str_id: true,
                  commission_amount: true,
                  new_commission_rate: true,
                  contacts: true,
                },
              },
            },
          },
          children_data: {
            where: { state: { in: ['active', 'grouped'] } },
            include: {
              report: {
                where: { state: { in: ['active', 'grouped'] } },
              },
            },
          },
        },
        orderBy: { id: 'asc' },
        take: pageSize,
        skip: pageSize * page,
      })) as StatementDataWithReport[];
      page += 1;
      console.timeEnd(title);
      if (items) {
        if (useGroupedCommissions) {
          items = items.map((item) => ({
            ...item,
            hasGroupedCommissions: item.children_data?.length > 0 || undefined,
          }));
        }
        statementData = statementData.concat(items);
      }
      if (items.length < pageSize) {
        hasNext = false;
        break;
      }
    }
    return statementData;
  };
}
