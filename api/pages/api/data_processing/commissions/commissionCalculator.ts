import { injectable, inject } from 'inversify';
import type { CustomMethodConfig } from 'common/interface';
import { numberOrDefault } from 'common/helpers';
import type { contacts } from '@prisma/client';
import BigNumber from 'bignumber.js';

import { CommissionUtils } from './commissionUtils';
import { DataStates, ReferralTypes } from '@/types';
import type { CommissionCalcContext } from './commissionCalContext';

@injectable()
export class CommissionCalculator {
  @inject(CommissionUtils)
  private readonly commissionUtils: CommissionUtils;

  policyScopeCalc = async ({
    accountId,
    agentCommissionProfile,
    calcBasis,
    calcMethod,
    calculationParallelLevel,
    commissionAmountBasis,
    contact,
    payoutRate,
    policyCommissionsSeenAmount,
    policyCommissionsSeenRecords,
    policyCommissionsUsedAmount,
    policyCommissionsUsedRecords,
    rule,
    statement,
  }) => {
    if (calculationParallelLevel !== 1) {
      throw new Error('PolicyScope parallel calculation is not supported yet');
    }
    if (!statement.report?.id) {
      return {
        commissionAmount: BigNumber(0),
        commissionRate: BigNumber(0),
        policyCommissionsUsed: BigNumber(0),
      };
    }
    const reportDataId = statement.report.id;
    const usedSeenCommissions =
      await this.commissionUtils.getUsedSeenPolicyCommissions(
        accountId,
        contact.str_id,
        statement
      );

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    Object.entries(usedSeenCommissions).forEach(([k, v]) => {
      if (!policyCommissionsUsedRecords[k]) {
        policyCommissionsUsedAmount[reportDataId] =
          +(policyCommissionsUsedAmount[reportDataId] ?? 0) + +v.used;
        policyCommissionsUsedRecords[k] = true;
      }
      if (!policyCommissionsSeenRecords[k]) {
        policyCommissionsSeenAmount[reportDataId] =
          +(policyCommissionsSeenAmount[reportDataId] ?? 0) + +v.seen;
        policyCommissionsSeenRecords[k] = true;
      }
    });

    const commissionAmountSoFar = BigNumber(
      policyCommissionsSeenAmount[reportDataId] ?? 0
    ).plus(
      policyCommissionsSeenRecords[statement.id] ? 0 : commissionAmountBasis
    );
    const commissionRateEffective = commissionAmountSoFar.div(
      statement.report?.commissionable_premium_amount
    );
    let commissionRate: BigNumber;
    switch (calcMethod) {
      case 'payoutRate':
        commissionRate = BigNumber(payoutRate);
        break;
      case 'keepRate':
        commissionRate = commissionRateEffective.minus(
          BigNumber(rule.keep_rate).div(100)
        );
        // TODO: Plus rate not currently supported for policy scope
        // (+rule.plus_rate * commissionRateEffective) / 100;
        break;
      default:
        commissionRate = BigNumber.max(
          commissionRateEffective.minus(payoutRate),
          BigNumber(0)
        );
    }
    const commissionAmountPrior = BigNumber(
      policyCommissionsUsedAmount[reportDataId] ?? 0
    );
    let commissionAmountCalced = commissionRate
      .times(calcBasis ?? 0)
      .minus(commissionAmountPrior)
      .times(
        BigNumber(
          numberOrDefault(
            agentCommissionProfile?.agentProfileConfig?.multiplier,
            100
          )
        ).div(100)
      );

    if (rule?.limit_to_received) {
      commissionAmountCalced = BigNumber.min(
        commissionAmountSoFar.minus(commissionAmountPrior),
        commissionAmountCalced
      );
    }

    // Sum of commission payout that's been calculated for this policy this run.
    // We have the amount of commission that's been paid for this policy, and this
    // keeps track of how much of it has been "used up"
    policyCommissionsUsedAmount[reportDataId] = BigNumber(
      policyCommissionsUsedAmount[reportDataId] ?? 0
    ).plus(
      this.commissionUtils.isIMO(contact) && commissionAmountCalced.isNegative()
        ? 0
        : commissionAmountCalced
    );
    policyCommissionsUsedRecords[statement.id] = true;
    policyCommissionsUsedRecords[statement.str_id] = true;

    // Sum of the commission line items that have been processed for this policy this run.
    // Since IMOs are charged (i.e. adding to received commissions, add to commissions seen)
    policyCommissionsSeenAmount[reportDataId] = BigNumber(
      policyCommissionsSeenAmount[reportDataId] ?? 0
    )
      .plus(commissionAmountBasis)
      .plus(
        this.commissionUtils.isIMO(contact) &&
          commissionAmountCalced.isNegative()
          ? commissionAmountCalced.negated()
          : 0
      );
    policyCommissionsSeenRecords[statement.id] = true;
    policyCommissionsSeenRecords[statement.str_id] = true;

    return {
      commissionAmount: commissionAmountCalced,
      commissionRate,
      policyCommissionsUsed: commissionAmountPrior,
      actualCalcBasis: calcBasis ?? 0,
    };
  };

  calcFixedOverride = ({
    config,
    statement = null,
    contactsMap = null,
    runningTotal = BigNumber(0),
    hierarchySplitPercentage = BigNumber(100),
  }) => {
    const premium_amount = BigNumber(statement?.premium_amount ?? 0).times(
      BigNumber(hierarchySplitPercentage).div(100)
    );
    const fixedOverride = BigNumber(config.rate);
    const commissionAmount = this.commissionUtils.alignSign(
      fixedOverride.div(100).times(premium_amount),
      this.commissionUtils.getValueSign(+statement?.commission_amount),
      this.commissionUtils.getValueSign(+fixedOverride)
    );
    /**
     * TODO: Confirm with @hungphucchu - Chu Phuc Hung, If we can remove this reference here
     *      disabling lint warning for now
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    runningTotal = runningTotal.plus(commissionAmount);
    const downLineId = config.payee_id;
    if (!downLineId) return;
    const payeeStrId = contactsMap[downLineId].str_id;

    return {
      commissionAmount,
      payeeStrId,
      payoutRate: fixedOverride,
    };
  };

  percentageToDecimal = (percentage: number | BigNumber) => {
    return BigNumber(percentage).div(100);
  };

  calcOverrideBonus = ({
    config,
    runningTotal = BigNumber(0),
    statement = null,
    contactsMap = null,
    hierarchySplitPercentage = BigNumber(100),
  }) => {
    const bonusRate = BigNumber(config.rate);
    const statementSplitPercentage = BigNumber(
      statement?.split_percentage ?? 100
    );
    const commissionAmount = this.commissionUtils.alignSign(
      bonusRate
        .div(100)
        .times(statement.premium_amount)
        .times(this.percentageToDecimal(statementSplitPercentage))
        .times(this.percentageToDecimal(hierarchySplitPercentage)),
      this.commissionUtils.getValueSign(+statement?.commission_amount),
      this.commissionUtils.getValueSign(+bonusRate)
    );
    /**
     * TODO: Confirm with @hungphucchu - Chu Phuc Hung, If we can remove this reference here
     *      disabling lint warning for now
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    runningTotal = runningTotal.plus(commissionAmount);
    const downLineId = config.payee_id;
    if (!downLineId) return;
    const payeeStrId = contactsMap[downLineId].str_id;

    return {
      commissionAmount,
      payeeStrId,
      payoutRate: bonusRate,
      splitPercentage: BigNumber(statement.split_percentage ?? 100),
    };
  };

  getFinalCalcBasisWithSign = (params: {
    defaultBasis: BigNumber;
    runningTotal: BigNumber;
    calculationBasis: string;
    isSalesRep?: boolean;
    agentSplit: BigNumber | null;
    statement: {
      premium_amount: number;
      commission_amount: number;
    };
  }) => {
    const {
      defaultBasis,
      runningTotal,
      calculationBasis,
      statement,
      agentSplit,
      isSalesRep,
    } = params;
    let sign = this.commissionUtils.getValueSign(
      Number(statement?.commission_amount) ?? 0
    );
    const split = isSalesRep ? BigNumber(1) : (agentSplit ?? BigNumber(1)); // For sales vp, the agentSplit is 0

    if (calculationBasis === 'commissions_remaining') {
      const remain = BigNumber(statement.commission_amount ?? 0).minus(
        runningTotal ?? 0
      );
      sign = this.commissionUtils.getValueSign(remain.toNumber());
      return {
        basis: remain,
        finalCalcBasis: remain.times(split),
        sign,
      };
    } else if (calculationBasis === 'premium') {
      const basis = BigNumber(statement.premium_amount ?? 0);
      return { basis, finalCalcBasis: basis.times(split), sign };
    }
    return {
      basis: defaultBasis,
      finalCalcBasis: defaultBasis.times(split),
      sign,
    };
  };
  sequenceRunMethods = async ({
    lookupData,
    agentApplicableProfile,
    statement = null,
    contactsMap = null,
    runningTotal = BigNumber(0),
    ctx,
    req,
    hierarchySplitPercentage = BigNumber(100),
    useCompGrids = false,
    cache,
    agentUplines = [],
  }) => {
    // Backward capability, just in case config is not exist
    if (
      ['fixed', 'bonus'].includes(
        agentApplicableProfile?.agentProfileConfig?.method
      ) &&
      !agentApplicableProfile?.agentProfileConfig?.config
    ) {
      const profile = agentApplicableProfile?.agentProfileConfig;
      agentApplicableProfile.agentProfileConfig.config = [
        {
          method: agentApplicableProfile?.agentProfileConfig?.method,
          rate: profile.rate,
          payee_id: profile.payee_id,
          multiplier: profile.multiplier,
        },
      ];
    }

    let configs: CustomMethodConfig[] = (
      agentApplicableProfile?.agentProfileConfig.config || []
    ).filter((r) => ['fixed', 'bonus'].includes(r.method));

    for (const config of configs) {
      if (
        !(await this.commissionUtils.isActivateCustomMethod(
          config,
          lookupData,
          statement,
          req,
          agentApplicableProfile.agentProfileConfig,
          useCompGrids,
          cache
        ))
      ) {
        configs = configs.filter((c) => c !== config);
      }
    }

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    configs.forEach((config) => {
      let result: {
        commissionAmount: BigNumber;
        payeeStrId: string;
        payoutRate: BigNumber;
        splitPercentage?: BigNumber;
      };
      if (config.method === 'fixed') {
        result = this.calcFixedOverride({
          config,
          statement,
          contactsMap,
          runningTotal,
          hierarchySplitPercentage,
        });
        runningTotal = runningTotal.plus(result?.commissionAmount ?? 0);
        // biome-ignore lint/suspicious/noDoubleEquals: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } else if (config.method == 'bonus') {
        result = this.calcOverrideBonus({
          config,
          statement,
          contactsMap,
          runningTotal,
          hierarchySplitPercentage,
        });
        runningTotal = runningTotal.plus(result?.commissionAmount ?? 0);
      }
      if (result) {
        (ctx as CommissionCalcContext).addResult({
          statementId: statement.id,
          payeeStrId: result.payeeStrId,
          amount: result.commissionAmount,
          logs: {
            profile_name:
              agentApplicableProfile.agentProfileConfig
                .agent_commission_schedule_profile.name,
            profile_id:
              agentApplicableProfile.agentProfileConfig
                .agent_commission_schedule_profile_id,
            profile_str_id:
              agentApplicableProfile.agentProfileConfig
                .agent_commission_schedule_profile.str_id,
            rule: Object.fromEntries(
              // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              Object.entries(agentApplicableProfile.rule).filter(([k, v]) => v)
            ),
            splitPercentage: result.splitPercentage,
            agentSplit: BigNumber(statement.split_percentage).div(100),
            payoutRate: result.payoutRate,
            commissionAmount: result?.commissionAmount ?? 0,
            agentUplines,
            calcMethod: config.method,
            // biome-ignore lint/complexity/useDateNow: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            calculatedAt: new Date().getTime(),
          },
        });
      }
    });
    return { runningTotal };
  };

  referralCalc = ({
    agentCommissionProfile,
    calculationBasisAmountSplitted,
    statement,
    statementContactStrId,
    contactsMap,
    isAnnuity,
    sRate,
    a3Rate,
    effectiveDate,
  }) => {
    const referrers = (
      contactsMap[statementContactStrId]?.contact_referrals ?? []
    ).filter((referral) => {
      return (
        (!referral.start_date || referral.start_date <= effectiveDate) &&
        (!referral.end_date || referral.end_date >= effectiveDate)
      );
    });
    const contactLevel =
      contactsMap[statementContactStrId]?.contact_level?.[0]?.level_label;
    const results = [];
    if (
      statement.compensation_type !== 'FYC' ||
      !effectiveDate ||
      referrers.length === 0
    )
      return results;
    if (referrers) {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      referrers.forEach((referrer) => {
        // let commissionRate = REFERRAL_RATES?.[referrer.type]?.[contactLevel] ?? 0;
        let commissionRate = +referrer.config?.rate
          ? BigNumber(referrer.config?.rate).div(100)
          : null;
        let referralRateLog = '';

        // Handle Carematters referrals differently
        if (this.commissionUtils.isCareMatters(statement)) {
          commissionRate = BigNumber(referrer.config?.rate || 0)
            .div(100)
            .times(BigNumber(sRate).div(100));
          referralRateLog = `Carematters referral: ${commissionRate.times(100)}% (${sRate}% S rate * ${referrer.config?.rate}% referral)`;
        } else if (isAnnuity) {
          const rateToUse = referrer.config?.useLifeRatesForAnnuity
            ? BigNumber(referrer.config?.rate)
            : BigNumber(referrer.config?.annuity_rate);
          commissionRate = BigNumber(
            numberOrDefault(BigNumber(rateToUse).div(100).toNumber(), 0)
          );
          if (referrer.payer_contact_id || referrer.config?.deductFromUpline) {
            commissionRate = BigNumber(commissionRate)
              .div(0.05)
              .times(BigNumber(a3Rate).minus(BigNumber(sRate)).div(100));
            referralRateLog = `Commission interval: ${commissionRate.times(100)}%`;
          } else {
            commissionRate = BigNumber(commissionRate).times(
              BigNumber(sRate).div(100)
            );
            referralRateLog = `S rate: ${sRate}%`;
          }
        }

        // CommissionRate is an instance of BigNumber, which !commisionRate will be evaluated to false
        if (
          [undefined, null].includes(commissionRate) ||
          commissionRate?.isNaN()
        ) {
          if (
            referrer.type === ReferralTypes.REGION &&
            referrer.referrer_contact_id === 1789 // Tian-Chiang Wang
          ) {
            commissionRate = BigNumber(0.005);
          } else if (
            referrer.type === ReferralTypes.OTHER &&
            !Number.isNaN(+referrer.config?.rate)
          ) {
            commissionRate = BigNumber(referrer.config?.rate).div(100);
          }
        }
        const uplineId =
          contactsMap[statementContactStrId]?.parent_relationships?.[0]
            ?.parent_id;
        const uplineStrId = contactsMap[uplineId]?.str_id;
        const referrerStrId = contactsMap[referrer.referrer_contact_id]?.str_id;
        const referralCommissionAmount = this.commissionUtils.alignSign(
          commissionRate
            .times(calculationBasisAmountSplitted)
            .times(
              BigNumber(
                numberOrDefault(
                  agentCommissionProfile?.agentProfileConfig?.multiplier,
                  100
                )
              ).div(100)
            ),
          this.commissionUtils.getValueSign(+statement?.commission_amount)
        );
        if (referrerStrId) {
          results.push({
            key: statement.id,
            subKey: referrerStrId,
            amount: referralCommissionAmount,
            logs: {
              agentSplit: BigNumber(statement.split_percentage ?? 0).div(100),
              calcMethod: 'referral',
              commissionAmount: BigNumber(referralCommissionAmount),
              commissionRate: commissionRate,
              contactStrId: statementContactStrId,
              payoutRate: commissionRate.times(100),
              profile_name: `Referral (${referrer.type} - ${contactLevel})`,
              product_type: isAnnuity ? 'Annuity' : 'Life',
              referral_rates: referralRateLog,
            },
          });
        }

        // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        let payerStrId;
        if (
          [
            ReferralTypes.AGENT,
            ReferralTypes.REGION,
            ReferralTypes.OTHER,
          ].includes(referrer?.type)
        ) {
          if (referrer.config?.deductFromUpline) payerStrId = uplineStrId;
          else if (referrer.payer_contact_id)
            payerStrId = contactsMap[referrer.payer_contact_id]?.str_id;
        } else if (referrer.type === ReferralTypes.RECRUITING) {
          payerStrId = contactsMap[referrer.payer_contact_id]?.str_id;
        } else if (referrer.config?.deductFromUpline) {
          payerStrId = uplineStrId;
        } else if (referrer.payer_contact_id) {
          payerStrId = contactsMap[referrer.payer_contact_id]?.str_id;
        }
        if (payerStrId) {
          results.push({
            key: statement.id,
            subKey: payerStrId,
            amount: BigNumber(-referralCommissionAmount),
            logs: {
              agentSplit: BigNumber(statement?.split_percentage).div(100),
              calcMethod: 'referral',
              commissionAmount: BigNumber(-referralCommissionAmount),
              commissionRate: -commissionRate,
              contactStrId: statementContactStrId,
              payoutRate: -commissionRate.times(100),
              profile_name: `Referral (${referrer.type} - ${contactLevel})`,
            },
          });
        }
      });
    }
    return results;
  };

  getCalcBasis = (params: {
    defaultBasis: BigNumber;
    statement: {
      premium_type: string;
      split_percentage: number;
      premium_amount: number;
      report?: {
        commissionable_premium_amount: number;
        premium_amount: number;
      };
    };
    calculation_basis: string;
    agentSplit: BigNumber;
  }) => {
    const { defaultBasis, statement, calculation_basis, agentSplit } = params;
    let calcBasis = defaultBasis;
    switch (calculation_basis) {
      case 'target_premium':
        calcBasis = BigNumber(
          +statement.report?.commissionable_premium_amount || 0
        );
        break;
      case 'annual_premium':
        calcBasis = BigNumber(+statement.report?.premium_amount || 0);
        break;
      case 'normalized_premium':
        if (statement.premium_type?.toLowerCase() === 'policy') {
          calcBasis = BigNumber(+statement.premium_amount);
        } else if (statement.premium_type?.toLowerCase() === 'split') {
          if (+statement.split_percentage !== 0) {
            calcBasis = BigNumber(+statement.premium_amount).div(
              BigNumber(+statement.split_percentage).div(100)
            );
          } else if (!agentSplit.eq(0)) {
            calcBasis = BigNumber(+statement.premium_amount).div(agentSplit);
          } else {
            // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            console.warn(
              'premium_type is split but split_percentage and policy.agent split are both 0'
            );
            calcBasis = BigNumber(+statement.premium_amount);
          }
        } else {
          throw new Error('Unsupported premium type');
        }
        break;
      case 'premium':
        if (
          statement.premium_type?.toLowerCase() === 'policy' ||
          statement.premium_type?.toLowerCase() === 'split'
        ) {
          calcBasis = BigNumber(+statement.premium_amount);
        } else {
          throw new Error('Unsupported premium type');
        }
        break;
      default:
        break;
    }
    return calcBasis;
  };

  payToActiveAgentInHierarchy(
    agentStrId: string,
    profiles: { agentId: number; agentStrId: string; state: string }[],
    contactsMap: { [strIdOrId: string]: contacts }
  ): string | undefined {
    const agent = contactsMap[agentStrId];
    if (agent?.status !== DataStates.ARCHIVED) {
      return agentStrId;
    }
    // Start from the agent of the agentStrId and find the first active upline
    const profileIndex = profiles.findIndex((p) => p.agentId === agent.id);
    const uplines = profiles.slice(profileIndex);
    return uplines.find(
      // biome-ignore lint/suspicious/noDoubleEquals: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (p) => contactsMap[p.agentStrId]?.status != DataStates.ARCHIVED
    )?.agentStrId;
  }
}
