import BigNumber from 'bignumber.js';
import { AgentCommissionsStatuses } from 'common/globalTypes';
import { Prisma } from '@prisma/client';

import { CommissionStatuses } from '@/types';

export const COMMISSION_STATUS_EDITABLE = [
  { agent_commissions_status: { equals: Prisma.DbNull } },
  {
    AND: [
      { agent_commissions_status: { not: CommissionStatuses.APPROVED } },
      { agent_commissions_status: { not: CommissionStatuses.MANUAL } },
      { agent_commissions_status: { not: CommissionStatuses.PAID } },
      { agent_commissions_status: { not: AgentCommissionsStatuses.REVIEWED } },
      { agent_commissions_status: { not: AgentCommissionsStatuses.OFFSET } },
      {
        agent_commissions_status: { not: AgentCommissionsStatuses.NO_PAYMENT },
      },
    ],
  },
];

export type StatementDataWithReport = Prisma.statement_dataGetPayload<{
  include: {
    report: {
      include: {
        commissionable_premium_amount: true;
        contacts_split: true;
        statement_data: true;
      };
    };
    children_data: { include: { report: true } };
  };
}>;

export const RULE_FLAGS = {
  effective_date: {
    label: 'missing_effective_date',
    value: 'Missing effective date',
    condition: (statement) =>
      !(statement.effective_date ?? statement.report?.effective_date),
  },
  commission_amount: {
    label: 'invalid_commission',
    value: 'Premium * Commission rate ≠ Commission amount',
    condition: (statement) =>
      BigNumber(statement.premium_amount)
        .multipliedBy(statement.commission_rate)
        .comparedTo(BigNumber(statement.commission_amount).abs()) !== 0,
  },
};

export const REFERRAL_RATES = {
  agent: {
    'Agency Top': 0.02,
    'Agency 0': 0.02,
    'Agency 1': 0.02,
    'Agency 2': 0.02,
    'Agency 3': 0.03,
    'Level S': 0.05,
    'Level A': 0.05,
    'Level B': 0.05,
  },
  region: {
    'Agency Top': 0.04,
    'Agency 0': 0.04,
    'Agency 1': 0.04,
    'Agency 2': 0.04,
    'Agency 3': 0.05,
    'Level S': 0.07,
    'Level A': 0.07,
    'Level B': 0.07,
  },
  recruiting: {
    'Agency Top': 0.02,
    'Agency 0': 0.02,
    'Agency 1': 0.02,
    'Agency 2': 0.02,
    'Agency 3': 0.03,
    'Level S': 0.05,
    'Level A': 0.05,
    'Level B': 0.05,
  },
  other: {
    'Agency Top': 0,
    'Agency 0': 0,
    'Agency 1': 0,
    'Agency 2': 0,
    'Agency 3': 0,
    'Level S': 0,
    'Level A': 0,
    'Level B': 0,
  },
};
