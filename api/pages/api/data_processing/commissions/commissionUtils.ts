import BigNumber from 'bignumber.js';
import { numberOrDefault } from 'common/helpers';
import {
  type CustomMethodConfig,
  type CustomMethodCondition,
  CustomMethodConditionOperator,
  CustomMethodConditionType,
} from 'common/interface';
import Formatter from 'common/Formatter';
import { inject, injectable } from 'inversify';
import { CompensationTypes, TransactionType } from 'common/globalTypes';

import { prismaClient as prisma, prismaClient } from '@/lib/prisma';
import {
  CommissionStatuses,
  DataStates,
  type ExtNextApiRequest,
} from '@/types';
import type { LRUCacheService } from '@/services/cache/lru';
import {
  getContactAncestorsWithCommissionProfiles,
  getApplicableCompProfiles,
} from '@/lib/commissions';
import type {
  CommissionCalcContext,
  AgentPayoutRateOverride,
} from './commissionCalContext';
import type { StatementDataWithReport } from './commissions.constant';
import { ContactService } from '@/services/contact';

@injectable()
export class CommissionUtils {
  @inject(ContactService)
  private contactService: ContactService;
  checkCompensationType = async (condition, statement) => {
    switch (condition.operator) {
      case CustomMethodConditionOperator.EQUAL:
        return statement.compensation_type === condition.value;
      case CustomMethodConditionOperator.NOT_EQUAL:
        return statement.compensation_type !== condition.value;
      case CustomMethodConditionOperator.CONTAINED_IN:
        try {
          const value = JSON.parse(condition.value);
          if (typeof value === 'string') {
            return value.split(',').includes(statement.compensation_type);
          }
          if (Array.isArray(value)) {
            return value.includes(statement.compensation_type);
          }
          return false;
        } catch (e) {
          // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          console.error('Error parsing condition value', e);
          return false;
        }
      default:
        return true;
    }
  };

  updateReferralTags = async (
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    ctx: any,
    req: ExtNextApiRequest,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    prisma: any
  ) => {
    // Find statement ids with calcMethod 'referral' in agent_commissions_log
    const referralStatementIds = Object.entries(ctx.agentCommissionCalcLog)
      .filter(([_statementId, agentsWithLogs]) =>
        Object.entries(agentsWithLogs).some(([_agentStrId, agentLogs]) =>
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          (agentLogs as any).some((log) => log?.calcMethod === 'referral')
        )
      )
      .map(([statementId]) => +statementId);

    // Extract agent string IDs that have referral calc method
    const referralAgentStrIds = [];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    Object.entries(ctx.agentCommissionCalcLog).forEach(
      ([_statementId, agentsWithLogs]) => {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        Object.entries(agentsWithLogs).forEach(([agentStrId, agentLogs]) => {
          if (
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            (agentLogs as any).some((log) => log?.calcMethod === 'referral')
          ) {
            referralAgentStrIds.push(agentStrId);
          }
        });
      }
    );

    if (referralStatementIds?.length > 0) {
      // Update statement_data tags for referral statements
      await prisma.statement_data.updateMany({
        where: {
          account_id: req.account_id,
          id: { in: referralStatementIds },
          NOT: {
            tags: {
              has: 'referral',
            },
          },
        },
        data: {
          tags: {
            push: 'referral',
          },
        },
      });

      // Get contact numeric IDs from string IDs for referral agents
      const referralContactIds =
        await this.contactService.getContactsByStrIdList(
          referralAgentStrIds,
          req.account_id
        );

      const referralContactNumericIds = referralContactIds.map(
        (contact) => contact.id
      );

      // Update accounting_transaction_details tags for referral statements with specific contact IDs
      if (referralContactNumericIds.length > 0) {
        await prisma.accounting_transaction_details.updateMany({
          where: {
            account_id: req.account_id,
            statement_id: { in: referralStatementIds },
            contact_id: { in: referralContactNumericIds },
            type: TransactionType.PAYABLE,
            state: DataStates.ACTIVE,
            NOT: {
              tags: {
                has: 'referral',
              },
            },
          },
          data: {
            tags: {
              push: 'referral',
            },
          },
        });
      }
    }
  };

  checkPayeeRateDifference = async (
    payeeId,
    condition: CustomMethodCondition,
    lookupData,
    statement,
    req: { account_id: string },
    compProfileConfig,
    useCompGrids: boolean,
    cache?: LRUCacheService
  ) => {
    const contact = await prismaClient.contacts.findFirst({
      where: {
        id: payeeId,
      },
      accountInject: false,
    });
    const effectiveDate =
      statement.effective_date ?? statement.report?.effective_date;
    const contactStrId = contact?.str_id;
    const key = `${contactStrId}-${effectiveDate}`;
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let contactAncestors;
    if (cache.get(key)) {
      contactAncestors = cache.get(key);
    } else {
      contactAncestors = await getContactAncestorsWithCommissionProfiles(
        contactStrId,
        effectiveDate,
        req,
        cache
      );
      cache.set(key, contactAncestors);
    }
    const payeeProfile = contactAncestors.find(
      (p) => p.agentStrId === contactStrId
    );
    const applicableProfiles = await getApplicableCompProfiles(
      { compProfiles: payeeProfile?.commissionProfiles },
      statement,
      lookupData,
      effectiveDate,
      useCompGrids
    );

    const operatorMap = {
      [CustomMethodConditionOperator.GREATER_THAN_OR_EQUAL]: 'gte',
      [CustomMethodConditionOperator.GREATER_THAN]: 'gt',
      [CustomMethodConditionOperator.LESS_THAN]: 'lt',
      [CustomMethodConditionOperator.LESS_THAN_OR_EQUAL]: 'lte',
      [CustomMethodConditionOperator.EQUAL]: 'eq',
      [CustomMethodConditionOperator.NOT_EQUAL]: 'neq',
    };
    const currentCriteriaId = compProfileConfig.matchedCriteriaId;
    const currentPayeeRates =
      compProfileConfig.agentProfileConfig?.agent_commission_schedule_profile?.payee_comp_grid_level?.comp_grid_rates.find(
        (r) => r.comp_grid_criterion_id === currentCriteriaId
      );

    const criteriaId = applicableProfiles.matchedCriteriaId;
    const payeeRates =
      applicableProfiles?.agentProfileConfig?.agent_commission_schedule_profile?.payee_comp_grid_level?.comp_grid_rates.find(
        (r) => r.comp_grid_criterion_id === criteriaId
      );
    if (condition.operator === CustomMethodConditionOperator.NOT_EQUAL) {
      return !BigNumber(currentPayeeRates.rate)
        .minus(payeeRates.rate)
        .abs()
        .eq(BigNumber(condition.value));
    } else {
      return BigNumber(currentPayeeRates.rate)
        .minus(payeeRates.rate)
        .abs()
        [operatorMap[condition.operator]](BigNumber(condition.value));
    }
  };

  isActivateCustomMethod = async (
    config: CustomMethodConfig,
    lookupData,
    statement,
    req: { account_id: string },
    compProfileConfig,
    useCompGrids: boolean,
    cache?: LRUCacheService
  ) => {
    const payeeId = config.payee_id;
    if (config?.conditions?.length === 0) {
      return true;
    }

    let isActive = true;
    // All conditions must be true for the method to be active
    for (const condition of config.conditions ?? []) {
      if (
        condition.type ===
        CustomMethodConditionType.PAYEE_LEVEL_RATE_DIFFERENCE_THRESHOLD
      ) {
        isActive = await this.checkPayeeRateDifference(
          payeeId,
          condition,
          lookupData,
          statement,
          req,
          compProfileConfig,
          useCompGrids,
          cache
        );
        if (!isActive) {
          break;
        }
      }
      if (condition.type === CustomMethodConditionType.COMPENSATION_TYPE) {
        isActive = await this.checkCompensationType(condition, statement);
        if (!isActive) {
          break;
        }
      }
    }
    return isActive;
  };

  getValueSign = (value: number): -1 | 0 | 1 => {
    if (!value) return 0;
    return value < 0 ? -1 : 1;
  };

  alignSign = (
    value: BigNumber,
    sign: -1 | 0 | 1,
    additionalSign: -1 | 0 | 1 = 1
  ): BigNumber =>
    value.isZero() || sign === 0
      ? value
      : value.abs().multipliedBy(sign * (additionalSign || 1));

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  getEffectiveSplit = (report: any = {}) => {
    if (
      Object.values(report?.contacts_commission_split ?? {}).some(
        (split) => !Number.isNaN(+split)
      )
    ) {
      return report?.contacts_commission_split;
    }
    return report?.contacts_split;
  };

  getUsedSeenPolicyCommissions: (
    accountId: string,
    contactStrId: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    statement: any
  ) => Promise<{
    [key: string]: { seen: number; used: number };
  }> = async (accountId, contactStrId, statement) => {
    const prevRecords = await prisma.statement_data.findMany({
      where: {
        account_id: accountId,
        report_data_id: statement.report.id,
        OR: [
          { agent_commissions_status: { equals: CommissionStatuses.APPROVED } },
          { agent_commissions_status: { equals: CommissionStatuses.MANUAL } },
          { agent_commissions_status: { equals: CommissionStatuses.PAID } },
        ],
        state: { in: [DataStates.ACTIVE, DataStates.GROUPED] },
        id: { not: statement.id },
      },
    });

    const result = prevRecords.reduce((acc, cur) => {
      acc[cur.id] = {
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        used: +((cur.agent_commissions as any)?.[contactStrId] ?? 0),
        seen: numberOrDefault(+cur.commission_amount, 0),
      };
      return acc;
    }, {});

    return result;
  };

  isSalesRep = (contact) =>
    contact?.type &&
    (Array.isArray(contact.type)
      ? contact.type.map((s) => s.toLowerCase()).includes('sales rep')
      : contact.type?.toLowerCase() === 'sales rep');

  isIMO = (contact) =>
    contact?.type &&
    (Array.isArray(contact.type)
      ? contact.type.map((s) => s.toLowerCase()).includes('imo')
      : contact.type?.toLowerCase() === 'imo');

  // CommissionRate should ensured to be divided by 100 before passing into this function
  adjustRate = async (
    { commissionRate, agentApplicableProfile },
    lookupData,
    statement,
    req: { account_id: string },
    useCompGrids: boolean,
    cache?: LRUCacheService
  ) => {
    if (!agentApplicableProfile?.agentProfileConfig?.config) {
      const profile = agentApplicableProfile?.agentProfileConfig;
      agentApplicableProfile.agentProfileConfig.config = [
        {
          method: profile.method,
          rate: profile.rate,
          payee_id: profile.payee_id,
          multiplier: profile.multiplier,
        },
      ];
    }

    let addOrSetConfig =
      agentApplicableProfile?.agentProfileConfig?.config.filter((r) =>
        ['set', 'add'].includes(r.method)
      );

    for (const config of addOrSetConfig) {
      if (
        !(await this.isActivateCustomMethod(
          config,
          lookupData,
          statement,
          req,
          agentApplicableProfile.agentProfileConfig,
          useCompGrids,
          cache
        ))
      ) {
        addOrSetConfig = addOrSetConfig.filter((c) => c !== config);
      }
    }
    if (!addOrSetConfig || addOrSetConfig.length === 0) {
      return BigNumber(commissionRate);
    }

    if (addOrSetConfig[0].method === 'set') {
      return BigNumber(agentApplicableProfile.agentProfileConfig.rate || 0).div(
        100
      );
    }

    if (addOrSetConfig[0].method === 'add') {
      return BigNumber(commissionRate).plus(
        BigNumber(addOrSetConfig[0].rate || 0).div(100)
      );
    }
    return BigNumber(commissionRate);
  };

  isCareMatters = (statement) =>
    statement?.product_name?.toLowerCase()?.includes('carematters') ||
    statement?.report?.product_name?.toLowerCase()?.includes('carematters');

  addFlagIfConditionMet = (
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    statement: any,
    ctx: CommissionCalcContext,
    rule: {
      label: string;
      value: string;
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      condition: (statement: any) => boolean;
    }
  ) => {
    if (rule.condition(statement)) ctx.updateFlags(statement, rule);
  };

  formatPaytoAlerts(
    originPayeeStrId: string,
    finalPayeeStrId: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    contactsMap: Record<string, any>
  ) {
    const originPayee = contactsMap[originPayeeStrId];
    const finalPayee = contactsMap[finalPayeeStrId];
    if (!finalPayeeStrId) {
      return 'All agents in the hierarchy are in archived status, no payee is available.';
    }
    if (finalPayeeStrId !== originPayeeStrId) {
      return `Agent ${Formatter.contact(originPayee)} is not active, pay to ${Formatter.contact(finalPayee)}`;
    }
    return undefined;
  }

  determineExtraAgentsToFetch(
    statement: StatementDataWithReport,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    profiles: any[]
  ) {
    const profileAgentStrIds = profiles.map((r) => r.agentStrId);
    const overridedConfig = {
      ...((statement?.report
        ?.agent_payout_rate_override as AgentPayoutRateOverride) ?? {}),
      ...((statement?.agent_payout_rate_override as AgentPayoutRateOverride) ??
        {}),
    };
    return Object.keys(overridedConfig)
      .filter((key) => key !== 'config')
      .filter(
        (key) =>
          !profileAgentStrIds.includes(key) && !statement.contacts.includes(key)
      )
      .map((agentStrId) => agentStrId);
  }

  standardizeCommissionResult(result: { [key: string]: number | BigNumber }) {
    return Object.fromEntries(
      Object.entries(result || {}).map(([key, value]) => {
        return [key, BigNumber(value).dp(2).toNumber()];
      })
    );
  }

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  filterApplicableProfile(profile: any, statement: StatementDataWithReport) {
    if (statement.compensation_type !== CompensationTypes.RENEWAL_COMMISSION) {
      return true;
    }
    if (!profile?.agentName) {
      return false;
    }
    return profile?.agentName
      ?.toUpperCase()
      .includes(CompensationTypes.RENEWAL.toUpperCase());
  }

  findProfilesWithNoHierarchyProcessing(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    ancestryApplicableProfilesAll: any[],
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    ancestryProfiles: any[],
    writingAgentsStrid: string[]
  ) {
    const writingAgentsProfilesWithNoHierarchyProcessing = new Set(
      ancestryApplicableProfilesAll
        .filter((profile) => {
          const commissionScheduleProfile =
            profile.agentCommissionProfile?.agentProfileConfig
              ?.agent_commission_schedule_profile;

          return (
            commissionScheduleProfile?.hierarchy_processing === 'none' &&
            writingAgentsStrid.includes(profile.agentStrId)
          );
        })
        .map((profile) => profile.agentId)
    );

    const excludedUplineAgentsIds = new Set();
    const uplineIdsMap = new Map(
      ancestryProfiles.map((profile) => [
        profile.agentId,
        profile.uplineIds || [],
      ])
    );

    for (const agentId of writingAgentsProfilesWithNoHierarchyProcessing) {
      const queue = [...(uplineIdsMap.get(agentId) || [])];

      while (queue.length > 0) {
        const currentUplineId = queue.shift();
        if (excludedUplineAgentsIds.has(currentUplineId)) continue;

        excludedUplineAgentsIds.add(currentUplineId);
        const nextUplineIds = uplineIdsMap.get(currentUplineId) || [];
        for (const uplineId of nextUplineIds) {
          if (!excludedUplineAgentsIds.has(uplineId)) {
            queue.push(uplineId);
          }
        }
      }
    }

    return ancestryApplicableProfilesAll.filter(
      (profile) => !excludedUplineAgentsIds.has(profile.agentId)
    );
  }

  getDownLineProfiles(ancestryApplicableProfiles, agentStrId) {
    if (!ancestryApplicableProfiles || !agentStrId) {
      return { downlineIndex: -1, downlineProfile: null };
    }
    const downlineIndex =
      ancestryApplicableProfiles.findIndex((e) => e.agentStrId === agentStrId) -
      1;
    if (downlineIndex < 0) {
      return { downlineIndex, downlineProfile: null };
    }
    const { agentCommissionProfile: downlineProfile } =
      ancestryApplicableProfiles[downlineIndex] ?? {};
    return { downlineIndex, downlineProfile };
  }

  calculateDownlineAdjustments = async (
    ancestryApplicableProfiles,
    agentStrId,
    statementContactStrId,
    agentCommissionProfile,
    payeeLevel,
    criterionId,
    payoutRateOverride,
    lookupData,
    statement,
    req,
    useCompGrids,
    directProducerRateMap
  ) => {
    const payeeRates = payeeLevel?.comp_grid_rates?.find(
      (rate) => rate.comp_grid_criterion_id === criterionId
    );
    let payoutRate = BigNumber(
      payeeLevel?.comp_grid_rates?.find(
        (rate) => rate.comp_grid_criterion_id === criterionId
      )?.rate ?? 0
    );

    const currentMultiplier = BigNumber(
      numberOrDefault(
        agentCommissionProfile?.agentProfileConfig?.multiplier,
        100
      )
    ).div(100);

    const { downlineIndex, downlineProfile } = this.getDownLineProfiles(
      ancestryApplicableProfiles,
      agentStrId
    );

    let downlineRate = BigNumber(0);
    let downlineMultiplier = BigNumber(1);
    let downLinePayeeRates = BigNumber(0);
    let currentPayeeRate = BigNumber(0);
    if (downlineIndex >= 0) {
      const downlineLevel =
        downlineProfile?.agentProfileConfig?.agent_commission_schedule_profile
          ?.payee_comp_grid_level;

      const downlineRates = downlineLevel?.comp_grid_rates?.find(
        (rate) =>
          rate.comp_grid_criterion_id === downlineProfile.matchedCriteriaId
      );

      downlineMultiplier = BigNumber(
        numberOrDefault(downlineProfile?.agentProfileConfig?.multiplier, 100)
      ).div(100);

      downLinePayeeRates = BigNumber(downlineRates?.rate ?? 0);
      currentPayeeRate = BigNumber(payeeRates?.rate ?? 0);

      const downlineHouseRate = downlineRates?.house_rate ?? 0;

      // Possible that downlineHouseRate is the desired variable,
      // but we need further testing. Start by turning on for WorldChangers
      downlineRate = new BigNumber(downlineHouseRate ?? 0).div(100);

      const updateCarrierRate = BigNumber(downlineRates?.carrier_rate ?? 0);
      const updateHouseRate = BigNumber(payeeRates?.rate).minus(
        updateCarrierRate
      );
      payeeRates.carrier_rate = updateCarrierRate;
      payeeRates.house_rate = updateHouseRate;
      payoutRate = updateHouseRate;
    }

    let commissionRate = payoutRate.div(100).minus(downlineRate);

    if (payoutRateOverride) {
      commissionRate = BigNumber(payoutRateOverride).div(100);
    }

    commissionRate = await this.adjustRate(
      {
        commissionRate,
        agentApplicableProfile: agentCommissionProfile,
      },
      lookupData,
      statement,
      { account_id: req.account_id },
      useCompGrids
    );

    // Update direct producer map if applicable
    if (agentStrId === statementContactStrId) {
      directProducerRateMap[agentStrId] = commissionRate;
    }

    const currentCommissionRate =
      currentMultiplier.multipliedBy(currentPayeeRate);
    const downLineCommissionRate =
      downlineMultiplier.multipliedBy(downLinePayeeRates);

    const effectiveCommissionRate = this.isCommissionRateDifference(
      currentMultiplier,
      currentPayeeRate,
      downlineMultiplier,
      downLinePayeeRates
    )
      ? currentCommissionRate.minus(downLineCommissionRate).div(100)
      : currentMultiplier.multipliedBy(commissionRate);

    return {
      effectiveCommissionRate,
      commissionRate,
      payeeRates,
      payoutRate,
    };
  };

  isCommissionRateDifference(
    currentMultiplier,
    currentAgentRate,
    downlineMultiplier,
    downLineAgentRates
  ) {
    const isRateMismatch = !currentMultiplier
      .multipliedBy(currentAgentRate)
      .isEqualTo(downlineMultiplier.multipliedBy(downLineAgentRates));
    const hasPositiveDifference = currentMultiplier
      .minus(downlineMultiplier)
      .isGreaterThan(0);

    const isNotOneMultiplier = !currentMultiplier.isEqualTo(1);

    return isRateMismatch && hasPositiveDifference && isNotOneMultiplier;
  }

  getDownlineAgentSplit(
    downlineIndex,
    downlineStrId,
    agentId,
    ancestryProfiles,
    statement,
    hierarchySplitPercentageMap
  ) {
    if (downlineIndex === -1) {
      let currentAgentId = agentId;
      while (currentAgentId) {
        downlineIndex = ancestryProfiles.findIndex((profile) =>
          profile.uplineIds.includes(currentAgentId)
        );
        currentAgentId = ancestryProfiles[downlineIndex]?.agentId ?? null;
        if (currentAgentId) {
          downlineStrId = ancestryProfiles[downlineIndex]?.agentStrId ?? null;
        }
      }
    }

    const defaultDowLineSplitPercentage =
      statement?.report?.contacts_split?.[downlineStrId] ?? 100;
    const downLineSplitPercentage =
      defaultDowLineSplitPercentage ??
      hierarchySplitPercentageMap[downlineStrId];

    return BigNumber(downLineSplitPercentage).div(100);
  }

  checkInvalidRate(
    rates: {
      carrier_rate?: number | null;
      house_rate?: number | null;
      rate?: number | null;
    },
    fieldsToCheck: (keyof typeof rates)[]
  ): boolean {
    if (!rates) {
      return true;
    }
    return fieldsToCheck.some(
      (field) => rates[field] === null || rates[field] === undefined
    );
  }
}
