import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  CustomMethodConditionOperator,
  CustomMethodConditionType,
  CustomMethodConditionField,
} from 'common/interface';
import { BigNumber } from 'bignumber.js';
import { CompensationTypes } from 'common/globalTypes';

import { CommissionUtils } from './commissionUtils';
import type { CommissionCalcContext } from './commissionCalContext';

vi.mock('@/lib/prisma', () => ({
  prismaClient: {
    contacts: {
      findFirst: vi.fn(),
    },
    statement_data: {
      findMany: vi.fn(),
    },
  },
}));
vi.mock('@/lib/commissions', () => ({
  getContactAncestorsWithCommissionProfiles: vi.fn(),
  getApplicableCompProfiles: vi.fn(),
}));

// biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
let mockCtx;

describe('CommissionUtils', () => {
  let utils: CommissionUtils;

  beforeEach(() => {
    mockCtx = {
      flags: [],
      updateFlags: vi.fn((statement, rule) => {
        mockCtx.flags.push({ statement, rule });
      }),
    } as unknown as CommissionCalcContext;
    utils = new CommissionUtils();
    vi.clearAllMocks();
  });

  describe('checkCompensationType', () => {
    it('should return true for EQUAL operator with matching field and value', async () => {
      const condition = {
        operator: CustomMethodConditionOperator.EQUAL,
        value: 'typeA',
        field: CustomMethodConditionField.ACCOUNT_TYPE,
      };
      const statement = { compensation_type: 'typeA' };

      const result = await utils.checkCompensationType(condition, statement);

      expect(result).toBe(true);
    });

    it('should return true if the specified field does not match', async () => {
      const condition = {
        operator: CustomMethodConditionOperator.EQUAL,
        value: 'typeA',
        field: CustomMethodConditionField.ACCOUNT_TYPE,
      };
      const statement = { compensation_type: 'typeA', payee_type: 'typeB' };

      const result = await utils.checkCompensationType(condition, statement);

      expect(result).toBe(true);
    });

    it('should handle CONTAINED_IN operator with a specific field and JSON array value', async () => {
      const condition = {
        operator: CustomMethodConditionOperator.CONTAINED_IN,
        value: '["typeA", "typeB"]',
        field: CustomMethodConditionField.ACCOUNT_TYPE,
      };
      const statement = { compensation_type: 'typeA' };

      const result = await utils.checkCompensationType(condition, statement);

      expect(result).toBe(true);
    });
  });

  describe('isActivateCustomMethod', () => {
    it('should return true when all conditions (with field) are satisfied', async () => {
      const config = {
        start_date: new Date('2025-01-01'),
        end_date: new Date('2025-12-31'),
        method: 'add',
        rate: 5,
        multiplier: 2,
        conditions: [
          {
            type: CustomMethodConditionType.COMPENSATION_TYPE,
            operator: CustomMethodConditionOperator.EQUAL,
            value: 'typeA',
            field: CustomMethodConditionField.ACCOUNT_TYPE,
          },
        ],
        payee_id: 123,
      };
      const statement = { compensation_type: 'typeA' };

      const result = await utils.isActivateCustomMethod(
        config,
        {},
        statement,
        { account_id: 'account1' },
        {},
        false
      );

      expect(result).toBe(true);
    });

    it('should return false when a condition (with field) fails', async () => {
      const config = {
        start_date: new Date('2025-01-01'),
        method: 'add',
        rate: 5,
        multiplier: 2,
        conditions: [
          {
            type: CustomMethodConditionType.COMPENSATION_TYPE,
            operator: CustomMethodConditionOperator.EQUAL,
            value: 'typeB',
            field: CustomMethodConditionField.ACCOUNT_TYPE,
          },
        ],
      };
      const statement = { compensation_type: 'typeA' };

      const result = await utils.isActivateCustomMethod(
        config,
        {},
        statement,
        { account_id: 'account1' },
        {},
        false
      );

      expect(result).toBe(false);
    });
  });

  describe('adjustRate', () => {
    it('should adjust rate when conditions with fields are satisfied', async () => {
      const config = {
        start_date: new Date('2025-01-01'),
        method: 'add',
        rate: 5,
        multiplier: 2,
        conditions: [
          {
            type: CustomMethodConditionType.COMPENSATION_TYPE,
            operator: CustomMethodConditionOperator.EQUAL,
            value: 'typeA',
            field: CustomMethodConditionField.ACCOUNT_TYPE,
          },
        ],
      };

      const mockAgentProfile = {
        agentProfileConfig: {
          config: [config],
        },
      };

      vi.spyOn(utils, 'isActivateCustomMethod').mockResolvedValue(true);

      const result = await utils.adjustRate(
        { commissionRate: 10, agentApplicableProfile: mockAgentProfile },
        {},
        {},
        { account_id: 'test' },
        false
      );

      expect(result.toNumber()).toBe(10.05);
    });

    it('should not adjust rate if field condition fails', async () => {
      const config = {
        start_date: new Date('2025-01-01'),
        method: 'add',
        rate: 5,
        multiplier: 2,
        conditions: [
          {
            type: CustomMethodConditionType.COMPENSATION_TYPE,
            operator: CustomMethodConditionOperator.EQUAL,
            value: 'typeB',
            field: CustomMethodConditionField.ACCOUNT_TYPE,
          },
        ],
      };

      const mockAgentProfile = {
        agentProfileConfig: {
          config: [config],
        },
      };

      vi.spyOn(utils, 'isActivateCustomMethod').mockResolvedValue(false);

      const result = await utils.adjustRate(
        { commissionRate: 10, agentApplicableProfile: mockAgentProfile },
        {},
        {},
        { account_id: 'test' },
        false
      );

      expect(result.toNumber()).toBe(10);
    });
  });

  describe('alignSign', () => {
    it('should return the same value if value is zero', () => {
      const value = new BigNumber(0);
      const result = utils.alignSign(value, 1, 1);
      expect(result.toString()).toBe(value.toString());
    });

    it('should return the same value if sign or additionalSign is 0', () => {
      const value = new BigNumber(123);
      expect(utils.alignSign(value, 0, 1).toString()).toBe(value.toString());
      expect(utils.alignSign(value, 0, -1).toString()).toBe(value.toString());
      expect(utils.alignSign(value, 1, 0).toString()).toBe(value.toString());
      expect(utils.alignSign(value, -1, 0).toString()).toBe(
        value.negated().toString()
      );
    });

    it('should return the absolute value with correct final sign', () => {
      const value = new BigNumber(-123);
      expect(utils.alignSign(value, 1, 1).toString()).toBe('123');
      expect(utils.alignSign(value, 1, -1).toString()).toBe('-123');
      expect(utils.alignSign(value, -1, 1).toString()).toBe('-123');
      expect(utils.alignSign(value, -1, -1).toString()).toBe('123');
    });

    it('should handle cases where additionalSign is omitted', () => {
      const value = new BigNumber(123);
      expect(utils.alignSign(value, 1).toString()).toBe('123');
      expect(utils.alignSign(value, -1).toString()).toBe('-123');
    });

    it('should return the correct value for all combinations of signs', () => {
      const value = new BigNumber(234);
      expect(utils.alignSign(value, 1, 1).toString()).toBe('234');
      expect(utils.alignSign(value, -1, 1).toString()).toBe('-234');
      expect(utils.alignSign(value, -1, -1).toString()).toBe('234');
      expect(utils.alignSign(value, 1, -1).toString()).toBe('-234');
    });

    it('should handle negative value inputs correctly', () => {
      const value = new BigNumber(-234);
      expect(utils.alignSign(value, 1, 1).toString()).toBe('234');
      expect(utils.alignSign(value, -1, 1).toString()).toBe('-234');
      expect(utils.alignSign(value, -1, -1).toString()).toBe('234');
      expect(utils.alignSign(value, 1, -1).toString()).toBe('-234');
    });
  });
  describe('isCareMatters', () => {
    it('should return true if product_name includes "carematters"', () => {
      const statement = { product_name: 'CareMatters Product' };
      expect(utils.isCareMatters(statement)).toBe(true);
    });

    it('should return true if report.product_name includes "carematters"', () => {
      const statement = {
        report: { product_name: 'CareMatters Plan' },
      };
      expect(utils.isCareMatters(statement)).toBe(true);
    });

    it('should return false if "carematters" is not present', () => {
      const statement = {
        product_name: 'Other Product',
        report: { product_name: 'Other Product' },
      };
      expect(utils.isCareMatters(statement)).toBe(false);
    });
  });

  describe('addFlagIfConditionMet', () => {
    it('should call updateFlags and modify flags when condition is true', () => {
      const statement = { field: 'value' };
      const rule = {
        label: 'Test Rule',
        value: 'Test Value',
        condition: (stmt) => stmt.field === 'value',
      };

      utils.addFlagIfConditionMet(statement, mockCtx, rule);
      expect(mockCtx.updateFlags).toHaveBeenCalledWith(statement, rule);
      expect(mockCtx.flags).toContainEqual({ statement, rule });
    });

    it('should not call updateFlags and not modify flags when condition is false', () => {
      const statement = { field: 'differentValue' };
      const rule = {
        label: 'Test Rule',
        value: 'Test Value',
        condition: (stmt) => stmt.field === 'value',
      };

      utils.addFlagIfConditionMet(statement, mockCtx, rule);
      expect(mockCtx.updateFlags).not.toHaveBeenCalled();
      expect(mockCtx.flags).toEqual([]);
    });
  });

  describe('standardizeCommissionResult', () => {
    it('should convert all values to numbers from BigNumber instances', () => {
      const input = {
        agent1: new BigNumber(123.451),
        agent2: new BigNumber(678.9),
      };

      const result = utils.standardizeCommissionResult(input);

      expect(result).toEqual({
        agent1: 123.45,
        agent2: 678.9,
      });
      expect(typeof result.agent1).toBe('number');
      expect(typeof result.agent2).toBe('number');
    });

    it('should handle number values correctly', () => {
      const input = {
        agent1: 123.45,
        agent2: 678.9,
      };

      const result = utils.standardizeCommissionResult(input);

      expect(result).toEqual({
        agent1: 123.45,
        agent2: 678.9,
      });
      expect(typeof result.agent1).toBe('number');
      expect(typeof result.agent2).toBe('number');
    });

    it('should handle mixed BigNumber and number values', () => {
      const input = {
        agent1: new BigNumber(123.45),
        agent2: 678.9,
      };

      const result = utils.standardizeCommissionResult(input);

      expect(result).toEqual({
        agent1: 123.45,
        agent2: 678.9,
      });
      expect(typeof result.agent1).toBe('number');
      expect(typeof result.agent2).toBe('number');
    });

    it('should return an empty object for empty input', () => {
      const result = utils.standardizeCommissionResult({});
      expect(result).toEqual({});
    });

    it('should handle null or undefined input by returning an empty object', () => {
      const result = utils.standardizeCommissionResult(null);
      expect(result).toEqual({});
    });
  });

  describe('findProfilesWithNoHierarchyProcessing', () => {
    it('should exclude profiles with upline IDs of profiles with no hierarchy processing', () => {
      const ancestryApplicableProfilesAll = [
        {
          agentId: 'A1',
          agentStrId: 'S1',
          agentCommissionProfile: {
            agentProfileConfig: {
              agent_commission_schedule_profile: {
                hierarchy_processing: 'none',
              },
            },
          },
        },
        {
          agentId: 'A2',
          agentStrId: 'S2',
          agentCommissionProfile: {
            agentProfileConfig: {
              agent_commission_schedule_profile: {
                hierarchy_processing: 'standard',
              },
            },
          },
        },
        {
          agentId: 'A3',
          agentStrId: 'S3',
          agentCommissionProfile: {
            agentProfileConfig: {
              agent_commission_schedule_profile: {
                hierarchy_processing: 'none',
              },
            },
          },
        },
      ];
      const ancestryProfiles = [
        { agentId: 'A1', uplineIds: ['A3'] },
        { agentId: 'A2', uplineIds: ['U3'] },
        { agentId: 'A3', uplineIds: ['A1'] },
      ];
      const writingAgentsStrid = ['S1', 'S2', 'S3'];

      const result = utils.findProfilesWithNoHierarchyProcessing(
        ancestryApplicableProfilesAll,
        ancestryProfiles,
        writingAgentsStrid
      );

      expect(result).toEqual([
        {
          agentId: 'A2',
          agentStrId: 'S2',
          agentCommissionProfile: {
            agentProfileConfig: {
              agent_commission_schedule_profile: {
                hierarchy_processing: 'standard',
              },
            },
          },
        },
      ]);
    });

    it('should return all profiles if none have hierarchy processing set to "none"', () => {
      const ancestryApplicableProfilesAll = [
        {
          agentId: 'A1',
          agentStrId: 'S1',
          agentCommissionProfile: {
            agentProfileConfig: {
              agent_commission_schedule_profile: {
                hierarchy_processing: 'standard',
              },
            },
          },
        },
        {
          agentId: 'A2',
          agentStrId: 'S2',
          agentCommissionProfile: {
            agentProfileConfig: {
              agent_commission_schedule_profile: {
                hierarchy_processing: 'standard',
              },
            },
          },
        },
      ];
      const ancestryProfiles = [
        { agentId: 'A1', uplineIds: ['U1', 'U2'] },
        { agentId: 'A2', uplineIds: ['U3'] },
      ];
      const writingAgentsStrid = ['S1', 'S2'];

      const result = utils.findProfilesWithNoHierarchyProcessing(
        ancestryApplicableProfilesAll,
        ancestryProfiles,
        writingAgentsStrid
      );

      expect(result).toEqual(ancestryApplicableProfilesAll);
    });

    it('should return an empty array if all profiles are excluded due to hierarchy processing set to "none"', () => {
      const ancestryApplicableProfilesAll = [
        {
          agentId: 'A1',
          agentStrId: 'S1',
          agentCommissionProfile: {
            agentProfileConfig: {
              agent_commission_schedule_profile: {
                hierarchy_processing: 'none',
              },
            },
          },
        },
        {
          agentId: 'A2',
          agentStrId: 'S2',
          agentCommissionProfile: {
            agentProfileConfig: {
              agent_commission_schedule_profile: {
                hierarchy_processing: 'none',
              },
            },
          },
        },
        {
          agentId: 'A3',
          agentStrId: 'S3',
          agentCommissionProfile: {
            agentProfileConfig: {
              agent_commission_schedule_profile: {
                hierarchy_processing: 'none',
              },
            },
          },
        },
        {
          agentId: 'A4',
          agentStrId: 'S4',
          agentCommissionProfile: {
            agentProfileConfig: {
              agent_commission_schedule_profile: {
                hierarchy_processing: 'none',
              },
            },
          },
        },
      ];
      const ancestryProfiles = [
        { agentId: 'A1', uplineIds: ['A2'] },
        { agentId: 'A3', uplineIds: ['A4'] },
      ];
      const writingAgentsStrid = ['S1', 'S3'];

      const result = utils.findProfilesWithNoHierarchyProcessing(
        ancestryApplicableProfilesAll,
        ancestryProfiles,
        writingAgentsStrid
      );

      expect(result).toEqual([
        {
          agentId: 'A1',
          agentStrId: 'S1',
          agentCommissionProfile: {
            agentProfileConfig: {
              agent_commission_schedule_profile: {
                hierarchy_processing: 'none',
              },
            },
          },
        },
        {
          agentId: 'A3',
          agentStrId: 'S3',
          agentCommissionProfile: {
            agentProfileConfig: {
              agent_commission_schedule_profile: {
                hierarchy_processing: 'none',
              },
            },
          },
        },
      ]);
    });

    it('should handle empty input arrays gracefully', () => {
      const ancestryApplicableProfilesAll = [];
      const ancestryProfiles = [];
      const writingAgentsStrid = [];

      const result = utils.findProfilesWithNoHierarchyProcessing(
        ancestryApplicableProfilesAll,
        ancestryProfiles,
        writingAgentsStrid
      );

      expect(result).toEqual([]);
    });

    it('should ignore profiles not in writingAgentsStrid', () => {
      const ancestryApplicableProfilesAll = [
        {
          agentId: 'A1',
          agentStrId: 'S1',
          agentCommissionProfile: {
            agentProfileConfig: {
              agent_commission_schedule_profile: {
                hierarchy_processing: 'none',
              },
            },
          },
        },
        {
          agentId: 'A2',
          agentStrId: 'S2',
          agentCommissionProfile: {
            agentProfileConfig: {
              agent_commission_schedule_profile: {
                hierarchy_processing: 'standard',
              },
            },
          },
        },
      ];
      const ancestryProfiles = [
        { agentId: 'A1', uplineIds: ['A3'] },
        { agentId: 'A2', uplineIds: ['U3'] },
      ];
      const writingAgentsStrid = ['S2'];

      const result = utils.findProfilesWithNoHierarchyProcessing(
        ancestryApplicableProfilesAll,
        ancestryProfiles,
        writingAgentsStrid
      );

      expect(result).toEqual([
        {
          agentId: 'A1',
          agentStrId: 'S1',
          agentCommissionProfile: {
            agentProfileConfig: {
              agent_commission_schedule_profile: {
                hierarchy_processing: 'none',
              },
            },
          },
        },
        {
          agentId: 'A2',
          agentStrId: 'S2',
          agentCommissionProfile: {
            agentProfileConfig: {
              agent_commission_schedule_profile: {
                hierarchy_processing: 'standard',
              },
            },
          },
        },
      ]);
    });
  });

  const createMockProfile = (
    agentStrId,
    level = null,
    rates = null,
    multiplier = 100
  ) => ({
    agentStrId,
    agentCommissionProfile: {
      matchedCriteriaId: 0,
      agentProfileConfig: {
        agent_commission_schedule_profile: level
          ? {
              payee_comp_grid_level: {
                comp_grid_rates: rates
                  ? [
                      {
                        comp_grid_criterion_id: 0,
                        rate: rates.rate,
                        house_rate: rates.house_rate,
                        carrier_rate: rates.carrier_rate,
                      },
                    ]
                  : [],
              },
            }
          : null,
        multiplier,
      },
    },
  });

  const createMockRates = (rate, houseRate, carrierRate) => ({
    comp_grid_criterion_id: 0,
    rate: rate ?? 0,
    house_rate: houseRate ?? 0,
    carrier_rate: carrierRate ?? 0,
  });

  describe('calculateDownlineAdjustments', () => {
    it('should return early values if statementContactStrId equals agentStrId', async () => {
      const payeeLevel = { comp_grid_rates: [createMockRates(0.25, 0.3, 0.6)] };
      const agentCommissionProfile = {
        agentProfileConfig: { multiplier: 100 },
      };
      const result = await utils.calculateDownlineAdjustments(
        [],
        'agent1',
        'agent1',
        agentCommissionProfile,
        payeeLevel,
        0,
        null,
        null,
        null,
        { account_id: 'test-account' },
        false,
        {}
      );
      expect(result).toEqual(
        expect.objectContaining({
          effectiveCommissionRate: expect.any(BigNumber),
          commissionRate: expect.any(BigNumber),
          payeeRates: expect.any(Object),
          payoutRate: expect.any(BigNumber),
        })
      );
    });

    it('should return early values if downlineIndex is less than 0', async () => {
      const ancestryApplicableProfiles = [
        createMockProfile('agent1'),
        createMockProfile('agent2'),
      ];
      const payeeLevel = { comp_grid_rates: [createMockRates(0.3, 0.4, 0.5)] };
      const agentCommissionProfile = {
        agentProfileConfig: { multiplier: 100 },
      };
      const result = await utils.calculateDownlineAdjustments(
        ancestryApplicableProfiles,
        'agent3',
        'statement1',
        agentCommissionProfile,
        payeeLevel,
        0,
        null,
        null,
        null,
        { account_id: 'test-account' },
        false,
        {}
      );
      expect(result.payeeRates).toBeDefined();
    });

    it('should calculate downlineRate correctly based on house_rate', async () => {
      const ancestryApplicableProfiles = [
        createMockProfile('agent1'),
        createMockProfile(
          'agent2',
          {
            payee_comp_grid_level: {
              comp_grid_rates: [createMockRates(0.2, 1.5, 0.5)],
            },
          },
          createMockRates(0.2, 1.5, 0.5),
          50
        ),
      ];
      const payeeLevel = { comp_grid_rates: [createMockRates(0.5, 1, 0.5)] };
      const agentCommissionProfile = {
        agentProfileConfig: { multiplier: 100 },
      };
      const result = await utils.calculateDownlineAdjustments(
        ancestryApplicableProfiles,
        'agent2',
        'statement1',
        agentCommissionProfile,
        payeeLevel,
        0,
        null,
        null,
        null,
        { account_id: 'test-account' },
        false,
        {}
      );

      expect(result.payoutRate).toEqual(BigNumber(0.5));
    });

    it('should handle missing rates gracefully', async () => {
      const ancestryApplicableProfiles = [createMockProfile('agent1')];
      const payeeLevel = { comp_grid_rates: [] };
      const agentCommissionProfile = {
        agentProfileConfig: { multiplier: 100 },
      };
      const result = await utils.calculateDownlineAdjustments(
        ancestryApplicableProfiles,
        'agent2',
        'statement1',
        agentCommissionProfile,
        payeeLevel,
        0,
        null,
        null,
        null,
        { account_id: 'test-account' },
        false,
        {}
      );

      expect(result.payeeRates).toBeUndefined();
    });
  });

  describe('isCommissionRateDifference', () => {
    it('should return true when there is a rate mismatch and a positive difference', () => {
      const currentMultiplier = new BigNumber(2);
      const currentPayeeRate = new BigNumber(0.5);
      const downlineMultiplier = new BigNumber(1.5);
      const downLinePayeeRates = new BigNumber(0.6);

      const result = utils.isCommissionRateDifference(
        currentMultiplier,
        currentPayeeRate,
        downlineMultiplier,
        downLinePayeeRates
      );

      expect(result).toBe(true);
    });

    it('should return false when there is no rate mismatch', () => {
      const currentMultiplier = new BigNumber(2);
      const currentPayeeRate = new BigNumber(0.5);
      const downlineMultiplier = new BigNumber(2);
      const downLinePayeeRates = new BigNumber(0.5);

      const result = utils.isCommissionRateDifference(
        currentMultiplier,
        currentPayeeRate,
        downlineMultiplier,
        downLinePayeeRates
      );

      expect(result).toBe(false);
    });

    it('should return false when there is no positive difference', () => {
      const currentMultiplier = new BigNumber(1.5);
      const currentPayeeRate = new BigNumber(0.6);
      const downlineMultiplier = new BigNumber(2);
      const downLinePayeeRates = new BigNumber(0.5);

      const result = utils.isCommissionRateDifference(
        currentMultiplier,
        currentPayeeRate,
        downlineMultiplier,
        downLinePayeeRates
      );

      expect(result).toBe(false);
    });

    it('should return false when both rate mismatch and positive difference conditions fail', () => {
      const currentMultiplier = new BigNumber(2);
      const currentPayeeRate = new BigNumber(0.5);
      const downlineMultiplier = new BigNumber(2);
      const downLinePayeeRates = new BigNumber(0.5);

      const result = utils.isCommissionRateDifference(
        currentMultiplier,
        currentPayeeRate,
        downlineMultiplier,
        downLinePayeeRates
      );

      expect(result).toBe(false);
    });
  });

  describe('getDownLineProfiles', () => {
    const ancestryApplicableProfiles = [
      { agentStrId: 'A1', agentCommissionProfile: { multiplier: 120 } },
      { agentStrId: 'A2', agentCommissionProfile: { multiplier: 110 } },
      { agentStrId: 'A3', agentCommissionProfile: { multiplier: 100 } },
    ];

    it('should return correct downline profile and index', () => {
      const result = utils.getDownLineProfiles(
        ancestryApplicableProfiles,
        'A2'
      );
      expect(result).toEqual({
        downlineIndex: 0,
        downlineProfile: ancestryApplicableProfiles[0].agentCommissionProfile,
      });
    });

    it('should return null profile and index less than 0 if agentStrId is not found', () => {
      const result = utils.getDownLineProfiles(
        ancestryApplicableProfiles,
        'A4'
      );
      expect(result).toEqual({ downlineIndex: -2, downlineProfile: null });
    });

    it('should return null profile and index less than 0 if agentStrId or ancestryApplicableProfiles is null', () => {
      const result = utils.getDownLineProfiles(null, null);
      expect(result).toEqual({ downlineIndex: -1, downlineProfile: null });
    });

    it('should handle empty profiles array gracefully', () => {
      const result = utils.getDownLineProfiles([], 'A1');
      expect(result).toEqual({ downlineIndex: -2, downlineProfile: null });
    });
  });

  describe('calculateDownlineAdjustments', () => {
    const ancestryApplicableProfiles = [
      {
        agentStrId: 'A1',
        agentCommissionProfile: {
          agentProfileConfig: { multiplier: 120 },
        },
      },
      {
        agentStrId: 'A2',
        agentCommissionProfile: {
          agentProfileConfig: { multiplier: 110 },
        },
      },
    ];
    const agentStrId = 'A2';
    const statementContactStrId = 'A3';
    const payeeLevel = {
      comp_grid_rates: [
        {
          comp_grid_criterion_id: 1,
          rate: 0.5,
          house_rate: 0.3,
          carrier_rate: 0.2,
        },
      ],
    };
    const agentCommissionProfile = {
      agentProfileConfig: { multiplier: 150 },
    };
    const criterionId = 1;
    const payoutRateOverride = new BigNumber(0.4);
    const lookupData = {};
    const statement = {};
    const req = { account_id: '123' };
    const useCompGrids = true;
    const directProducerRateMap = {};

    it('should calculate effective commission rate when all data is provided', async () => {
      const result = await utils.calculateDownlineAdjustments(
        ancestryApplicableProfiles,
        agentStrId,
        statementContactStrId,
        agentCommissionProfile,
        payeeLevel,
        criterionId,
        null,
        lookupData,
        statement,
        req,
        useCompGrids,
        directProducerRateMap
      );

      expect(result).toEqual({
        effectiveCommissionRate: expect.any(BigNumber),
        commissionRate: expect.any(BigNumber),
        payeeRates: expect.any(Object),
        payoutRate: expect.any(BigNumber),
      });
    });

    it('should apply payoutRateOverride when provided', async () => {
      const result = await utils.calculateDownlineAdjustments(
        ancestryApplicableProfiles,
        agentStrId,
        statementContactStrId,
        agentCommissionProfile,
        payeeLevel,
        criterionId,
        payoutRateOverride,
        lookupData,
        statement,
        req,
        useCompGrids,
        directProducerRateMap
      );

      expect(result.commissionRate).toEqual(new BigNumber(0.004));
    });

    it('should handle payeeLevels gracefully', async () => {
      const result = await utils.calculateDownlineAdjustments(
        ancestryApplicableProfiles,
        agentStrId,
        statementContactStrId,
        agentCommissionProfile,
        payeeLevel,
        criterionId,
        null,
        lookupData,
        statement,
        req,
        useCompGrids,
        directProducerRateMap
      );

      expect(result).toEqual({
        effectiveCommissionRate: new BigNumber(0.0075),
        commissionRate: new BigNumber(0.005),
        payeeRates: {
          comp_grid_criterion_id: 1,
          rate: 0.5,
          house_rate: new BigNumber(0.5),
          carrier_rate: new BigNumber(0),
        },
        payoutRate: new BigNumber(0.5),
      });
    });
  });

  describe('getDownlineAgentSplit', () => {
    it('should correctly calculate downline split percentage when a direct match is found', () => {
      const params = {
        downlineIndex: 0,
        downlineStrId: 'agent-123',
        agentId: '001',
        ancestryProfiles: [],
        statement: {
          report: {
            contacts_split: {
              'agent-123': 80,
            },
          },
        },
        hierarchySplitPercentageMap: {},
      };

      const result = utils.getDownlineAgentSplit(
        params.downlineIndex,
        params.downlineStrId,
        params.agentId,
        params.ancestryProfiles,
        params.statement,
        params.hierarchySplitPercentageMap
      );

      expect(result).toEqual(new BigNumber(0.8));
    });

    it('should fallback to upline logic when downline index is -1', () => {
      const params = {
        downlineIndex: -1,
        downlineStrId: null,
        agentId: '001',
        ancestryProfiles: [
          { agentId: '001', agentStrId: 'agent-001', uplineIds: [] },
          { agentId: '123', agentStrId: 'agent-123', uplineIds: ['001'] },
        ],
        statement: {
          report: {},
        },
        hierarchySplitPercentageMap: {
          'agent-123': 90,
        },
      };

      const result = utils.getDownlineAgentSplit(
        params.downlineIndex,
        params.downlineStrId,
        params.agentId,
        params.ancestryProfiles,
        params.statement,
        params.hierarchySplitPercentageMap
      );

      expect(result).toEqual(new BigNumber(1));
    });

    it('should default downline split percentage to 1 when not found', () => {
      const params = {
        downlineIndex: 0,
        downlineStrId: 'agent-789',
        agentId: '001',
        ancestryProfiles: [],
        statement: {},
        hierarchySplitPercentageMap: {},
      };

      const result = utils.getDownlineAgentSplit(
        params.downlineIndex,
        params.downlineStrId,
        params.agentId,
        params.ancestryProfiles,
        params.statement,
        params.hierarchySplitPercentageMap
      );

      expect(result).toEqual(new BigNumber(1));
    });
  });

  describe('checkInvalidRate', () => {
    it('should return true when rates is null', () => {
      const rates = null;
      expect(
        utils.checkInvalidRate(rates, ['carrier_rate', 'house_rate', 'rate'])
      ).toBe(true);
    });

    it('should return false when all specified rates are present', () => {
      const rates = { carrier_rate: 100, house_rate: 50, rate: 30 };
      expect(
        utils.checkInvalidRate(rates, ['carrier_rate', 'house_rate', 'rate'])
      ).toBe(false);
    });

    it('should return true when carrier_rate is missing', () => {
      const rates = { carrier_rate: null, house_rate: 50, rate: 30 };
      expect(
        utils.checkInvalidRate(rates, ['carrier_rate', 'house_rate', 'rate'])
      ).toBe(true);
    });

    it('should return true when house_rate is missing and only house_rate is checked', () => {
      const rates = { carrier_rate: 100, house_rate: null, rate: 30 };
      expect(utils.checkInvalidRate(rates, ['house_rate'])).toBe(true);
    });

    it('should return true when carrier_rate and house_rate are missing', () => {
      const rates = { carrier_rate: null, house_rate: null, rate: 30 };
      expect(
        utils.checkInvalidRate(rates, ['carrier_rate', 'house_rate'])
      ).toBe(true);
    });

    it('should return true when all specified rates are missing', () => {
      const rates = { carrier_rate: null, house_rate: null, rate: null };
      expect(
        utils.checkInvalidRate(rates, ['carrier_rate', 'house_rate', 'rate'])
      ).toBe(true);
    });

    it('should return false when no rates are checked', () => {
      const rates = { carrier_rate: null, house_rate: null, rate: null };
      expect(utils.checkInvalidRate(rates, [])).toBe(false);
    });

    it('should handle undefined fields gracefully', () => {
      const rates = { carrier_rate: undefined, house_rate: 50, rate: 30 };
      expect(
        utils.checkInvalidRate(rates, ['carrier_rate', 'house_rate'])
      ).toBe(true);
    });

    it('should return false for missing fields not included in fieldsToCheck', () => {
      const rates = { carrier_rate: null, house_rate: 50, rate: 30 };
      expect(utils.checkInvalidRate(rates, ['house_rate', 'rate'])).toBe(false);
    });
  });

  describe('filterApplicableProfile', () => {
    it('should return true if compensation type is not RENEWAL_COMMISSION', () => {
      const profile = { agentName: 'Some Agent' };
      const statement = { compensation_type: 'ADVANCE' };

      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const result = utils.filterApplicableProfile(profile, statement as any);

      expect(result).toBe(true);
    });

    it('should return true if compensation type is RENEWAL_COMMISSION and agentName includes "RENEWAL"', () => {
      const profile = { agentName: 'John Renewal Smith' };
      const statement = {
        compensation_type: CompensationTypes.RENEWAL_COMMISSION,
      };

      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const result = utils.filterApplicableProfile(profile, statement as any);

      expect(result).toBe(true);
    });

    it('should return false if compensation type is RENEWAL_COMMISSION and agentName does not include "RENEWAL"', () => {
      const profile = { agentName: 'John Smith' };
      const statement = {
        compensation_type: CompensationTypes.RENEWAL_COMMISSION,
      };

      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const result = utils.filterApplicableProfile(profile, statement as any);

      expect(result).toBe(false);
    });

    it('should return false if compensation type is RENEWAL_COMMISSION and agentName is undefined', () => {
      const profile = {};
      const statement = {
        compensation_type: CompensationTypes.RENEWAL_COMMISSION,
      };

      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const result = utils.filterApplicableProfile(profile, statement as any);

      expect(result).toBe(false);
    });
  });
});
