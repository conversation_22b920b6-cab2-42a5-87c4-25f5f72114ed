import { Decimal } from '@prisma/client/runtime/library';
import BigNumber from 'bignumber.js';
import { inject, injectable } from 'inversify';
import { isEqual } from 'lodash-es';
import type winston from 'winston';
import { AccessTypes } from 'common/globalTypes';

import { SERVICE_TYPES } from '@/constants';
import dayjs from '@/lib/dayjs';
import { BusinessException } from '@/lib/exceptionHandler';
import { prismaClient } from '@/lib/prisma';
import type { AppLoggerService } from '@/services/logger/appLogger';
import { QueueService } from '@/services/queue';
import type { DBData, IDataSyncWorker } from '@/services/queue/worker/base';
import { DataProcessingTypes, type ExtAccountInfo } from '@/types';

@injectable()
export class SyncFieldService {
  @inject(QueueService) service: QueueService;
  private logger: AppLoggerService;
  constructor(
    @inject(SERVICE_TYPES.LoggerServiceFactory)
    loggerServiceFactory: (
      options: Parameters<typeof winston.createLogger>[0]
    ) => AppLoggerService
  ) {
    this.logger = loggerServiceFactory({
      defaultMeta: { service: 'SyncFieldService' },
    });
  }
  async getSyncFields(account: ExtAccountInfo, workerName?: string) {
    const worker = (await this.service.getWorker({
      type: DataProcessingTypes.data_sync,
      account,
      payload: { entities: [], worker: workerName },
    })) as IDataSyncWorker<unknown>;
    this.logger.debug('getSyncFields for account', { account });
    if (!worker) return [];

    const transfomers = worker.getAvailableTransformers();
    const tableFields = transfomers.reduce((acc, curr) => {
      acc[curr.table] = [
        ...new Set([
          ...Object.keys(curr.transformer({}, {} as DBData)),
          ...(acc[curr.table] || []),
        ]),
      ];
      return acc;
    }, {});

    this.logger.debug('fields returned', { tableFields });
    return tableFields;
  }

  async canUpdateIfChanged(params: {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    newData: Record<string, any>;
    tableName: string;
    account: ExtAccountInfo;
    id: number;
    config?: { overrideFields?: string[] };
    accountInject?: boolean;
  }) {
    const {
      id,
      config,
      tableName,
      account,
      newData,
      accountInject = true,
    } = params;

    const origin = await prismaClient[tableName].findFirst({
      where: { id },
      accountInject,
    });
    if (origin?.access === AccessTypes.GLOBAL) {
      this.logger.info(
        'Global company detected, skipping sync field validation',
        { id, tableName }
      );
      return;
    }

    if (config) {
      const finalConfig = { ...(origin.config || {}), ...config };
      await prismaClient[tableName].update({
        where: { id },
        data: { config: finalConfig },
      });
      origin.config = finalConfig;
    }
    // If not synced data, permit to edit
    if (origin.sync_id === null) {
      return;
    }
    // biome-ignore lint/suspicious/noDoubleEquals: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (newData.sync_id !== undefined && origin.sync_id != newData.sync_id) {
      throw BusinessException.from('Sync id cannot be updated');
    }
    let errorFields = [];
    const tableFields = await this.getSyncFields(account, origin.sync_worker);
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    tableFields[tableName]?.forEach((field) => {
      if (
        field === 'sync_id' ||
        !Object.hasOwn(newData, field) ||
        newData[field] === undefined
      ) {
        return;
      }
      if (origin[field] === null && isEqual(newData[field], {})) {
        return;
      } else if (origin[field] instanceof Decimal) {
        if (
          BigNumber(origin[field]).toFixed(2) !==
          BigNumber(newData[field]).toFixed(2)
        ) {
          errorFields.push(field);
        }
      } else if (origin[field] instanceof Date) {
        if (
          !dayjs
            .utc(origin[field])
            .startOf('day')
            .isSame(dayjs.utc(newData[field]).startOf('day'))
        ) {
          errorFields.push(field);
        }
      } else if (!isEqual(origin[field], newData[field])) {
        errorFields.push(field);
      }
    });
    if (origin?.config?.overrideFields) {
      errorFields = errorFields.filter(
        (r) => !origin?.config?.overrideFields.includes(r)
      );
    }
    if (errorFields.length > 0) {
      const plural = errorFields.length > 1 ? 'are' : 'is';
      throw BusinessException.from(
        `${errorFields.join(', ')} ${plural} synced fields which cannot be updated`
      );
    }
  }
}
