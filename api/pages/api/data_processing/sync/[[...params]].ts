import 'reflect-metadata';
import { Catch, Get, Post, Req, createH<PERSON><PERSON> } from 'next-api-decorators';
import * as dto from 'common/dto/data_processing/sync';
import { ProcessorConfigVO } from 'common/vo/data_processing/index';
import { WorkerNames } from 'common/constants';
import type { NextApiRequest } from 'next';

import {
  AccountInfo,
  convertModelToVO,
  convertModelToVOList,
  EnhancedQuery,
  ZodBody,
} from '@/lib/decorators';
import {
  DataProcessingTypes,
  DataStates,
  type ExtAccountInfo,
  type ExtNextApiRequest,
} from '@/types';
import { BusinessException, exceptionHandler } from '@/lib/exceptionHandler';
import { QueueService } from '@/services/queue';
import { container } from '@/ioc';
import { withAuth } from '@/lib/middlewares';
import { AccountProcessorConfigService } from '@/services/account-processor-config';
import { Queue } from '@/services/queue/types';
import { NowCertsWorker } from '@/services/queue/worker/nowCerts';
import { BenefitPointWorker } from '@/services/queue/worker/benefitPoint';
import { SyncFieldService } from '@/pages/api/data_processing/sync/syncFieldService';
import { ReportService } from '@/services/report';
import type { ConfigItemValueForDataSync } from '@/services/account-processor-config/interfaces';
import { CommonAction } from '@/services/permission/interface';
import { EntityType } from '@/services/permission/interface';
import { Guard } from '@/services/permission/decorator';

@Catch(exceptionHandler)
class SyncHandler {
  configItemService: AccountProcessorConfigService;

  reportService: ReportService;

  syncFieldService: SyncFieldService;

  constructor() {
    // ConfigItemService is not request specific, so it can be injected here
    this.configItemService = container.get<AccountProcessorConfigService>(
      AccountProcessorConfigService
    );
    this.reportService = container.get<ReportService>(ReportService);

    this.syncFieldService = container.get<SyncFieldService>(SyncFieldService);
  }

  @Post()
  @Guard(CommonAction.RUN, EntityType.SETTINGS_DATA_PROCESSING)
  async syncData(
    @Req() _req: ExtNextApiRequest & NextApiRequest,
    // biome-ignore format: compound decorator
    @(ZodBody(dto.SyncParamsSchema)())
    body: dto.SyncParamsDTO,
    @AccountInfo()
    account: ExtAccountInfo
  ) {
    const queueService = container.get<QueueService>(QueueService);
    const entities = await this.configItemService.getEntitiesToSync(
      account.account_id,
      body.entities,
      body.worker as WorkerNames
    );
    if (entities.length === 0) {
      throw BusinessException.from('No valid entities to sync');
    }
    if (body.worker === WorkerNames.OneHQWorker && body.entities.length > 1) {
      throw BusinessException.from(
        'OneHQWorker only supports syncing one entity at a time'
      );
    }
    const params = {
      account: account,
      type: DataProcessingTypes.data_sync,
      payload: {
        ...body,
        entities,
        queue: Queue.DATA_PROCESSING,
        worker: body.worker,
      },
    };
    const taskId = await queueService.createTask<dto.SyncParamsDTO>(params);
    if (body.sync) {
      await queueService.process<dto.SyncParamsDTO>({
        ...params,
        task_id: taskId,
      });
    }
    return { success: true, taskId };
  }

  @Post('/nowcerts/policy')
  async syncNowCertsPolicy(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.SyncNowCertsPolicySchema)()) body: dto.SyncNowCertsPolicyDTO,
    @AccountInfo() account: ExtAccountInfo
  ) {
    const worker = container.get<NowCertsWorker>(NowCertsWorker);
    await worker.setup(account);

    let syncIds = [];
    if (body.syncId) {
      syncIds.push(body.syncId);
    }
    if (body.ids) {
      const policies = await this.reportService.getReportData({
        id: { in: body.ids },
      });
      syncIds = syncIds.concat(policies.map((r) => r.sync_id));
    }

    for (const syncId of syncIds) {
      const policyDetail = await worker.getPolicyBySyncId(syncId);
      if (!policyDetail) {
        throw BusinessException.from('Policy not found');
      }
      await worker.updateExtraInfo(policyDetail, account);
    }

    return true;
  }

  @Get('/worker')
  async get(@EnhancedQuery() query: dto.QueryAccountEntitiesDTO) {
    const result = await this.configItemService.getDataSyncConfig(
      query.accountId
    );
    return convertModelToVO(ProcessorConfigVO, result);
  }
  @Get('/workers')
  async getWorkers(@EnhancedQuery() query: dto.QueryAccountEntitiesDTO) {
    const result = await this.configItemService.getDataSyncConfigs(
      query.accountId
    );
    return convertModelToVOList(ProcessorConfigVO, result);
  }

  @Post('/benefit-point/statements')
  async syncBenefitPointStatements(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.SyncBenefitPointStatementsDTOSchema)())
    body: dto.SyncBenefitPointStatementsDTO,
    @AccountInfo() account: ExtAccountInfo
  ) {
    const benefitWorker = container.get<BenefitPointWorker>(BenefitPointWorker);
    const stats = await benefitWorker.syncBenefitPointStatements({
      ...body,
      account,
    });
    return stats;
  }

  @Post('/benefit-point/member-count')
  async syncBenefitPointMemberCount(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.SyncBenefitPointMemberCountDTOSchema)())
    body: dto.SyncBenefitPointMemberCountDTO,
    @AccountInfo() account: ExtAccountInfo
  ) {
    const benefitWorker = container.get<BenefitPointWorker>(BenefitPointWorker);
    await benefitWorker.setup(account);
    const stats = await benefitWorker.syncBenefitPointMemberCount(
      body.documentId
    );
    return { success: true, stats };
  }

  @Get('/synced-fields')
  async getSyncedFields(@AccountInfo() account: ExtAccountInfo) {
    return await this.syncFieldService.getSyncFields(account);
  }

  @Get('/worker-synced-fields')
  async getWorkerSyncedFields(@AccountInfo() account: ExtAccountInfo) {
    const configs = await this.configItemService.getAccountConfigByType<
      ConfigItemValueForDataSync<unknown>
    >({
      type: 'dataSync',
      account_id: account.account_id,
      states: [DataStates.ACTIVE, DataStates.DELETED],
    });
    const getWorkerSyncedFields = await Promise.all(
      configs.map(async (config) => [
        config.value.worker,
        await this.syncFieldService.getSyncFields(account, config.value.worker),
      ])
    );
    return Object.fromEntries(getWorkerSyncedFields);
  }

  @Get('/workers-entities')
  async getWorkersEntities() {
    const queueService = container.get<QueueService>(QueueService);
    const workers = queueService.dataSyncWorkers;

    const workersEntities = {};
    for (const worker of workers) {
      const availableEntities = worker.getAvailableEntities();
      workersEntities[worker.name] = availableEntities;
    }

    return workersEntities;
  }
}

export default withAuth(createHandler(SyncHandler));

// export default createHandler(SyncHandler);
