import { <PERSON>Hand<PERSON> } from '@/lib/baseHandler';
import { AccountInfo, ZodBody } from '@/lib/decorators';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtAccountInfo } from '@/types';
import type { NextApiRequest } from 'next';
import { createHandler, Post, Req } from 'next-api-decorators';
import {
  ReceivableCalcSchema,
  type ReceivableCalcRequest,
} from 'common/dto/data_processing/receivable_calc';
import { container } from '@/ioc';
import { _getReportData } from '../../report_data';
import { ReceivableScheduleService } from '@/services/receivable-schedule';

class Handler extends BaseHandler {
  private receivableScheduleService: ReceivableScheduleService;

  constructor() {
    super();
    this.receivableScheduleService = container.get<ReceivableScheduleService>(
      ReceivableScheduleService
    );
  }

  @Post()
  async handle(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @AccountInfo() account: ExtAccountInfo,
    // biome-ignore format: compound decorator
    @(ZodBody(ReceivableCalcSchema)()) body: ReceivableCalcRequest
  ) {
    const data = await _getReportData(req, body);
    const result = await this.receivableScheduleService.executeReceivableCalc(
      data,
      account,
      body,
      req.ouid
    );

    return {
      taskId: result.taskId,
      success: result.success,
    };
  }
}

export default withAuth(createHandler(Handler));
