import * as Sentry from '@sentry/nextjs';

import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';

/** Deprecated and will remove soon. use v2/data_processing */
const handler = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  switch (req.method) {
    case 'GET':
      await getData(req, res);
      break;
    case 'PATCH':
      await patchData(req, res);
      break;
    case 'DELETE':
      await deleteData(req, res);
      break;
    default:
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
};

export default withAuth(handler, { adminOnly: true });

const TABLE = 'data_processing';

export const getData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  dynamicSelect = false
) => {
  try {
    const where = {
      account_id: req.account_id,
      state: 'active',
      master_str_id: null,
      str_id: undefined,
      companies: undefined,
    };
    if (req.query?.id) {
      where.str_id = req.query.id;
    }
    if (req.query?.incl_tasks?.toLowerCase() === 'true') {
      where.master_str_id = undefined;
    }
    const data = await prisma[TABLE].findMany({
      where,
      take: 250,
      orderBy: {
        created_at: 'desc',
      },
    });

    if (dynamicSelect) return data;
    res.json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error('Error getting agent commission schedule profile', error);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const patchData = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const body = req.body; // As DataProcessing;
  try {
    if (!body.id) throw new Error('Missing id');
    const { id: _id, output: _output, uid: _uid, ...rest } = body;
    const data = await prisma[TABLE].update({
      where: { id: Number(body.id), account_id: String(req.account_id) },
      data: {
        ...rest,
        updated_at: new Date(),
        updated_by: req.uid,
      },
    });
    res.status(200).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error('Error updating data', error);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const deleteData = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const { ids } = req.body;
  try {
    if (!Array.isArray(ids) || ids.length === 0) throw new Error('Missing ids');
    // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
      throw new Error('Invalid id(s)');
    const promises = ids.map((_id) => {
      return prisma[TABLE].update({
        where: { id: Number(_id), account_id: String(req.account_id) },
        data: {
          state: 'deleted',
          updated_at: new Date(),
          updated_by: req.uid,
        },
      });
    });
    await prisma.$transaction(promises);
    res.status(200).json({ status: 'OK' });
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error('Error deleting data', error);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};
