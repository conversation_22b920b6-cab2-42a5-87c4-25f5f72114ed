import type { NextApiRequest, NextApiResponse } from 'next';
import { create<PERSON><PERSON><PERSON>, Post, Req, Res } from 'next-api-decorators';
import type { CreateCustomerDTO } from 'common/customer/customer.validator';
import type { CreateCustomerFromPolicies } from 'common/customer/customer.types';

import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { CustomerService } from '@/services/customers/service';
import { container } from '@/ioc';

class Handler extends BaseHandler {
  private service: CustomerService;
  constructor() {
    super();
    this.service = container.get(CustomerService);
  }

  @Post()
  async bulkAdd(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const customers = req.body.map((item: Partial<CreateCustomerDTO>) => ({
      ...item,
      account_id: req.account_id,
      created_by: req.uid,
      created_proxied_by: req.ouid,
    })) as (Partial<CreateCustomerDTO> & CreateCustomerFromPolicies)[];

    const result = await this.service.createCustomerFromPolicies(customers);
    res.json({ success: result });
  }
}

export default withAuth(createHandler(Handler));
