import type { NextApiRequest } from 'next';
import { createH<PERSON>ler, Get, Req } from 'next-api-decorators';

import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest } from '@/types';
import { CustomerService } from '@/services/customers/service';
import { container } from '@/ioc';

class Handler extends BaseHandler {
  private service: CustomerService;
  constructor() {
    super();
    this.service = container.get(CustomerService);
  }

  @Get()
  async search(@Req() req: ExtNextApiRequest & NextApiRequest) {
    return this.service.searchName(req.query.q, req.account_id);
  }
}

export default withAuth(createHandler(Handler));
