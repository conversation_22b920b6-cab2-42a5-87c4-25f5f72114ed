import type { NextApiRequest, NextApiResponse } from 'next';
import { create<PERSON><PERSON><PERSON>, Get, Req, Res } from 'next-api-decorators';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import {
  ContactOptionsService,
  type IContactOptionsService,
} from '@/services/contact/options';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';

class ContactOptionsHandler extends BaseHandler {
  private contactOptionsService: ContactOptionsService;

  constructor() {
    super();
    this.contactOptionsService = container.get<ContactOptionsService>(
      ContactOptionsService
    );
  }

  @Get()
  async getContactOptions(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await getData(req, res, this.contactOptionsService);
  }
}

export default withAuth(createHandler(ContactOptionsHandler));

const getData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  contactOptionsService: IContactOptionsService
) => {
  let savedReportGroupId = '';
  if (req.query?.saved_report_group)
    savedReportGroupId = req.query.saved_report_group as string;

  const options = await contactOptionsService.getContactOptions(
    req.account_id,
    savedReportGroupId
  );

  res.json(options);
};
