import { Catch, create<PERSON><PERSON><PERSON>, <PERSON>, Req } from 'next-api-decorators';
import type { NextApiRequest } from 'next';
import {
  CarrierGridLevelArraySchema,
  type CarrierGridLevelDTO,
} from 'common/dto/contact/carrier-grid-level';

import { BaseHandler } from '@/lib/baseHandler';
import type { ExtNextApiRequest } from '@/types';
import { withAuth } from '@/lib/middlewares';
import { ContactsUpdateService } from '@/services/contacts/contacts_update_service';
import { container } from '@/ioc';
import { exceptionHandler } from '@/lib/exceptionHandler';
import { ZodBody } from '@/lib/decorators';

@Catch(exceptionHandler)
class Handler extends BaseHandler {
  private service: ContactsUpdateService;
  constructor() {
    super();
    this.service = container.get(ContactsUpdateService);
  }

  @Patch()
  async bulkUpdateCarrierGridLevels(
    // biome-ignore format: compound decorator
    @(ZodBody(CarrierGridLevelArraySchema)())
    body: CarrierGridLevelDTO[],
    @Req() req: ExtNextApiRequest & NextApiRequest
  ) {
    await this.service.bulkUpdateCarrierGridLevels({
      data: body,
      account_id: req.account_id,
      uid: req.uid,
      ouid: req.ouid,
    });
    return { success: true };
  }
}

export default withAuth(createHandler(Handler));
