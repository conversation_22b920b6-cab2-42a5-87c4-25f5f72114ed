import { Catch, createH<PERSON><PERSON>, <PERSON>, Req } from 'next-api-decorators';
import type { NextApiRequest } from 'next';

import { BaseHandler } from '@/lib/baseHandler';
import type { ExtNextApiRequest } from '@/types';
import { withAuth } from '@/lib/middlewares';
import { ContactsUpdateService } from '@/services/contacts/contacts_update_service';
import { container } from '@/ioc';
import { exceptionHandler } from '@/lib/exceptionHandler';

@Catch(exceptionHandler)
class Handler extends BaseHandler {
  private service: ContactsUpdateService;
  constructor() {
    super();
    this.service = container.get(ContactsUpdateService);
  }

  @Post()
  async bulkUpdate(@Req() req: ExtNextApiRequest & NextApiRequest) {
    const data = req.body;
    return this.service.bulkUpdateUpline({
      data,
      account_id: req.account_id,
      uid: req.uid,
      ouid: req.ouid,
    });
  }
}

export default withAuth(createHandler(Handler));
