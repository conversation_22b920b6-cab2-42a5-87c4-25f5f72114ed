import * as Sentry from '@sentry/nextjs';

import prisma from '@/lib/prisma';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';

// Endpoint for getting contacts/Agents not linked to a user
const handler = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  switch (req.method) {
    case 'GET':
      await getData(req, res);
      break;
    default:
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
};

export default withAuth(handler);

const getData = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  try {
    const where = {
      account_id: req.account_id,
      state: 'active',
      str_id: undefined,
      user_str_id: null,
    };
    if (req.query?.id) {
      where.str_id = req.query.id;
    }
    const data = await prisma.contacts.findMany({
      where,
      include: {
        user_contact: {
          select: {
            state: true,
          },
        },
      },
    });
    res.json(data);
  } catch (e) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(
      `An error occurred getting contacts without user: ${e.message}`
    );
    Sentry.captureException(e);
    res.status(500).json({ error: e.message });
  }
};
