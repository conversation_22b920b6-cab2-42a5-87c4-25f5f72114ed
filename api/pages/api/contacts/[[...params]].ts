import * as Sentry from '@sentry/nextjs';
import { nanoid } from 'nanoid';
import type { NextApiRequest, NextApiResponse } from 'next';
import {
  Delete,
  Get,
  Patch,
  Post,
  Query,
  Req,
  Re<PERSON>,
  createHandler,
} from 'next-api-decorators';
// biome-ignore lint/correctness/noUndeclaredDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import validator from 'validator';
import { hasInvalidDateRange } from 'common/validators/agents/validator';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import { ContactService, type IContactService } from '@/services/contact';
import type {
  ExtAccountInfo,
  ExtNextApiRequest,
  ExtNextApiResponse,
} from '@/types';
import { SyncFieldService } from '@/pages/api/data_processing/sync/syncFieldService';
import { ContactsQueries } from '@/queries/contactsQueries';
import {
  ContactHierarchyService,
  type IContactHierarchyService,
} from '@/services/contact/hierarchy';
import {
  ContactGroupService,
  type IContactGroupService,
} from '@/services/contact/group';
import {
  AccountingService,
  type IAccountingService,
} from '@/services/accounting';
import { Guard } from '@/services/permission/decorator';
import { CrudAction, EntityType } from '@/services/permission/interface';
import {
  ContactLevelService,
  type IContactLevelService,
} from '@/services/contact/levels';
import type { Contact } from '@/pages/api/contacts/types';
import dayjs from '@/lib/dayjs';

class ContactsHandler extends BaseHandler {
  private contactService: ContactService;
  private contactHierarchyService: ContactHierarchyService;
  private contactLevelService: IContactLevelService;
  private contactGroupService: ContactGroupService;
  private contactsQueries: ContactsQueries;
  private accountingService: IAccountingService;

  constructor() {
    super();
    this.contactService = container.get<ContactService>(ContactService);
    this.contactLevelService =
      container.get<IContactLevelService>(ContactLevelService);
    this.contactHierarchyService = container.get<ContactHierarchyService>(
      ContactHierarchyService
    );
    this.contactGroupService =
      container.get<ContactGroupService>(ContactGroupService);
    this.contactsQueries = container.get<ContactsQueries>(ContactsQueries);
    this.accountingService =
      container.get<IAccountingService>(AccountingService);
  }

  @Get()
  @Guard(CrudAction.READ, EntityType.AGENTS)
  async getContactsData(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @Query('ids') ids: string,
    @Query('is_dynamic_select') isDynamicSelect: string
  ) {
    try {
      const queryNestedContacts = this.queryNestedContacts;

      if (isDynamicSelect === 'true' && ids) {
        const idsArray = ids
          .split(',')
          .map((id) => parseInt(id, 10))
          // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          .filter((id) => !isNaN(id));

        // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        let childResult;
        if (req.query?.add_contact_children) {
          const result = this.contactsQueries.getNestedContacts(
            [],
            '9',
            queryNestedContacts
          );
          childResult = result.childResult;
        }
        const contactList = await this.contactService.getContactDataByIdList(
          idsArray,
          childResult
        );
        return res.json(contactList);
      }

      const data = await this.contactsQueries._getData(
        req,
        queryNestedContacts
      );
      res.json(data);
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(
        `An error occurred when getting contacts: ${error.message}`
      );
      Sentry.captureException(error);
      res.status(500).json({ error: error.message });
    }
  }

  @Post()
  @Guard(CrudAction.CREATE, EntityType.AGENTS)
  async postContacts(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return postData(
      req,
      res,
      this.contactGroupService,
      this.accountingService,
      this.contactLevelService
    );
  }

  @Patch()
  @Guard(CrudAction.UPDATE, EntityType.AGENTS)
  async patchContacts(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return patchData(
      req,
      res,
      this.contactService,
      this.contactHierarchyService,
      this.contactGroupService,
      this.contactLevelService
    );
  }

  @Delete()
  @Guard(CrudAction.DELETE, EntityType.AGENTS)
  async deleteContacts(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return deleteData(req, res);
  }
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '2mb',
    },
  },
};

export default withAuth(createHandler(ContactsHandler));

const TABLE = 'contacts';

// Hierarchy currently supports up to depth 10 based on Prisma nested include query structure.
// https://github.com/prisma/prisma/issues/4562

const updateContactCompProfiles = async (
  contact,
  compProfiles,
  req,
  type = ''
) => {
  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let keyToUse;
  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let idToUse;
  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let tableToUse;
  if (type === 'sets') {
    keyToUse = 'contacts_agent_commission_schedule_profiles_sets';
    idToUse = 'agent_commission_schedule_profile_set_id';
    tableToUse = 'agent_commission_schedule_profiles_sets';
  } else {
    keyToUse = 'contacts_agent_commission_schedule_profiles';
    idToUse = 'agent_commission_schedule_profile_id';
    tableToUse = 'agent_commission_schedule_profile';
  }
  const validateDateRange = (startDate, endDate) => {
    return startDate && endDate ? validator.isBefore(startDate, endDate) : true;
  };

  if (Array.isArray(compProfiles)) {
    const hasInvalidDateRange = compProfiles.some(
      (profile) => !validateDateRange(profile.start_date, profile.end_date)
    );
    if (hasInvalidDateRange) {
      throw new Error('Invalid date range, start date must be before end date');
    }
  }

  // New profiles will have a field separator setted in the frontend (::) this was made so we can enter multiple profiles for a same agent
  // Here we separate the actual id using the separator
  // If a profile already exits this will fail and go to the catch block
  compProfiles = compProfiles?.map((profile) => {
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let profileId;
    try {
      profileId = parseInt(profile[idToUse].split('::')[0]);
      return {
        ...profile,
        [idToUse]: profileId,
      };
    } catch {
      return {
        ...profile,
      };
    }
  });

  // Delete all contacts_agent_commission_schedule_profiles/sets for this contact
  await prisma[keyToUse].deleteMany({
    where: {
      contact_id: contact.id,
    },
  });

  if (Array.isArray(compProfiles)) {
    // Loop over contacts_agent_commission_schedule_profiles/sets
    for (const profileId of compProfiles) {
      await prisma[keyToUse].create({
        data: {
          account_id: req.account_id,
          uid: req.uid,
          str_id: nanoid(),
          created_at: new Date(),
          created_by: req.uid,
          created_proxied_by: req.ouid,
          end_date: profileId.end_date ? new Date(profileId.end_date) : null,
          method: profileId.method,
          multiplier: +profileId.multiplier,
          rate: +profileId.rate,
          start_date: profileId.start_date
            ? new Date(profileId.start_date)
            : null,
          payee_id: profileId.payee_id,
          config: profileId.config,
          [tableToUse]: {
            connect: {
              id: profileId[idToUse],
            },
          },
          contact: {
            connect: {
              id: contact.id,
            },
          },
        },
      });
    }
  }
};

const postData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  contactGroupService: IContactGroupService,
  // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  accountingService: IAccountingService,
  contactLevelService: IContactLevelService
) => {
  const body = req.body as Contact;
  try {
    const invalidDateRangeKey = hasInvalidDateRange(body);
    if (invalidDateRangeKey) {
      throw new Error(`Invalid date range, start date must be before end date`);
    }

    if (body.parent_id && body.contact_group_id) {
      throw new Error(
        'Cannot set both parent and group. Group can only be set for root contacts.'
      );
    }

    const {
      id: _id,
      report_contacts: _report_contacts,
      agent_level: _agent_level,
      user_str_id: _user_str_id,
      contact_group_id,
      contacts_agent_commission_schedule_profiles,
      contacts_agent_commission_schedule_profiles_sets,
      parent_relationships,
      contact_level,
      agency_contact_levels,
      contact_memos,
      contact_referrals,
      ...rest
    } = body;

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    [contact_memos, contact_referrals].forEach((data) => {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (data ?? []).forEach((datum) => {
        if (typeof datum?.id === 'string') delete datum.id;
      });
    });

    const data = {
      ...rest,
      str_id: nanoid(),
      account_id: req.account_id,
      uid: req.uid,
      created_by: req.uid,
      created_proxied_by: req.ouid,
      contact_group:
        contact_group_id && !parent_relationships
          ? {
              connect: { id: contact_group_id },
            }
          : undefined,
    };

    const resultData = await prisma[TABLE].create({
      data,
    });

    if (Array.isArray(contact_memos)) {
      for await (const memo of contact_memos) {
        const { match_critera: _match_criteria, ..._memo } = memo;
        await prisma.contact_memos.create({
          data: {
            ..._memo,
            account_id: req.account_id,
            created_by: req.uid,
            created_proxied_by: req.ouid,
            contact: { connect: { id: resultData.id } },
          },
        });
      }
    }

    if (Array.isArray(contact_referrals)) {
      for await (const referral of contact_referrals) {
        const {
          id: _id,
          contact_id: _contact_id,
          payer_contact_id,
          referrer_contact_id,
          ..._referral
        } = referral;
        await prisma.contact_referrals.create({
          data: {
            ..._referral,
            account_id: req.account_id,
            created_by: req.uid,
            created_proxied_by: req.ouid,
            contact: { connect: { id: resultData.id } },
            payer: payer_contact_id
              ? { connect: { id: +payer_contact_id } }
              : undefined,
            referrer: referrer_contact_id
              ? { connect: { id: +referrer_contact_id } }
              : undefined,
          },
        });
      }
    }

    if (Array.isArray(contact_level) && contact_level.length > 0) {
      await contactLevelService.createContactLevels({
        contactId: resultData.id,
        accountId: req.account_id,
        userId: req.uid,
        userProxyId: req.ouid,
        contactLevels: contact_level,
      });
    }

    if (
      Array.isArray(agency_contact_levels) &&
      agency_contact_levels.length > 0
    ) {
      await contactLevelService.createAgencyContactLevels({
        agencyContactId: resultData.id,
        accountId: req.account_id,
        userId: req.uid,
        userProxyId: req.ouid,
        agencyContactLevels: agency_contact_levels,
      });
    }

    if (parent_relationships && Array.isArray(parent_relationships)) {
      const parentGroups = [];
      for (const parent_relationship of parent_relationships) {
        // TODO: How to handle multiple parent groups?
        const parentGroup = await contactGroupService.getContactGroupDataById(
          parent_relationship.parent.id,
          req.account_id
        );
        parentGroups.push(parentGroup);

        await prisma.contact_hierarchy.create({
          data: {
            start_date: parent_relationship.start_date
              ? new Date(parent_relationship.start_date)
              : null,
            end_date: parent_relationship.end_date
              ? new Date(parent_relationship.end_date)
              : null,
            split_percentage:
              parent_relationship.split_percentage === null ||
              parent_relationship.split_percentage === ''
                ? null
                : +parent_relationship.split_percentage,
            updated_at: new Date(),
            account_id: req.account_id,
            created_at: new Date(),
            created_by: req.uid,
            created_proxied_by: req.ouid,
            contact: { connect: { id: resultData.id } },
            parent: { connect: { id: parent_relationship.parent.id } },
          },
        });
      }
      // TODO: How to handle multiple parent groups?
      const firstExistingGroupId = parentGroups
        .flat()
        .find((obj) => obj.id)?.id;

      if (firstExistingGroupId) {
        await prisma[TABLE].update({
          where: { id: resultData.id },
          data: {
            contact_group_id: firstExistingGroupId,
            updated_by: req.uid,
            updated_at: new Date(),
          },
        });
      }
    }

    await updateContactCompProfiles(
      resultData,
      contacts_agent_commission_schedule_profiles,
      req
    );

    await updateContactCompProfiles(
      resultData,
      contacts_agent_commission_schedule_profiles_sets,
      req,
      'sets'
    );

    res.status(201).json({ status: 'OK', resultData });
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error('Error creating contact', error);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const patchData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  contactService: IContactService,
  contactHierarchyService: IContactHierarchyService,
  contactGroupService: IContactGroupService,
  contactLevelService: IContactLevelService
) => {
  const body = req.body as Contact;

  try {
    if (!body.id) throw new Error('Missing id');

    // Agent group rules
    // The group can only be set for a root agent
    // If the group is set, also set the group for all downline agents
    // When a contact's upline is updated, update it and all of its children to be the same group (unset if none)
    const {
      agent_level: _agent_level,
      child_relationships: _child_relationships,
      contact_group_id,
      contact_group: _contact_group,
      contact_level,
      agency_contact_levels,
      contact_memos,
      contact_referrals,
      contacts_agent_commission_schedule_profiles_sets,
      contacts_agent_commission_schedule_profiles,
      id: contact_id,
      parent_relationships,
      report_contacts: _report_contacts,
      saved_reports: _saved_reports,
      user_contact: _user_contacts,
      user_str_id: _user_str_id,
      config,
      balance: _balance,
      // The account_role_settings_id is already set in ...rest
      // So we must exclude this, update can't take both relation and foreign key at the same time
      account_role_settings: _,
      account_role_settings_id,
      customer: _customer,
      customer_id,
      ...rest
    } = body;

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    [contact_memos, contact_referrals].forEach((data) => {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (data ?? []).forEach((datum) => {
        if (typeof datum?.id === 'string') delete datum.id;
        datum.start_date = datum.start_date
          ? dayjs(datum.start_date).toDate()
          : null;
        datum.end_date = datum.end_date ? dayjs(datum.end_date).toDate() : null;
      });
    });

    const where = { id: body.id, account_id: req.account_id };

    const data = {
      ...rest,
      updated_at: new Date(),
      updated_by: req.uid,
      updated_proxied_by: req.ouid,
      account_role_settings: account_role_settings_id
        ? { connect: { id: account_role_settings_id } }
        : { disconnect: true },
      customer: customer_id
        ? { connect: { id: customer_id } }
        : { disconnect: true },
    };

    if (contact_group_id !== undefined) {
      await contactGroupService.updateContactGroup({
        contactId: contact_id,
        contactGroupId: contact_group_id,
        accountId: req.account_id,
        userId: req.uid,
        userProxyId: req.ouid,
        contactService,
        contactHierarchyService,
      });
    }

    if (Array.isArray(contact_level)) {
      await contactLevelService.updateContactLevels({
        contactId: contact_id,
        accountId: req.account_id,
        userId: req.uid,
        userProxyId: req.ouid,
        contactLevels: contact_level,
        config: config || {},
      });
    }

    if (Array.isArray(agency_contact_levels)) {
      await contactLevelService.updateAgencyContactLevels({
        agencyContactId: contact_id,
        accountId: req.account_id,
        userId: req.uid,
        userProxyId: req.ouid,
        agencyContactLevels: agency_contact_levels,
        config: config || {},
      });
    }

    if (
      parent_relationships &&
      Array.isArray(parent_relationships) &&
      parent_relationships.length > 0
    ) {
      await contactHierarchyService.updateParentRelationships({
        contactId: contact_id,
        parentRelationships: parent_relationships,
        accountId: req.account_id,
        userId: req.uid,
        userProxyId: req.ouid,
        contactGroupService,
      });
    }

    await container.get<SyncFieldService>(SyncFieldService).canUpdateIfChanged({
      newData: data,
      tableName: TABLE,
      account: { account_id: req.account_id } as ExtAccountInfo,
      id: where.id,
      config: config || {},
    });

    const resultData = await prisma[TABLE].update({
      where,
      data,
    });

    const existingMemos = await prisma.contact_memos.findMany({
      where: {
        contact_id: resultData.id,
        account_id: req.account_id,
        state: 'active',
      },
    });
    const deletedMemos = existingMemos.filter(
      (memo) => !contact_memos.some((m) => m.id === memo.id)
    );
    await prisma.contact_memos.updateMany({
      where: {
        id: { in: deletedMemos.map((memo) => memo.id) },
        account_id: req.account_id,
      },
      data: {
        state: 'deleted',
        updated_at: new Date(),
        updated_by: req.uid,
        updated_proxied_by: req.ouid,
      },
    });
    for await (const memo of contact_memos) {
      const { id, ..._memo } = memo;
      await prisma.contact_memos.upsert({
        where: { id: id ?? -1, account_id: req.account_id },
        update: {
          ..._memo,
          updated_at: new Date(),
          updated_by: req.uid,
          updated_proxied_by: req.ouid,
        },
        create: {
          ..._memo,
          account_id: req.account_id,
          created_by: req.uid,
          created_proxied_by: req.ouid,
          contact: { connect: { id: resultData.id } },
        },
      });
    }

    const existingReferrals = await prisma.contact_referrals.findMany({
      where: {
        contact_id: resultData.id,
        account_id: req.account_id,
        state: 'active',
      },
    });
    const deletedReferrals = existingReferrals.filter(
      (referral) => !contact_referrals.some((r) => r.id === referral.id)
    );
    await prisma.contact_referrals.updateMany({
      where: {
        id: { in: deletedReferrals.map((referral) => referral.id) },
        account_id: req.account_id,
      },
      data: {
        state: 'deleted',
        updated_at: new Date(),
        updated_by: req.uid,
        updated_proxied_by: req.ouid,
      },
    });
    for await (const referral of contact_referrals) {
      const {
        id,
        payer_contact_id,
        referrer_contact_id,
        contact_id: _contact_id,
        ..._referral
      } = referral;

      await prisma.contact_referrals.upsert({
        where: { id: id ?? -1, account_id: req.account_id },
        update: {
          ..._referral,
          updated_at: new Date(),
          updated_by: req.uid,
          updated_proxied_by: req.ouid,

          end_date: _referral.end_date ? new Date(_referral.end_date) : null,
          start_date: _referral.start_date
            ? new Date(_referral.start_date)
            : null,
          payer: payer_contact_id
            ? { connect: { id: +payer_contact_id } }
            : { disconnect: true },
          referrer: referrer_contact_id
            ? { connect: { id: +referrer_contact_id } }
            : { disconnect: true },
        },
        create: {
          ..._referral,
          account_id: req.account_id,
          contact: { connect: { id: resultData.id } },
          created_by: req.uid,
          created_proxied_by: req.ouid,

          end_date: _referral.end_date ? new Date(_referral.end_date) : null,
          start_date: _referral.start_date
            ? new Date(_referral.start_date)
            : null,
          payer: payer_contact_id
            ? { connect: { id: +payer_contact_id } }
            : undefined,
          referrer: referrer_contact_id
            ? { connect: { id: +referrer_contact_id } }
            : undefined,
        },
      });
    }

    await updateContactCompProfiles(
      resultData,
      contacts_agent_commission_schedule_profiles,
      req
    );

    await updateContactCompProfiles(
      resultData,
      contacts_agent_commission_schedule_profiles_sets,
      req,
      'sets'
    );

    res.status(200).json(resultData);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error('Error updating contact', error);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const deleteData = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const { ids, strIds: _strIds } = req.body;
  try {
    if (!Array.isArray(ids) || ids.length === 0) throw new Error('Missing ids');
    // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
      throw new Error('Invalid id(s)');
    const transaction = await prisma.$transaction([
      prisma.contacts.updateMany({
        where: {
          id: { in: ids },
          account_id: String(req.account_id),
        },
        data: {
          state: 'deleted',
          updated_at: new Date(),
          updated_by: req.uid,
          updated_proxied_by: req.ouid,
        },
      }),
      prisma.contact_hierarchy.updateMany({
        where: {
          contact_id: { in: ids },
          account_id: req.account_id,
        },
        data: {
          state: 'deleted',
          updated_at: new Date(),
          updated_by: req.uid,
          updated_proxied_by: req.ouid,
        },
      }),
    ]);
    res.status(200).json({ status: 'OK', data: transaction[0].count });
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error deleting data: ${error}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};
