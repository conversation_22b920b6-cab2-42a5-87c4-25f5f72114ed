import type { NextApiRequest, NextApiResponse } from 'next';
import { create<PERSON><PERSON><PERSON>, Get, Req, Res } from 'next-api-decorators';

import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import { ContactsValidator } from '@/pages/api/contacts/validator';
import { container } from '@/ioc';
import { ContactService } from '@/services/contact';

class ContactsHandler extends BaseHandler {
  private validator: ContactsValidator;
  private contactService: ContactService;

  constructor() {
    super();
    this.validator = container.get(ContactsValidator);
    this.contactService = container.get<ContactService>(ContactService);
  }

  @Get()
  async getCommissionsContactsData(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const params = this.validator.validateCommissionContactsParams(req.query);
    const { date_start, date_end, date_type } = params;

    const response = await this.contactService.getContactsByCommissionDates(
      date_start,
      date_end,
      date_type
    );

    res.json(response);
  }
}
export default withAuth(createHandler(ContactsHandler));
