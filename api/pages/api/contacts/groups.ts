import * as Sentry from '@sentry/nextjs';
import { nanoid } from 'nanoid';
import {
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
  createHandler,
} from 'next-api-decorators';
import type { NextApiRequest, NextApiResponse } from 'next';

import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { BaseHandler } from '@/lib/baseHandler';
import { ContactService } from '@/services/contact';
import { container } from '@/ioc';
import { CrudAction, EntityType } from '@/services/permission/interface';
import { Guard } from '@/services/permission/decorator';
import { limitConcurrency } from '@/lib/helpers';

class ContactsGroupsHandler extends BaseHandler {
  private contactService: ContactService;

  constructor() {
    super();
    this.contactService = container.get<ContactService>(ContactService);
  }

  @Get()
  @Guard(CrudAction.READ, EntityType.AGENTS_GROUPS)
  async getContactsGroups(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return getContactsGroupsData(req, res);
  }

  @Post()
  @Guard(CrudAction.CREATE, EntityType.AGENTS_GROUPS)
  async postContactsGroups(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return postData(req, res, this.contactService);
  }

  @Patch()
  @Guard(CrudAction.UPDATE, EntityType.AGENTS_GROUPS)
  async patchContactsGroups(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return patchData(req, res, this.contactService);
  }

  @Delete()
  @Guard(CrudAction.DELETE, EntityType.AGENTS_GROUPS)
  async deleteContactsGroups(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return deleteData(req, res);
  }
}

export default withAuth(createHandler(ContactsGroupsHandler));

const TABLE = 'contact_groups';

interface ContactGroup {
  id: number;
  str_id: string;
  account_id: string;
  uid: string;
  state: string;
  created_at: Date;
  created_by: string;
  updated_at: Date;
  updated_by: string;

  name: string;
  notes: string;

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  contacts?: any[];
}

export const _getContactsGroupsData = async (req: ExtNextApiRequest) => {
  const where = {
    account_id: req.account_id,
    state: 'active',
    str_id: undefined,
  };
  if (req.query?.id) {
    where.str_id = req.query.id;
  }
  const data = await prisma[TABLE].findMany({
    where,
    include: {
      contacts: true,
    },
  });

  return data;
};

const getContactsGroupsData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  try {
    const data = await _getContactsGroupsData(req);

    res.json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error('Error: ', error);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const postData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  contactService
) => {
  const body = req.body as ContactGroup;
  try {
    const { id: _id, contacts, ...rest } = body;
    const data = await prisma[TABLE].create({
      data: {
        ...rest,
        str_id: nanoid(),
        account_id: req.account_id,
        uid: req.uid,
        created_by: req.uid,
        created_proxied_by: req.ouid,
      },
    });
    const contactGroupId = data.id;
    if (contacts) {
      for (const contactId of contacts) {
        const contactsToUpdate =
          await contactService.getChildrenContactIdListByDepth(
            contactId,
            9,
            req.account_id
          );

        await limitConcurrency(async (id) => {
          return await prisma.contacts.update({
            where: { account_id: req.account_id, id: id },
            data: {
              contact_group_id: contactGroupId,
              updated_by: req.uid,
              updated_at: new Date(),
            },
          });
        }, contactsToUpdate);
      }
    }
    res.status(201).json({ status: 'OK', data });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const patchData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  contactService
) => {
  const body = req.body as ContactGroup;
  try {
    if (!body.id) throw new Error('Missing id');
    const { contacts: _contacts, ...rest } = body;
    const data = await prisma[TABLE].update({
      where: {
        id: Number(body.id),
        account_id: req.account_id,
      },
      data: {
        ...rest,
        id: Number(rest.id),
        updated_at: new Date(),
        updated_by: req.uid,
      },
    });

    if (_contacts) {
      await prisma.contacts.updateMany({
        where: {
          account_id: req.account_id,
          contact_group_id: Number(body.id),
        },
        data: {
          contact_group_id: null,
        },
      });
      for (const contactId of _contacts) {
        const contactsToUpdate =
          await contactService.getChildrenContactIdListByDepth(
            contactId,
            9,
            req.account_id
          );
        await limitConcurrency(async (id) => {
          return await prisma.contacts.update({
            where: { account_id: req.account_id, id: id },
            data: {
              contact_group_id: Number(body.id),
              updated_by: req.uid,
              updated_at: new Date(),
            },
          });
        }, contactsToUpdate);
      }
    }
    res.status(200).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error patching contact groups: ${error.message}`);
    Sentry.captureException(error.error);
    res.status(500).json({ error: error.message });
  }
};

const deleteData = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const { ids, strIds: _strIds } = req.body;
  try {
    if (!Array.isArray(ids) || ids.length === 0) throw new Error('Missing ids');
    // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
      throw new Error('Invalid id(s)');
    // TODO: create contact groups service and migrate this logic there
    const data = await prisma[TABLE].updateMany({
      where: {
        id: {
          in: ids,
        },
        account_id: String(req.account_id),
      },
      data: {
        state: 'deleted',
        updated_at: new Date(),
        updated_by: req.uid,
      },
    });

    // Remove relation to contacts
    await prisma.contacts.updateMany({
      where: {
        contact_group_id: {
          in: ids,
        },
      },
      data: {
        contact_group_id: null,
      },
    });
    res.status(200).json({ status: 'OK', data: data.count });
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error deleting data: ${error}`);
    res.status(500).json({ error: error.message });
  }
};
