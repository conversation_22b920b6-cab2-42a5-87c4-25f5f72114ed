import { CompReportViewTypes } from 'common/globalTypes';
import { startCase } from 'lodash-es';
import type { NextApiRequest, NextApiResponse } from 'next';
import { createHandler, Get, Req, Res } from 'next-api-decorators';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { Timezone } from '@/lib/decorators';
import { processContacts } from '@/lib/helpers/processContacts';
import { withAuth } from '@/lib/middlewares';
import { getExportData } from '@/pages/api/export/base';
import { formatPdfCurrency } from '@/services/export-report/format-currency';
import { SettingsService } from '@/services/settings';
import {
  type ExtNextApiRequest,
  type ExtNextApiResponse,
  Roles,
} from '@/types';
import { getData } from '../saved_reports/reports';
import { exportCsvResponse } from './base/export-csv-response';
import { exportTotalRows } from './exportFields';
import { processReportDataToFormattedData } from './report_data';

class ExportSavedReportHandler extends BaseHandler {
  private settingsService: SettingsService;
  constructor() {
    super();
    this.settingsService = container.get(SettingsService);
  }

  @Get()
  async exportHandler(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @Timezone() timezone: string
  ) {
    const { q, isExportRelationship } = req.query;
    const query = JSON.parse(req.query?.exportOptions);
    const viewType = query?.view;

    const reportData = await getData(req, this.settingsService, {
      ...query,
      report_id: q,
    });

    let roleToUse = Roles.PRODUCER;
    if (viewType === CompReportViewTypes.ADMIN_VIEW)
      roleToUse = Roles.ACCOUNT_ADMIN;

    let table: 'report_data' | 'statement_data' | 'reconciliation_data';
    if (reportData.page === 'policies') {
      table = 'report_data';
    } else if (reportData.page === 'commissions') {
      table = 'statement_data';
      await processContacts(reportData.snapshot_data.data.data);
    } else if (reportData.page === 'reconciliation') {
      table = 'reconciliation_data';
    }

    const templateType = reportData.saved_report_group?.template;
    let formatData = [];

    if (table === 'report_data') {
      formatData = await processReportDataToFormattedData({
        data: reportData.snapshot_data.data.data,
        req,
        res,
        timezone,
        producerView: viewType === CompReportViewTypes.PRODUCER_VIEW,
      });
    } else {
      formatData = await getExportData({
        data: reportData.snapshot_data.data.data,
        table,
        roleId: roleToUse.toString(),
        accountId: req.account_id,
        isExportRelationship: isExportRelationship === 'true',
        templateType,
        uid: req.uid,
        contactStrId:
          viewType === CompReportViewTypes.PRODUCER_VIEW
            ? reportData.snapshot_data.data.contactStrId
            : undefined,
      });
    }

    formatData.push(await exportTotalRows(formatData));

    formatData = formatPdfCurrency(formatData);

    // Set the response headers to trigger the download
    const subFilename = startCase(
      (templateType ?? reportData.page).replace(/_/g, ' ')
    ).replace(/ /g, '-');
    const filename = `Fintary-${subFilename}-Report-Export.csv`;
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    // Converting the data to CSV
    exportCsvResponse(formatData, res);
  }
}

export default withAuth(createHandler(ExportSavedReportHandler));
