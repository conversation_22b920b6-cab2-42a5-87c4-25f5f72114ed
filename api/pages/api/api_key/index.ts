import type { NextApiRequest, NextApiResponse } from 'next';
import * as Sentry from '@sentry/nextjs';
import {
  createHandler,
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
} from 'next-api-decorators';

import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { container } from '@/ioc';
import { APIKeyService } from '@/services/api_key/service';

class Handler extends BaseHandler {
  private service: APIKeyService;
  constructor() {
    super();
    this.service = container.get(APIKeyService);
  }

  @Get()
  async get(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      const params = {
        account_id: req.account_id,
        id: req.query?.id,
      };
      const result = await this.service.getAPIKeys(params);
      res.json(result);
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`Error getting API keys: ${error}`);
      Sentry.captureException(error);
      res.status(500).json({ error: error.message });
    }
  }

  @Post()
  async create(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      const result = await this.service.createAPIKey({
        ...req.body,
        uid: req.uid,
        account_id: req.account_id,
        created_by: req.uid,
        created_proxied_by: req.ouid,
      });
      res.status(201).json({ result });
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`Error creating API key: ${error}`);
      Sentry.captureException(error);
      res.status(500).json({ error: error.message });
    }
  }

  @Patch()
  async update(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      const result = await this.service.updateAPIKey({
        ...req.body,
        account_id: req.account_id,
        updated_by: req.uid,
        updated_proxied_by: req.ouid,
      });
      res.status(200).json(result);
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`Error updating API key: ${error}`);
      Sentry.captureException(error);
      res.status(500).json({ error: error.message });
    }
  }

  @Delete()
  async delete(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      await this.service.deleteAPIKey({
        id: req.body.ids[0],
        account_id: req.account_id,
        updated_by: req.uid,
        updated_proxied_by: req.ouid,
      });
      res.status(200).json({ status: 'OK' });
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`Error deleting API key: ${error}`);
      Sentry.captureException(error);
      res.status(500).json({ error: error.message });
    }
  }
}

export default withAuth(createHandler(Handler));
