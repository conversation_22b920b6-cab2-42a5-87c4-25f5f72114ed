import * as Sentry from '@sentry/nextjs';
import { nanoid } from 'nanoid';
import type { processors as Processors } from '@prisma/client';
import { ProcessorReviewStatuses, ImportStatuses } from 'common/globalTypes';

import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import {
  type ExtNextApiRequest,
  type ExtNextApiResponse,
  DataStates,
} from '@/types';
import { container } from '@/ioc';
import { EmailerService } from '@/services/emailer';
import { limitConcurrency } from '@/lib/helpers';
import { ProcessorsService } from '@/services/processors/service';
import type { SettingsService } from '@/services/settings';
import type { ContactsService } from '@/services/contacts';
import type { UserService } from '@/services/user';
import type { CompaniesService } from '@/services/companies';

const emailerService = container.get<EmailerService>(EmailerService);
const processorsService = container.get<ProcessorsService>(ProcessorsService);

const handler = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  switch (req.method) {
    case 'GET':
      await queryMany(req, res);
      break;
    case 'POST':
      await createOne(req, res);
      break;
    case 'PATCH':
      await patchOne(req, res);
      break;
    case 'DELETE':
      await deleteMany(req, res);
      break;
    default:
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
};

export const _getAdminProcessorsData = async (
  req: ExtNextApiRequest,
  _res: ExtNextApiResponse,
  _settingsService: SettingsService,
  _contactsService: ContactsService,
  _userService: UserService,
  _companiesService: CompaniesService,
  _queryNestedContacts: (
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    initialObject: any,
    depth: number,
    relationshipType: 'parent' | 'child',
    getParentChildren: boolean
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ) => any
) => {
  return processorsService.fetchProcessorsForAccountId({
    accountId: req.account_id,
    userId: req.uid,
    isAdmin: true,
    query: req.query,
  });
};

const queryMany = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const data = await _getAdminProcessorsData(
    req,
    null,
    null,
    null,
    null,
    null,
    null
  );
  res.json(data);
};

const createOne = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const body = req.body as Processors & {
    company_ids?: string[];
  };
  try {
    const {
      company_id: _company_id,
      reviewer_str_id,
      extractionsid,
      company_ids,
      ...rest
    } = body;

    const poster = {
      ...rest,
      str_id: nanoid(),
      extractions: {
        connect: extractionsid ? { id: extractionsid } : undefined,
      },
      users_processors_reviewed_byTousers: {
        connect: reviewer_str_id ? { uid: reviewer_str_id } : undefined,
      },
      reviewed_by:
        body.status === ProcessorReviewStatuses.Approved ? req.uid : null,
      users_processors_created_byTousers: { connect: { uid: req.uid } },
      account: {
        connect: { str_id: req.account_id },
      },
      uid: req.uid,
      created_proxied_by: req.ouid,
    };

    const data = await prisma.$transaction(async (tx) => {
      const processor = await tx.processors.create({
        data: poster,
      });

      if (company_ids && Array.isArray(company_ids) && company_ids.length > 0) {
        await limitConcurrency(async (companyId) => {
          await tx.companies_processors.create({
            data: {
              account_id: req.account_id,
              company_str_id: companyId,
              processor_str_id: processor.str_id,
              created_at: new Date(),
              created_by: req.uid,
              created_proxied_by: req.ouid,
              updated_at: new Date(),
              updated_by: req.uid,
              updated_proxied_by: req.ouid,
            },
          });
        }, company_ids);
      }

      return processor;
    });

    res.status(201).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error creating processor: ${error}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const patchOne = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  delete req.body.sheetName;
  const body = req.body as Processors & {
    company_ids?: string[];
  };
  try {
    if (!body.id) throw new Error('Missing id');

    const { id, company_ids, company_id: _company_id, ...update } = body;
    // TODO: We need to define how to handle global companies
    // where: { id: Number(body.id), account_id: String(req.account_id) },

    // Temporary solution for a reviewer to edit a processor
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const updateData: Record<string, any> = {
      ...update,
      updated_by: req.uid,
      updated_at: new Date(),
      reviewed_at:
        body.status === ProcessorReviewStatuses.Approved ? new Date() : null,
      reviewed_by:
        body.status === ProcessorReviewStatuses.Approved ? req.uid : null,
      uid: req.uid,
    };

    if (
      update?.type === 'policies-report' ||
      update?.type === 'commissions-report'
    ) {
      updateData.account = {
        connect: { str_id: req.account_id },
      };
    }

    const data = await prisma.processors.update({
      where: { id: Number(id) },
      data: updateData,
    });

    if (company_ids && Array.isArray(company_ids)) {
      const existingRecords = await prisma.companies_processors.findMany({
        where: { processor_str_id: data.str_id },
        select: {
          company_str_id: true,
          import_status: true,
        },
      });

      const importStatusMap = existingRecords.reduce((map, record) => {
        map[record.company_str_id] =
          record.import_status || ImportStatuses.NONE;
        return map;
      }, {});

      await prisma.companies_processors.deleteMany({
        where: { processor_str_id: data.str_id },
      });

      if (company_ids.length > 0) {
        await limitConcurrency(
          async (company_id) => {
            const import_status =
              importStatusMap[company_id] || ImportStatuses.NONE;

            await prisma.companies_processors.create({
              data: {
                processor_str_id: data.str_id,
                company_str_id: company_id,
                account: {
                  connect: { str_id: req.account_id },
                },
                created_at: new Date(),
                created_by: req.uid,
                created_proxied_by: req.ouid,
                updated_at: new Date(),
                updated_by: req.uid,
                updated_proxied_by: req.ouid,
                import_status: import_status,
              },
            });
          },
          company_ids,
          15
        );
      }
    }

    // Check if processor is in review and send email to reviewer
    if (
      data.reviewer_str_id &&
      data.status === ProcessorReviewStatuses.InReview
    ) {
      const reviewerMail = await prisma.users.findFirst({
        where: { uid: data.reviewer_str_id },
        select: { email: true, first_name: true },
      });
      await emailerService.sendEmail(
        reviewerMail.email,
        'Fintary processor ready for review',
        `<div>
          Hi ${reviewerMail.first_name},<br/>
          <p>The document processor <strong>${data.name}</strong> is ready for review.</p>
          <a href="https://app.fintary.com/processors?id=${data.str_id}">Click here to review</a>
        </div>`
      );
    }
    // Check if processor is "needs update" and send email to dev
    if (
      data.reviewer_str_id &&
      data.status === ProcessorReviewStatuses.NeedsUpdate
    ) {
      const devMail = await prisma.users.findFirst({
        where: { uid: data.created_by },
        select: { email: true, first_name: true },
      });
      await emailerService.sendEmail(
        devMail.email,
        'Fintary processor needs update',
        `<div>
          Hi ${devMail.first_name},<br/>
          <p>Your processor <strong>${data.name}</strong> has been reviewed and some changes have been requested.</p>
          <a href="https://app.fintary.com/processors?id=${data.str_id}">Click here to check the processor</a>
          <p>Please take a look as soon as posible.</p>
        </div>`
      );
    }
    res.status(200).json(data);
  } catch (error) {
    req.logger.error(`Error updating processor: ${error}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const deleteMany = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const { ids } = req.body;
  try {
    if (!Array.isArray(ids) || ids.length === 0) throw new Error('Missing ids');
    // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
      throw new Error('Invalid ids');
    const promises = ids.map((_id) => {
      return prisma.processors.update({
        where: { id: Number(_id) },
        data: {
          state: DataStates.DELETED,
          updated_at: new Date(),
          updated_by: req.uid,
          updated_proxied_by: req.ouid,
        },
      });
    });
    await prisma.$transaction(promises);
    res.status(200).json({ status: 'OK' });
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error deleting processor: ${error}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

export default withAuth(handler, { adminOnly: true });
