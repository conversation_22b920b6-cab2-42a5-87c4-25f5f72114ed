import { getOrderBy } from 'common/helpers';
import dayjs from 'dayjs';
import * as Sentry from '@sentry/nextjs';
import { AccessTypes } from 'common/globalTypes';

import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import {
  documentSubTableFields,
  formatDocumentsData,
} from '@/pages/api/documents';
import {
  DataStates,
  type ExtNextApiRequest,
  type ExtNextApiResponse,
} from '@/types';
import { calculateSkipAndTake } from '@/prisma';
import { limitConcurrency } from '@/lib/helpers';

const TABLE = 'documents';

const handler = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  if (req.method === 'GET') {
    const where = {
      account: undefined,
      created_at: undefined,
      profile_str_id: undefined,
      state: undefined,
      status: undefined,
      str_id: undefined,
      type: undefined,
      OR: undefined,
      company_str_id: undefined,
      sync_id: undefined,
    };

    const {
      end_date,
      start_date,
      id,
      limit,
      orderBy = 'created_at',
      page,
      profile,
      q = '',
      sort = 'desc',
      type,
      companies,
      sync_status,
    } = req.query;

    const companyStrIds = (
      Array.isArray(companies) ? companies : [companies]
    ).filter((c) => c);

    if (companies !== undefined) {
      const expandedCompanyIds = [];

      for (const companyId of companyStrIds) {
        const company = await prisma.companies.findUnique({
          where: { str_id: companyId },
          include: {
            child_companies: {
              where: { state: DataStates.ACTIVE },
            },
          },
        });

        if (company) {
          expandedCompanyIds.push(company.str_id);

          if (
            company.access === AccessTypes.GLOBAL &&
            company.child_companies
          ) {
            expandedCompanyIds.push(
              ...company.child_companies.map((child) => child.str_id)
            );
          }
        }
      }

      where.company_str_id = { in: expandedCompanyIds };
    }

    const { skip, take } = calculateSkipAndTake({ page, limit });

    const orderByData = getOrderBy(orderBy, sort, [
      { query_field: 'companies', db_field: 'company_name' },
      { query_field: 'statement_data', db_field: 'commission_amount' },
      { query_field: 'imports', db_field: 'summed_total_amount' },
    ]);

    if (start_date || end_date) {
      where.created_at = {
        ...(start_date && { gte: dayjs.utc(start_date).toDate() }),
        ...(end_date && { lt: dayjs.utc(end_date).toDate() }),
      };
    }

    if (type) {
      where.type = type;
    }

    if (req.query?.status) {
      if (Array.isArray(req.query.status)) {
        where.status = {
          in: req.query.status,
        };
      } else {
        where.status = req.query.status;
      }
    }
    if (sync_status) {
      where.sync_id = sync_status === 'not_synced' ? null : { not: null };
    }
    if (q) {
      where.OR = [
        {
          filename: {
            contains: q,
            mode: 'insensitive',
          },
        },
        {
          file_path: {
            contains: q,
            mode: 'insensitive',
          },
        },
        {
          companies: {
            company_name: {
              contains: q,
              mode: 'insensitive',
            },
          },
        },
        {
          processor: {
            contains: q,
            mode: 'insensitive',
          },
        },
      ];
    }

    if (id || profile) {
      if (id) {
        where.str_id = where.str_id = {
          in: id.split(','),
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        } as any;
      }

      if (profile) {
        where.profile_str_id = profile;
      }
    } else {
      where.state = 'active';
      where.account = {
        isNot: {
          account_user_roles: {
            some: {
              user: {
                email: {
                  endsWith: '@fintary.com',
                },
              },
            },
          },
        },
      };
    }

    const data = await prisma[TABLE].findMany({
      where,
      skip,
      take,
      orderBy: {
        ...orderByData,
      },
      select: {
        id: true,
        str_id: true,
        type: true,
        company_str_id: true,
        status: true,
        profile_str_id: true,
        statement_amount: true,
        bank_total_amount: true,

        created_at: true,
        created_by: true,
        check_date: true,
        deposit_date: true,
        file_hash: true,
        file_path: true,
        file_type: true,
        filename: true,
        import_id: true,
        imported_at: true,
        mapping: true,
        method: true,
        notes: true,
        override_file_path: true,
        override_filename: true,
        override_mapping: true,
        processor: true,
        prompt: true,
        sync_id: true,
        sync_worker: true,
        tag: true,
        statement_month: true,
        payment_method: true,
        validations: true,
        upload_source: true,
        process_method: true,
        processing_task_id: true,

        account: true,
        ...documentSubTableFields,
      },
      accountInject: false,
    });
    const count = await prisma[TABLE].count({ where, accountInject: false });

    const taskIds = data
      .filter((doc) => doc.processing_task_id)
      .map((doc) => doc.processing_task_id);

    let processingMap = new Map<string, { notes?: string }>();

    if (taskIds.length > 0) {
      try {
        const processingTasks = await prisma.data_processing.findMany({
          where: {
            str_id: { in: taskIds },
            state: DataStates.ACTIVE,
          },
          select: {
            str_id: true,
            notes: true,
          },
          accountInject: false,
        });

        processingMap = new Map(
          processingTasks.map((task) => [task.str_id, task])
        );
      } catch (error) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.warn('Failed to fetch processing notes:', error);
        Sentry.captureException(error, {
          extra: { message: 'Failed to fetch processing notes' },
        });
      }
    }

    const dataWithProcessingNotes = data.map((doc) => ({
      ...doc,
      processing_notes: doc.processing_task_id
        ? processingMap.get(doc.processing_task_id)?.notes
        : null,
    }));

    let formatData = formatDocumentsData(dataWithProcessingNotes);
    formatData = await limitConcurrency(async (document) => {
      const imports_count = await prisma.data_imports.count({
        where: {
          document_str_id: document.str_id,
          state: DataStates.ACTIVE,
        },
      });

      return {
        ...document,
        imports_count,
      };
    }, formatData);

    res.json({ data: formatData, count });
  } else {
    throw new Error(
      `The HTTP ${req.method} method is not supported at this route.`
    );
  }
};

export default withAuth(handler, { adminOnly: true, allowNoAccount: true });
