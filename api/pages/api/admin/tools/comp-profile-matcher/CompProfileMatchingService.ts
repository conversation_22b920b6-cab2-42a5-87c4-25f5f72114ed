import { CompProfileMatcherState } from 'common/globalTypes';
import { inject, injectable } from 'inversify';
import keyBy from 'lodash-es/keyBy';

import {
  fetchProfileCompanies,
  getApplicableCompProfiles,
  getContactAncestorsWithCommissionProfiles,
} from '@/lib/commissions';
import { prismaClient as prisma } from '@/lib/prisma';
import {
  CompProfileMatchContext,
  type CompProfileMatchContextObj,
} from '@/pages/api/admin/tools/comp-profile-matcher/CompProfileMatchContext';
import { CommissionRetrieval } from '@/pages/api/data_processing/commissions/commissionRetrieval';
import { DataStates } from '@/types';

@injectable()
export class CompProfileMatchingService {
  @inject(CommissionRetrieval)
  private readonly commissionRetrieval: CommissionRetrieval;

  private async getEffectiveDate(statement: {
    effective_date?: Date;
    report_data_id?: number;
  }): Promise<Date | null> {
    if (statement?.effective_date) return statement.effective_date;

    const report = await prisma.report_data.findUnique({
      where: { id: statement?.report_data_id },
      select: { effective_date: true },
    });

    return report?.effective_date || null;
  }

  async match(
    accountId: string,
    commissionId: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    compProfileMatchers: any[]
  ): Promise<CompProfileMatchContextObj | null> {
    const statement = await prisma.statement_data.findUnique({
      where: { str_id: commissionId, account_id: accountId },
      include: {
        report: true,
      },
    });

    const effectiveDate = await this.getEffectiveDate(statement);

    const {
      companies,
      companyProducts,
      companyProductOptions,
      compGridCriteria,
      compGridProducts,
    } = await this.commissionRetrieval.getAllData({
      account_id: accountId,
      contactIds: null,
      documentIds: null,
      startDate: null,
      endDate: null,
      payingEntity: null,
      policyId: null,
      statementIds: null,
      id: statement?.id,
      ids: null,
      regressionTestMode: null,
      useGroupedCommissions: null,
      regressionAccount: null,
    });

    const lookupData = {
      companies: keyBy(companies, 'id'),
      companyProducts: keyBy(companyProducts, 'id'),
      companyProductOptions: keyBy(companyProductOptions, 'id'),
      compGridCriteria: keyBy(compGridCriteria, 'id'),
      compGridProducts: keyBy(compGridProducts, 'id'),
    };

    const agents = await prisma.contacts.findMany({
      where: {
        str_id: {
          in: compProfileMatchers.map((matcher) => matcher.agentStrId),
        },
        account_id: accountId,
        state: DataStates.ACTIVE,
      },
      select: { str_id: true, id: true },
    });

    const agentMap = keyBy(agents, 'str_id');
    const compProfileMatchContext = CompProfileMatchContext.init();

    for (const matcher of compProfileMatchers) {
      const { agentStrId, compProfileStrId } = matcher;
      const agentId = agentMap[agentStrId]?.id;

      if (!agentId) {
        compProfileMatchContext.addCompProfileMatchContextItem(
          agentStrId,
          compProfileStrId,
          {
            state: CompProfileMatcherState.UNMATCHED,
            message: `Agent ${agentStrId} not found`,
            updateAt: new Date(),
          }
        );
        continue;
      }

      const allAncestryProfiles =
        await getContactAncestorsWithCommissionProfiles(
          agentStrId,
          effectiveDate,
          { account_id: accountId }
        );

      const allProfiles = allAncestryProfiles?.[0]?.commissionProfiles || [];

      const compProfile = allProfiles.find(
        (profile) =>
          profile?.agent_commission_schedule_profile?.str_id ===
          compProfileStrId
      );

      if (!compProfile) {
        compProfileMatchContext.addCompProfileMatchContextItem(
          agentStrId,
          compProfileStrId,
          {
            state: CompProfileMatcherState.UNMATCHED,
            message: `Comp Profile ${compProfileStrId} not found for agent ${agentStrId}`,
            updateAt: new Date(),
          }
        );
        continue;
      }

      if (!compProfile?.agent_commission_schedule_profile.single_carrier_mode) {
        await fetchProfileCompanies([compProfile]);
      }

      await getApplicableCompProfiles(
        {
          agentStrId,
          compProfiles: [compProfile],
          statementContactStrId: agentStrId,
        },
        statement,
        lookupData,
        effectiveDate,
        true,
        {},
        compProfileMatchContext
      );
    }

    return compProfileMatchContext.compProfileMatchContextObj;
  }
}
