import * as Sentry from '@sentry/nextjs';
import { getOrderBy } from 'common/helpers';

import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import { calculateSkipAndTake } from '@/prisma';

const handler = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  switch (req.method) {
    case 'GET':
      await getData(req, res);
      break;
    default:
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
};

export default withAuth(handler, { adminOnly: true });

const TABLE = 'data_processing';

export const getData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  try {
    const where = {
      state: 'active',
      master_str_id: null,
      str_id: undefined,
      companies: undefined,
      type: undefined,
    };

    const {
      page,
      limit,
      qc: processingType,
      orderBy = 'created_at',
      sort = 'desc',
      id,
      incl_tasks,
    } = req.query;

    const { take, skip } = calculateSkipAndTake({ page, limit });

    if (processingType) {
      where.type = processingType;
    }

    if (id) {
      where.str_id = id;
    }
    if (incl_tasks?.toLowerCase() === 'true') {
      where.master_str_id = undefined;
    }

    const orderByData = getOrderBy(orderBy, sort);

    const [data, count] = await Promise.all([
      prisma[TABLE].findMany({
        where,
        take,
        skip,
        orderBy: {
          ...orderByData,
        },
        include: {
          account: true,
        },
      }),
      prisma[TABLE].count({
        where,
      }),
    ]);

    res.json({
      data,
      count,
    });
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error('Error getting activities', error);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};
