import type { NextApiRequest, NextApiResponse } from 'next';
import { create<PERSON><PERSON><PERSON>, Post, Req, Res } from 'next-api-decorators';

import { <PERSON>Hand<PERSON> } from '@/lib/baseHandler';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { withAuth } from '@/lib/middlewares';
import { container } from '@/ioc';
import {
  DataUpdateService,
  type IDataUpdateService,
} from '@/services/data-update';
import { DataUpdateValidator } from '@/pages/api/admin/data-update/validator';

class Handler extends BaseHandler {
  private dataUpdateService: IDataUpdateService;
  private validator: DataUpdateValidator;

  constructor() {
    super();
    this.dataUpdateService =
      container.get<IDataUpdateService>(DataUpdateService);
    this.validator = container.get(DataUpdateValidator);
  }

  @Post()
  async postDataUpdate(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const body = this.validator.validateDataUpdateRunParams(req.body);

    const { dataToUpdate, dataEntity } = body;

    const response = await this.dataUpdateService.updateData(
      req.account_id,
      req.uid,
      req.ouid,
      dataEntity,
      dataToUpdate
    );
    if (!Number.isNaN(response)) {
      res.json({ data: response });
    } else {
      res.status(500).json({ error: 'An error occurred' });
    }
  }
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '25mb',
    },
  },
};

export default withAuth(createHandler(Handler), {
  adminOnly: true,
  allowNoAccount: true,
});
