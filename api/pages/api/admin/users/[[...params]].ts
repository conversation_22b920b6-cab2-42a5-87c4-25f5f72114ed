import * as dto from 'common/dto/admin/users/dto';
import type { NextApiRequest, NextApiResponse } from 'next';
import {
  Delete,
  Get,
  Post,
  Req,
  Res,
  createHandler,
} from 'next-api-decorators';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { Pagination, ZodQuery } from '@/lib/decorators';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import { UserAuthService } from '@/services/user/auth';
import {
  DataStates,
  type ExtNextApiRequest,
  type ExtNextApiResponse,
  userAuthActions,
} from '@/types';

const TABLE = 'users';
const FIELDS_TO_SEARCH = ['uid', 'email', 'first_name', 'last_name'];

class Handler extends BaseHandler {
  private userAuthService: UserAuthService;

  constructor() {
    super();
    this.userAuthService = container.get<UserAuthService>(UserAuthService);
  }

  @Get()
  @Pagination()
  async getAccount(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let where: any = { state: 'active', str_id: undefined };
    if (req.query?.id) {
      where.str_id = req.query.id;
    }
    const q = req.query?.q;
    if (q) {
      where = this.whereClauseBuilder(FIELDS_TO_SEARCH, q, where);
    }

    const [_data, meta] = await prisma[TABLE]
      .paginate({
        where: where,
        include: {
          account_user_roles: {
            where: {
              state: 'active',
            },
            include: { account: true, role: true },
          },
        },
      })
      .withPages({
        limit: req.limit,
        page: req.page + 1,
        includePageCount: true,
      });

    res.json({ data: _data, count: meta.totalCount });
  }

  @Delete()
  async softDeleteUser(
    // biome-ignore format: compound decorator
    @(ZodQuery(dto.DeleteAdminUsersDTOSchema)())
    validBody: dto.DeleteAdminUsersDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const { id } = validBody;
    await prisma[TABLE].update({
      where: { id },
      data: {
        state: DataStates.DELETED,
        updated_at: new Date(),
        updated_by: req.uid,
        updated_proxied_by: req.ouid,
      },
    });
    res.status(200).json({ status: 'OK' });
  }

  @Post('/verify-user-account')
  async postVerifyUser(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const { userUid } = req.body;
    if (!userUid) {
      throw new Error('User uid is required');
    }
    const userUpdate = await this.userAuthService.updateUserAuthByUid(
      userUid,
      userAuthActions.VERIFY_EMAIL
    );
    if (userUpdate) {
      res.json({ data: userUpdate });
    } else {
      res.status(500).json({ error: userUpdate });
    }
  }

  @Post('/reset-user-password')
  async postResetUserPassword(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const { userUid, newPassword } = req.body;
    if (!userUid) {
      throw new Error('User uid is required');
    }
    if (!newPassword) {
      throw new Error('Password is required');
    }
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.log(
      `Reset user password requested for user ${userUid} by ${req.uid} proxied by ${req.ouid}`
    );
    const userUpdate = await this.userAuthService.updateUserAuthByUid(
      userUid,
      userAuthActions.RESET_PASSWORD,
      newPassword
    );
    if (userUpdate) {
      res.json({ data: userUpdate });
    } else {
      res.status(500).json({ error: userUpdate });
    }
  }
}

export default withAuth(createHandler(Handler), {
  adminOnly: true,
  allowNoAccount: true,
});
