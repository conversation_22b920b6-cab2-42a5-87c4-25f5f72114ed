import type { J<PERSON>Value } from '@prisma/client/runtime/library';
import { create<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'next-api-decorators';
import type { NextApiRequest, NextApiResponse } from 'next';
import { DEFAULT_PAGE_SETTING_FIELDS } from 'common/constants/default_fields';

import prisma from '@/lib/prisma';
import { withAuth } from '@/lib/middlewares';
import {
  DataStates,
  type ExtNextApiRequest,
  type ExtNextApiResponse,
  Roles,
} from '@/types';
import { arrayToObjByKey, consolidateGroupedRecords } from '@/lib/helpers';
import { BaseHandler } from '@/lib/baseHandler';
import {
  type ISavedReportsService,
  SavedReportsService,
} from '@/services/saved-reports';
import { container } from '@/ioc';
import { filterContactFieldsByContactType } from '@/lib/helpers/filterContactFieldsByContactType';
import { SettingsService } from '@/services/settings';

interface DataWithOwner {
  name: JsonValue;
  created_at: Date;
  notes: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  snapshot_data: any;
  id: number;
  access: string;
  users_white_list: string[];
  created_by: string;
  isOwner?: boolean;
  reviewed_by?: string;
  status?: string;
  page?: string;
  saved_report_group: {
    template: string;
  };
}

interface Fields {
  fields: string[];
}

interface PageSettings {
  commissions?: Fields;
  policies?: Fields;
  reconciliation?: Fields;
}

interface AccountSettings {
  pages_settings?: PageSettings;
}

class Handler extends BaseHandler {
  private savedReportsService: SavedReportsService;
  private settingsService: SettingsService;

  constructor() {
    super();
    this.savedReportsService =
      container.get<SavedReportsService>(SavedReportsService);
    this.settingsService = container.get<SettingsService>(SettingsService);
  }

  @Get()
  async reportsDataGet(@Req() req: ExtNextApiRequest & NextApiRequest) {
    return await getData(req, this.settingsService);
  }

  @Patch()
  async reportsDataPatch(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await patchData(req, res, this.savedReportsService);
  }
}

export default withAuth(createHandler(Handler));

const processHeadersBySettings = (data, fields) => {
  // Get headers from pages_settings
  const orderedHeadersFromPageSettings = fields.map((item) => ({ id: item }));
  data.snapshot_data.headers = orderedHeadersFromPageSettings;

  data.snapshot_data.data.data = data.snapshot_data.data.data
    .filter((item) => item !== null && item !== undefined)
    .map((item) => {
      const filteredItem: Partial<typeof item> = {};
      if (Object.hasOwn(item, 'id')) {
        filteredItem.id = item.id;
      }

      if (Object.hasOwn(item, 'report')) {
        filteredItem.report = item.report;
      }

      if (fields.length > 0) {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        fields.forEach((field) => {
          if (Object.hasOwn(item, field)) {
            if (field === 'contacts') {
              filteredItem[field] = filterContactFieldsByContactType(
                field,
                item,
                data.snapshot_data
              );
            } else {
              filteredItem[field] = item[field];
            }
          }
        });
      } else {
        // If no fields are provided, assign all item properties to filteredItem
        Object.assign(filteredItem, item);
        filteredItem.contacts = filterContactFieldsByContactType(
          'contacts',
          filteredItem.contacts,
          data.snapshot_data
        );
      }
      return filteredItem;
    });
};

export const getData = async (
  req: ExtNextApiRequest,
  settingsService: SettingsService,
  query?: {
    report_id: string;
    grouping: string;
    view: string;
  }
) => {
  const { report_id, grouping, view } = query || req.query;

  const data: DataWithOwner = await prisma.saved_reports.findFirst({
    where: {
      account_id: req.account_id,
      str_id: report_id,
      state: DataStates.ACTIVE,
    },
    select: {
      str_id: true,
      page: true,
      name: true,
      created_at: true,
      notes: true,
      snapshot_data: true,
      id: true,
      access: true,
      users_white_list: true,
      created_by: true,
      reviewed_by: true,
      status: true,
      saved_report_group: {
        select: {
          template: true,
        },
      },
      users_reports_reviewed_byTousers: {
        select: {
          email: true,
        },
      },
    },
  });

  if (!data) throw new Error('Report not found');

  let roleToUse: number = parseInt(req.role_id);
  let contactStrId: string | undefined;
  if (roleToUse === Roles.ACCOUNT_ADMIN && view === 'prod-view') {
    roleToUse = Roles.PRODUCER;
    contactStrId = data.snapshot_data.data.contactStrId;
  } else if (roleToUse === Roles.ACCOUNT_ADMIN && view === 'admin-view') {
    roleToUse = Roles.ACCOUNT_ADMIN;
  } else roleToUse = Roles.PRODUCER;

  const accountSettings: AccountSettings =
    await settingsService.getSettingsByContact({
      uid: req.uid,
      accountId: req.account_id,
      roleId: roleToUse,
      contactStrId,
    });
  if (data.page) {
    let fields = accountSettings?.pages_settings?.[data.page]?.fields;

    if (!fields || fields.length === 0) {
      fields = DEFAULT_PAGE_SETTING_FIELDS[data.page] ?? [];
    }

    processHeadersBySettings(data, fields);
  }

  if (req.uid === data.created_by) data.isOwner = true;

  if (grouping === 'policyNumber') {
    const groupedRecords = arrayToObjByKey(
      data.snapshot_data.data.data,
      'policy_id',
      true
    );
    const consolidatedData = consolidateGroupedRecords(groupedRecords);

    data.snapshot_data.data.data = consolidatedData;
    data.snapshot_data.count = consolidatedData.length;

    // Recalculate totals
    data.snapshot_data.data.totals = {
      fees: 0,
      commission_amount: 0,
      commission_paid_amount: 0,
      customer_paid_premium_amount: 0,
      commissionable_premium_amount: 0,
    };
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    consolidatedData.forEach((record) => {
      data.snapshot_data.data.totals.fees += record.fees || 0;
      data.snapshot_data.data.totals.commission_amount +=
        record.commission_amount || 0;
      data.snapshot_data.data.totals.commission_paid_amount +=
        record.commission_paid_amount || 0;
      data.snapshot_data.data.totals.customer_paid_premium_amount +=
        record.customer_paid_premium_amount || 0;
      data.snapshot_data.data.totals.commissionable_premium_amount +=
        record.commissionable_premium_amount || 0;
    });
  }

  return data;
};

const patchData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  savedReportsService: ISavedReportsService
) => {
  const { id: reportId, account_id: accountId, data } = req.body;

  const response = await savedReportsService.patchReportData(
    +reportId,
    null,
    accountId,
    data,
    req.uid,
    req.ouid
  );

  if (!response) {
    res.status(404).json({ error: 'Report not found', status: 404 });
    return;
  }
  res.status(200).json({ id: response, status: 200 });
};
