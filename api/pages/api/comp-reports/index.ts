import type { NextApiRequest, NextApiResponse } from 'next';
import { create<PERSON><PERSON><PERSON>, Get, Req, Res } from 'next-api-decorators';
import { GetCompReportSchema } from 'common/dto/comp_reports/dto';
import type { GetCompReportDto } from 'common/dto/comp_reports/dto';
import { CompReportViewTypes } from 'common/globalTypes';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import {
  type ExtNextApiRequest,
  type ExtNextApiResponse,
  Roles,
} from '@/types';
import {
  CompReportsService,
  type ICompReportsService,
} from '@/services/comp-reports';
import { ZodQuery } from '@/lib/decorators';

class Handler extends BaseHandler {
  private savedReportsService: ICompReportsService;

  constructor() {
    super();
    this.savedReportsService =
      container.get<ICompReportsService>(CompReportsService);
  }

  // TODO: Add integration tests for this endpoint
  @Get()
  async reportsDataGet(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    // biome-ignore format: compound decorator
    @(ZodQuery(GetCompReportSchema)())
    query: GetCompReportDto
  ) {
    const { report_str_id, grouping, view, q, page, limit } = query;

    const role_to_use = this.determineRoleToUse(parseInt(req.role_id), view);

    const response = await this.savedReportsService.getCompReportDataByStrId(
      report_str_id,
      req.account_id,
      role_to_use,
      grouping,
      q,
      page,
      limit
    );

    if (!response) {
      return res.status(500).json({
        success: false,
        message: 'Report not found',
      });
    }

    return res.status(200).json({
      success: true,
      data: response,
    });
  }

  private determineRoleToUse(
    userRole: number,
    view?: CompReportViewTypes
  ): number {
    if (
      userRole === Roles.ACCOUNT_ADMIN &&
      view === CompReportViewTypes.PRODUCER_VIEW
    ) {
      return Roles.PRODUCER;
    }

    if (
      userRole === Roles.ACCOUNT_ADMIN &&
      view === CompReportViewTypes.ADMIN_VIEW
    ) {
      return Roles.ACCOUNT_ADMIN;
    }

    return Roles.PRODUCER;
  }
}

export default withAuth(createHandler(Handler));
