import * as Sentry from '@sentry/nextjs';
import { nanoid } from 'nanoid';
import type { NextApiRequest, NextApiResponse } from 'next';
import { createHand<PERSON>, Get, <PERSON>, Req, Res } from 'next-api-decorators';

import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { AccountConfigService } from '@/services/account/config';
import { FirebaseStorageSignedUrlService } from '@/services/firebase-storage/signedUrl';
import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';

/**
 * @swagger
 * /api/accounts:
 *   get:
 *     tags:
 *        - Accounts
 *     description: Get account settings
 *     authorizations:
 *      BearerAuth:[]
 *     responses:
 *       200:
 *         description: Returns account settings
 *       500:
 *         description: Error getting account settings
 *   patch:
 *      tags:
 *        - Accounts
 *      responses:
 *        200:
 *          description: Returns updated account settings
 *        500:
 *          description: Error updating account settings
 */

class Handler extends BaseHandler {
  // biome-ignore lint/complexity/noUselessConstructor: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  constructor() {
    super();
  }

  @Get()
  async get(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    await getSettings(req, res);
  }

  @Patch()
  async patch(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    await patchSettings(req, res);
  }
}

export default withAuth(createHandler(Handler));

const accountConfigService =
  container.get<AccountConfigService>(AccountConfigService);
const firebaseStorageSignedUrlService =
  container.get<FirebaseStorageSignedUrlService>(
    FirebaseStorageSignedUrlService
  );

const getSettings = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  try {
    const { accId } = req.query;

    const accountId = accId ?? req.account_id;

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const accountData: any = await prisma.accounts.findUnique({
      where: { str_id: accountId },
    });
    // TODO: Come up with a better way of handling these composite fields
    // Break down the report_dedupe object into two fields for FE
    // Reset the field itself last since others read from it
    accountData.report_de_dupe_unique_empty =
      accountData.report_de_dupe?.unique_empty ?? [];
    accountData.report_de_dupe = accountData.report_de_dupe?.fields ?? [];

    accountData.report_grouping_unique_empty =
      accountData.report_grouping?.unique_empty ?? [];
    accountData.report_grouping_priority_field =
      accountData.report_grouping?.priority_field;
    accountData.report_grouping = accountData.report_grouping?.fields ?? [];

    accountData.report_grouping_unique_empty2 =
      accountData.report_grouping2?.unique_empty ?? [];
    accountData.report_grouping_priority_field2 =
      accountData.report_grouping2?.priority_field;
    accountData.report_grouping2 = accountData.report_grouping2?.fields ?? [];

    accountData.statement_de_dupe_unique_empty =
      accountData.statement_de_dupe?.unique_empty ?? [];
    accountData.statement_de_dupe = accountData.statement_de_dupe?.fields ?? [];

    accountData.statement_grouping_unique_empty =
      accountData.statement_grouping?.unique_empty ?? [];
    accountData.statement_grouping_diff_fields =
      accountData.statement_grouping?.diff_fields ?? [];
    accountData.statement_grouping_priority_field =
      accountData.statement_grouping?.priority_field;
    accountData.statement_grouping =
      accountData.statement_grouping?.fields ?? [];

    const accountConfigData =
      await accountConfigService.getAccountConfig(accountId);

    const perAgentPayoutStatusConfig = accountConfigData.find(
      (config) => config.type === 'per_agent_payout_status'
    );

    if (
      perAgentPayoutStatusConfig &&
      typeof perAgentPayoutStatusConfig.value === 'object' &&
      perAgentPayoutStatusConfig.value !== null
    ) {
      accountData.per_agent_payout_status =
        (
          // biome-ignore format: biome likes the comments to be outside the parens, but then won't find the ignore comment because it's moved
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          perAgentPayoutStatusConfig.value as { per_agent_payout_status: any }
        ).per_agent_payout_status;
    } else {
      accountData.per_agent_payout_status = null;
    }

    const { url, exists } = await firebaseStorageSignedUrlService.getSignedUrl(
      {
        expires: Date.now() + 1000 * 60 * 60 * 8,
      },
      accountData.logo_url,
      req,
      true
    );
    accountData.storage_logo_url = url;
    accountData.exists_in_storage = exists;
    res.status(200).json(accountData);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`An error occurred ${error}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error });
  }
};

const patchSettings = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const body = req.body;
  try {
    if (!body.id) throw new Error(`id is required`);
    if (!body.short_name) throw new Error(`Account identifier is required`);
    try {
      await validateShortname(body.short_name, req.account_id);
    } catch (validationError) {
      return res.status(400).json({ error: validationError.message });
    }

    const accountStrId = req.account_id;
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    delete body['created_by'];
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    delete body['exists_in_storage'];
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    delete body['storage_logo_url'];

    const per_agent_payout_status = body.per_agent_payout_status;

    accountConfigService.setAccountConfig(
      accountStrId,
      req.uid,
      { per_agent_payout_status: per_agent_payout_status },
      'per_agent_payout_status',
      null
    );
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    delete body['per_agent_payout_status'];

    // TODO: Come up with a better way of handling these composite fields
    // Update dedupe/grouping configs to be object
    body.report_de_dupe = {
      fields: body.report_de_dupe,
      unique_empty: body.report_de_dupe_unique_empty,
    };
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    delete body['report_de_dupe_unique_empty'];

    body.report_grouping = {
      fields: body.report_grouping,
      unique_empty: body.report_grouping_unique_empty,
      priority_field: body.report_grouping_priority_field,
    };
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    delete body['report_grouping_unique_empty'];
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    delete body['report_grouping_priority_field'];

    body.report_grouping2 = {
      fields: body.report_grouping2,
      unique_empty: body.report_grouping_unique_empty2,
      priority_field: body.report_grouping_priority_field2,
    };
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    delete body['report_grouping_unique_empty2'];
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    delete body['report_grouping_priority_field2'];

    body.statement_de_dupe = {
      fields: body.statement_de_dupe,
      unique_empty: body.statement_de_dupe_unique_empty,
    };
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    delete body['statement_de_dupe_unique_empty'];

    body.statement_grouping = {
      fields: body.statement_grouping,
      diff_fields: body.statement_grouping_diff_fields,
      unique_empty: body.statement_grouping_unique_empty,
      priority_field: body.statement_grouping_priority_field,
    };
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    delete body['statement_grouping_diff_fields'];
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    delete body['statement_grouping_unique_empty'];
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    delete body['statement_grouping_priority_field'];

    if (body.logo_url && !body.logo_url.includes('accountLogos/')) {
      body.logo_url = `accountLogos/${accountStrId}/${nanoid()}-${body.logo_url}`;
    }

    const data = await prisma.accounts.update({
      where: { str_id: accountStrId },
      data: {
        ...body,
        state: 'active',
        updated_by: req.uid,
        updated_proxied_by: req.ouid,
        updated_at: new Date(),
      },
    });

    const { url, exists } = await firebaseStorageSignedUrlService.getSignedUrl(
      {
        expires: Date.now() + 1000 * 60 * 60 * 8,
      },
      data.logo_url,
      req,
      true
    );
    data.storage_logo_url = url;
    data.exists_in_storage = exists;
    res.status(200).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`An error occurred ${error}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error?.message || 'Error update settings' });
  }
};

export const validateShortname = async (
  shortname: string,
  accountId: string
) => {
  const isValid = /^[a-z0-9-]{3,20}$/.test(shortname);
  if (!isValid) {
    throw new Error(
      'Account identifier may only use lowercase letters, numbers, dashes, and be between 3 and 20 characters long'
    );
  }

  const isExists = await checkExistingShortName(accountId, shortname);
  if (isExists) {
    throw new Error('Account identifier already exists');
  }
};

const checkExistingShortName = async (accountId: string, shortName: string) => {
  const existingShortName = await prisma.accounts.findFirst({
    where: {
      short_name: shortName,
      str_id: { not: accountId },
    },
  });

  return Boolean(existingShortName);
};
