import { create<PERSON><PERSON><PERSON>, Get, Req, Res } from 'next-api-decorators';
import type { NextApiRequest, NextApiResponse } from 'next';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { withAuth } from '@/lib/middlewares';
import { SettingsService } from '@/services/settings';

class Handler extends BaseHandler {
  private settingsService: SettingsService;

  constructor() {
    super();
    this.settingsService = container.get<SettingsService>(SettingsService);
  }

  @Get()
  async getAgentCommissionsSettings(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() _res: ExtNextApiResponse & NextApiResponse
  ) {
    return await this.settingsService.getAgentCommissionsDownlineDataAccess(
      req.account_id
    );
  }
}

export default withAuth(createHand<PERSON>(Handler));
