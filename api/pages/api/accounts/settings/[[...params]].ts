import { Get, <PERSON>, <PERSON>q, <PERSON><PERSON>, create<PERSON><PERSON><PERSON> } from 'next-api-decorators';
import type { NextApiRequest, NextApiResponse } from 'next';
import { customSort } from 'common/helpers';
import type { SortOrder } from 'common/globalTypes';
import { customViewDefault } from 'common/constants/account_role_settings';

import { withAuth } from '@/lib/middlewares';
import {
  type ExtNextApiRequest,
  type ExtNextApiResponse,
  Roles,
  type RoleSetting,
} from '@/types';
import { BaseHandler } from '@/lib/baseHandler';
import { UserService } from '@/services/user';
import { container } from '@/ioc';
import { SettingsService } from '@/services/settings';
import { DashboardService } from '@/services/dashboard';
import { AccountService } from '@/services/account';
import {
  defaultContactSettingsObject,
  DirectDownlineDataAccessCompGridRatesOptions,
  ExtendedDownlineDataAccessCompGridRatesOptions,
} from '@/pages/api/accounts/settings/defaults';
import { getParams } from '@/lib/param';

interface UpdateBody {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  insights_widgets?: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  pages_settings?: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  agent_settings?: any;
  custom_view_name?: string;
}

class Handler extends BaseHandler {
  private userService: UserService;
  private settingsService: SettingsService;
  private dashboardService: DashboardService;

  constructor() {
    super();
    this.userService = container.get<UserService>(UserService);
    this.settingsService = container.get<SettingsService>(SettingsService);
    this.dashboardService = container.get<DashboardService>(DashboardService);
  }

  @Get()
  async get(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    req.logger.debug(
      `getSettings in default handler: ${req.method} ${req.url}`
    );
    await getSettings(
      req,
      res,
      this.userService,
      this.settingsService,
      this.dashboardService
    );
  }

  @Post()
  async post(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await postSettings(req, res, this.settingsService);
  }
}
export default withAuth(createHandler(Handler));

export const getSettings = async (
  req: ExtNextApiRequest & NextApiRequest,
  res: ExtNextApiResponse & NextApiResponse,
  userService: UserService,
  settingsService: SettingsService,
  dashboardService: DashboardService
) => {
  const { roleId: queryRoleId } = req.query;

  const accountUserRole = await userService.getUserRoleByUidAndAccountId(
    req.uid,
    req.account_id
  );

  const { role_id: userRole } = accountUserRole;

  const settings = await settingsService.getSettingsByContact({
    uid: req.uid,
    accountId: req.account_id,
    roleId: queryRoleId ? parseInt(queryRoleId) : userRole,
  });
  const dashboardNames = await dashboardService.getDashboardNames(
    req.account_id
  );
  const result = { ...settings, dashboardNames: dashboardNames };
  req.logger.debug(`Response for ${req.method} ${req.url}`, result);

  res.status(200).json(result);
};

export const postCompaniesSettings = async (
  req: ExtNextApiRequest & NextApiRequest,
  res: ExtNextApiResponse & NextApiResponse,
  settingsService: SettingsService
) => {
  const body = req.body;
  if (req.query.roleId) body.role = req.query.roleId;

  // TODO: This companies setting should be placed at account level not at role level
  // When adding/editing companies_view, we need to update the settings for all roles
  const availableRoles = [
    Roles.ACCOUNT_ADMIN,
    Roles.DATA_SPECIALIST,
    Roles.PRODUCER,
  ];
  const response = [];
  for (const role of availableRoles) {
    const settings = await settingsService.upsertAccountRoleSettings(
      req.account_id,
      role,
      { companies_view: body.companies_view },
      req.uid,
      req.ouid
    );
    response.push(settings);
  }

  res.status(200).json(response);
};

const getSettingsParams = (req: ExtNextApiRequest, body) => {
  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let id;
  let accountId = req.account_id;
  let role = +body.role;
  let customViewName = customViewDefault;

  if (body.id) {
    id = JSON.parse(body.id);
    accountId = id.account_id;
    role = id.role_id;
    customViewName = id.custom_view_name;
  }

  return {
    id,
    accountId,
    role,
    customViewName,
  };
};

export const postSettings = async (
  req: ExtNextApiRequest & NextApiRequest,
  res: ExtNextApiResponse & NextApiResponse,
  settingsService: SettingsService
) => {
  const body = req.body;
  if (req.query.roleId) body.role = req.query.roleId;

  if (!body.role) throw new Error(`Role is required`);

  let updateBody: UpdateBody = {};

  const { id, accountId, role, customViewName } = getSettingsParams(req, body);

  if (body.insights_widgets) {
    updateBody = {
      insights_widgets: body.insights_widgets,
    };
  } else {
    if (!body.key) throw new Error(`Key is required`);

    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let currentPageSettings;

    // If id is provided, we are updating an existing page settings
    if (id) {
      currentPageSettings =
        await settingsService.getRoleSettingsByAccountAndRole(
          accountId,
          role,
          customViewName
        );
    }

    const agentSettings = {
      [body.key]: body.value ? JSON.parse(body.value) : {},
    };

    const pageSettings = {
      custom_fields_id: body.custom_fields_id,
      page_label: body.page_label ?? null,
      menu_label: body.menu_label ?? null,
      show_page: body.show_page ?? true,
      fields: body.fields ?? [],
      outstandingMobileFields: body.outstandingMobileFields ?? [], // Mobile fields
      default_filters: body.default_filters ?? null,
      read_only: body.read_only ?? false,
      page_options: body.page_options ?? [],
    };

    updateBody = {
      pages_settings: {
        ...(currentPageSettings?.pages_settings ?? {}),
        [body.key]: pageSettings,
      },
      agent_settings: {
        ...(currentPageSettings?.agent_settings ?? {}),
        ...agentSettings,
      },
    };

    // If we are updating the extendedDownlineDataAccess.compGridRates to LEVELS_LOWER, set the directDownlineDataAccess.compGridRates to NO to avoid conflicts
    const extendedDownlineDataAccess =
      updateBody.agent_settings?.extendedDownlineDataAccess;
    if (
      extendedDownlineDataAccess ===
        ExtendedDownlineDataAccessCompGridRatesOptions.LEVELS_LOWER ||
      extendedDownlineDataAccess ===
        ExtendedDownlineDataAccessCompGridRatesOptions.GRID_LEVELS_LOWER
    ) {
      updateBody.agent_settings.directDownlineDataAccess.compGridRates =
        DirectDownlineDataAccessCompGridRatesOptions.NO;
    }
  }

  if (customViewName !== body.custom_view_name) {
    updateBody.custom_view_name = body.custom_view_name;
  }

  const settings = await settingsService.upsertAccountRoleSettings(
    accountId,
    role,
    updateBody,
    req.uid,
    req.ouid,
    id
  );

  res.status(200).json(settings);
};

export const getSettingsForRoles = async (
  req: ExtNextApiRequest & NextApiRequest,
  res: ExtNextApiResponse & NextApiResponse,
  accountService: AccountService
) => {
  const orderBy = req.query?.orderBy as keyof RoleSetting;
  const sort = req.query?.sort as SortOrder;
  const enable = req.query?.enable;

  req.logger.debug(`getSettingsForRoles: ${req.method} ${req.url}`);
  const pages = getParams<string[]>({ req, key: 'pages', isArray: true });
  const roles = getParams<number[]>({ req, key: 'roles', isArray: true })?.map(
    (roleId) => +roleId
  );
  const keyword = (req.query.q as string) || '';

  let response: RoleSetting[] = await accountService.getSettingsForRoles(
    req.account_id,
    {
      pages,
      keyword,
    }
  );

  response = response.filter((item) =>
    roles?.length ? roles.includes(item.role) : true
  );

  if (enable) {
    const enableCondition =
      enable === 'yes' ? (item) => item.show_page : (item) => !item.show_page;
    response = response.filter(enableCondition);
  }

  response = customSort({ array: response, orderBy, sort });

  req.logger.debug(`Response for ${req.method} ${req.url}`, { data: response });
  res.status(200).json({ data: response, count: response.length });
};

export const getAgentSettingsForProducerRole = async (
  req: ExtNextApiRequest & NextApiRequest,
  res: ExtNextApiResponse & NextApiResponse,
  accountService: AccountService
) => {
  const roleSettings = await accountService.getAllAccountRoleSettings(
    req.account_id
  );

  const producerRoleSettings = roleSettings.filter(
    (setting) => setting.role.id === Roles.PRODUCER
  );

  const allSettings = Object.keys(defaultContactSettingsObject).flatMap(
    (key) => {
      const roleSettingsWithDefaults = producerRoleSettings.map((setting) => {
        const { role, agent_settings } = setting;
        const value =
          agent_settings?.[key] || defaultContactSettingsObject[key];

        return {
          key,
          role: role.id,
          value: JSON.stringify(value),
          custom_view_name: setting.custom_view_name,
          id: JSON.stringify({
            account_id: setting.account_id,
            role_id: setting.role_id,
            custom_view_name: setting.custom_view_name,
            key: key,
          }),
        };
      });

      return roleSettingsWithDefaults;
    }
  );

  res.status(200).json(allSettings);
};

export const getCustomViewProducer = async (
  req: ExtNextApiRequest & NextApiRequest
) => {
  const accountService = container.get<AccountService>(AccountService);

  const customViewProducer =
    await accountService.getAllAccountRoleSettingForRole(
      req.account_id,
      Roles.PRODUCER
    );

  return customViewProducer;
};
