import * as Sentry from '@sentry/nextjs';
import { UserStates } from 'common/constants/user-states.enum';
import type { NextApiRequest, NextApiResponse } from 'next';
import { createHandler, Get, Req, Res } from 'next-api-decorators';

import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import { prismaClient } from '@/lib/prisma';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';

// Endpoint used for getting all accounts for an user on first login
class Handler extends BaseHandler {
  // biome-ignore lint/complexity/noUselessConstructor: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  constructor() {
    super();
  }

  @Get()
  async getOnboardingAccounts(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    await getSettings(req, res);
  }
}

export default withAuth(createHandler(<PERSON>ler));

const getSettings = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  try {
    // TODO: Change to findUnique when it supports the stateInject option
    const user = await prismaClient.users.findFirst({
      where: { uid: req.uid },
      stateInject: false,
      include: {
        account_user_roles: {
          where: {
            account_id: req.account_id,
            OR: [{ state: UserStates.ACTIVE }, { state: UserStates.INVITED }],
          },
        },
      },
    });

    if (!user) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(
        `An error occurred when selecting all user accounts: User not found`
      );
      res.status(500).json({
        error: `An error occurred when selecting all user accounts: User not found`,
      });
      return;
    }
    const accountsData = [];
    for (const accountUserRole of user.account_user_roles) {
      // TODO: Change to findUnique when it supports the accountInject option
      const account = await prismaClient.accounts.findFirst({
        accountInject: false,
        where: { str_id: accountUserRole.account_id, state: UserStates.ACTIVE },
        select: {
          str_id: true,
          name: true,
          mode: true,
          state: true,
        },
      });

      if (account)
        accountsData.push({
          state: account.state,
        });
    }

    res.status(200).json(accountsData);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(
      `An error occurred when selecting all user accounts: ${error}`
    );
    Sentry.captureException(error);
    res.status(500).json({ error: 'Error getting user accounts' });
  }
};
