import * as Sentry from '@sentry/nextjs';

import prisma from '@/lib/prisma';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { validateShortname } from '.';

// Endpoint used for updating the account for an admin user on onboarding process
const onboarding_update = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  switch (req.method) {
    case 'GET':
      await getSettings(req, res);
      break;
    case 'POST':
      await patchSettings(req, res);
      break;
    case 'PATCH':
      await patchSettings(req, res);
      break;
    default:
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
};

const getSettings = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  try {
    const user = await prisma.users.findFirst({
      where: { uid: String(req.uid) },
      include: {
        account_user_roles: true,
      },
    });
    const accountData = await prisma.accounts.findFirst({
      // This endpoint is only supposed to be used on the first login for a new created account
      // so it should be safe to assume that there only exists one account for that user.
      where: { str_id: String(user.account_user_roles[0].account_id) },
    });
    res.status(200).json(accountData);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`An error occurred ${error}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error });
  }
};

const patchSettings = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const body = req.body;
  try {
    if (!body.short_name) throw new Error(`Account identifier is required`);
    await validateShortname(body.short_name, req.account_id);

    const user = await prisma.users.findFirst({
      where: { uid: String(req.uid) },
      include: {
        account_user_roles: true,
      },
    });
    const accountIds = user.account_user_roles.map(
      (account) => account.account_id
    );
    if (!user || !req.account_id || !accountIds.includes(req.account_id)) {
      throw new Error(`Account not found for user`);
    }
    const data = await prisma.accounts.update({
      where: { str_id: req.account_id },
      data: {
        ...body,
        state: 'active',
        mode: 'insurance',
      },
    });
    res.status(200).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`An error occurred ${error}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

export default withAuth(onboarding_update, { doNotCheckRole: true });
