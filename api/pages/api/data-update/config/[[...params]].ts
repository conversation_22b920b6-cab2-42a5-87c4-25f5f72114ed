import type { NextApiRequest, NextApiResponse } from 'next';
import {
  createHandler,
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
} from 'next-api-decorators';

import { BaseHandler } from '@/lib/baseHandler';
import { ZodBody } from '@/lib/decorators';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { DataUpdateConfigService } from '@/services/data-update/config';
import { container } from '@/ioc';
import * as dto from 'common/dto/data-update/config';

class Handler extends BaseHandler {
  private dataUpdateConfigService: DataUpdateConfigService;

  constructor() {
    super();
    this.dataUpdateConfigService = container.get<DataUpdateConfigService>(
      DataUpdateConfigService
    );
  }

  @Get()
  async get(@Req() req: ExtNextApiRequest & NextApiRequest) {
    const { data, count } = await this.dataUpdateConfigService.getByAccountId({
      accountId: req.account_id,
      isGlobal: req.query.global === 'true',
      orderBy: req.query.orderBy as string,
      textSearch: this.getTextSearch(req),
      sort: this.getSort(req),
      pagination: this.getPagination(req),
    });

    return { data, count, totals: data.length };
  }

  @Post()
  async post(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    if (!req.account_id) throw new Error('Account id is required');
    const { copy } = req.query;
    const isCopy = copy === 'true';

    if (isCopy) {
      // Validate the array when copy is true
      const validatedData = dto.DataUpdateConfigCreateArraySchema.parse(
        req.body
      );
      const response = [];
      for (const datum of validatedData) {
        const {
          group,
          data_entity,
          data_update_criteria,
          data_update_actions,
          flag_config_mode,
          notes,
          name,
          access,
        } = datum;

        const res = await this.dataUpdateConfigService.createOne(
          flag_config_mode,
          group,
          data_entity,
          data_update_criteria,
          data_update_actions,
          notes,
          name,
          req.account_id,
          req.uid,
          req.ouid,
          access
        );
        response.push(res);
      }
      res.json(response);
      return;
    }

    // Validate single object when copy is false
    const body = dto.DataUpdateConfigCreateSchema.parse(req.body);
    const {
      data_entity,
      group,
      data_update_criteria,
      data_update_actions,
      flag_config_mode,
      notes,
      name,
      access,
    } = body;

    const data = await this.dataUpdateConfigService.createOne(
      flag_config_mode,
      group,
      data_entity,
      data_update_criteria,
      data_update_actions,
      notes,
      name,
      req.account_id,
      req.uid,
      req.ouid,
      access
    );

    res.json(data);
  }

  @Patch()
  async patch(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.DataUpdateConfigUpdateSchema)())
    body: dto.DataUpdateConfigUpdateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    if (!req.account_id) throw new Error('Account id is required');
    const {
      str_id,
      group,
      data_entity,
      data_update_criteria,
      data_update_actions,
      flag_config_mode,
      notes,
      name,
    } = body;

    const data = await this.dataUpdateConfigService.updateOne(
      flag_config_mode,
      str_id,
      group,
      data_entity,
      data_update_criteria,
      data_update_actions,
      notes,
      name,
      req.account_id,
      req.uid,
      req.ouid
    );

    res.json(data);
  }

  @Delete()
  async delete(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.DataUpdateConfigDeleteSchema)())
    body: dto.DataUpdateConfigDeleteDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    if (!req.account_id) throw new Error('Account id is required');
    const { ids } = body;
    const data = await this.dataUpdateConfigService.deleteOne(
      +ids[0],
      req.account_id,
      req.uid,
      req.ouid
    );

    res.json(data);
  }
}

export default withAuth(createHandler(Handler));
