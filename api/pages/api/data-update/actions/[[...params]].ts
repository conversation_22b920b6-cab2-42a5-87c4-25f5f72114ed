import type { NextApiRequest, NextApiResponse } from 'next';
import {
  createHandler,
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
} from 'next-api-decorators';

import { BaseHandler } from '@/lib/baseHandler';
import { ZodBody } from '@/lib/decorators';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import {
  DataUpdateActionsService,
  type IDataUpdateActionsService,
} from '@/services/data-update/actions';
import { container } from '@/ioc';
import * as dto from 'common/dto/data-update/actions';

class Handler extends BaseHandler {
  private dataUpdateActionsService: IDataUpdateActionsService;

  constructor() {
    super();
    this.dataUpdateActionsService = container.get<DataUpdateActionsService>(
      DataUpdateActionsService
    );
  }

  @Get()
  async get(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    // TODO: add a Zod validation here for the inputs
    const { data, count } = await this.dataUpdateActionsService.getByAccountId({
      accountId: req.account_id,
      isGlobal: req.query.global === 'true',
      orderBy: req.query.orderBy as string,
      textSearch: this.getTextSearch(req),
      sort: this.getSort(req),
      pagination: this.getPagination(req),
    });

    res.json({ data, count, totals: data.length });
  }

  @Post()
  async post(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    if (!req.account_id) throw new Error('Account id is required');
    const { copy } = req.query;
    const isCopy = copy === 'true';

    if (isCopy) {
      // Validate the array when copy is true
      const validatedData = dto.DataUpdateActionCreateArraySchema.parse(
        req.body
      );
      const response = [];
      for (const datum of validatedData) {
        const {
          data_entity,
          data_update_actions,
          data_update_actions_params,
          notes,
          name,
          access,
        } = datum;
        const res = await this.dataUpdateActionsService.createDataUpdateAction(
          data_entity,
          data_update_actions,
          data_update_actions_params,
          notes,
          name,
          req.account_id,
          req.uid,
          req.ouid,
          access
        );
        response.push(res);
      }
      res.json(response);
      return;
    }

    // Validate single object when copy is false
    const body = dto.DataUpdateActionCreateSchema.parse(req.body);
    const {
      data_entity,
      data_update_actions,
      data_update_actions_params,
      notes,
      name,
      access,
    } = body;

    const data = await this.dataUpdateActionsService.createDataUpdateAction(
      data_entity,
      data_update_actions,
      data_update_actions_params,
      notes,
      name,
      req.account_id,
      req.uid,
      req.ouid,
      access
    );

    res.json(data);
  }

  @Patch()
  async patch(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.DataUpdateActionUpdateSchema)())
    body: dto.DataUpdateActionUpdateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    if (!req.account_id) throw new Error('Account id is required');
    const {
      data_entity,
      data_update_actions,
      data_update_actions_params,
      notes,
      name,
      str_id,
    } = body;

    const data = await this.dataUpdateActionsService.updateDataUpdateAction(
      data_entity,
      data_update_actions,
      data_update_actions_params,
      notes,
      name,
      str_id,
      req.account_id,
      req.uid,
      req.ouid
    );

    res.json(data);
  }

  @Delete()
  async delete(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.DataUpdateActionDeleteSchema)())
    body: dto.DataUpdateActionDeleteDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    if (!req.account_id) throw new Error('Account id is required');
    const { ids } = body;
    const data = await this.dataUpdateActionsService.deleteOne(
      +ids[0],
      req.account_id,
      req.uid,
      req.ouid
    );

    res.json(data);
  }
}

export default withAuth(createHandler(Handler));
