import type { NextApiRequest, NextApiResponse } from 'next';
import {
  createHandler,
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
} from 'next-api-decorators';

import { BaseHandler } from '@/lib/baseHandler';
import { ZodBody } from '@/lib/decorators';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import {
  DataUpdateCriteriaService,
  type IDataUpdateCriteriaService,
} from '@/services/data-update/criteria';
import { container } from '@/ioc';
import * as dto from 'common/dto/data-update/criteria';

class Handler extends BaseHandler {
  private dataUpdateCriteriaService: IDataUpdateCriteriaService;

  constructor() {
    super();
    this.dataUpdateCriteriaService = container.get<DataUpdateCriteriaService>(
      DataUpdateCriteriaService
    );
  }

  @Get()
  async get(@Req() req: ExtNextApiRequest & NextApiRequest) {
    const { data, count } = await this.dataUpdateCriteriaService.getByAccountId(
      {
        accountId: req.account_id,
        isGlobal: req.query.global === 'true',
        orderBy: req.query.orderBy as string,
        textSearch: this.getTextSearch(req),
        sort: this.getSort(req),
        pagination: this.getPagination(req),
      }
    );

    return { data, count, totals: data.length };
  }

  @Post()
  async post(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    if (!req.account_id) throw new Error('Account id is required');
    const { copy } = req.query;
    const isCopy = copy === 'true';

    if (isCopy) {
      // Validate the array when copy is true
      const validatedData = dto.DataUpdateCriteriaCreateArraySchema.parse(
        req.body
      );
      const response = [];
      for (const datum of validatedData) {
        const {
          custom_data_update_criteria_mode,
          data_entity,
          data_update_criteria,
          custom_data_update_criteria,
          custom_data_update_criteria_params,
          notes,
          name,
          access,
        } = datum;

        this.dataUpdateCriteriaService.validateDataUpdateCriteria(
          custom_data_update_criteria_mode,
          data_update_criteria,
          custom_data_update_criteria
        );

        const res =
          await this.dataUpdateCriteriaService.createDataUpdateCriteria(
            custom_data_update_criteria_mode,
            data_entity,
            data_update_criteria,
            custom_data_update_criteria,
            custom_data_update_criteria_params,
            notes,
            name,
            req.account_id,
            req.uid,
            req.ouid,
            access
          );
        response.push(res);
      }
      res.json(response);
      return;
    }

    // Validate single object when copy is false
    const body = dto.DataUpdateCriteriaCreateSchema.parse(req.body);
    const {
      custom_data_update_criteria_mode,
      data_entity,
      data_update_criteria,
      custom_data_update_criteria,
      custom_data_update_criteria_params,
      notes,
      name,
      access,
    } = body;

    this.dataUpdateCriteriaService.validateDataUpdateCriteria(
      custom_data_update_criteria_mode,
      data_update_criteria,
      custom_data_update_criteria
    );

    const data = await this.dataUpdateCriteriaService.createDataUpdateCriteria(
      custom_data_update_criteria_mode,
      data_entity,
      data_update_criteria,
      custom_data_update_criteria,
      custom_data_update_criteria_params,
      notes,
      name,
      req.account_id,
      req.uid,
      req.ouid,
      access
    );

    res.json(data);
  }

  @Patch()
  async patch(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.DataUpdateCriteriaUpdateSchema)())
    body: dto.DataUpdateCriteriaUpdateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    if (!req.account_id) throw new Error('Account id is required');
    const {
      custom_data_update_criteria_mode,
      data_entity,
      data_update_criteria,
      custom_data_update_criteria,
      custom_data_update_criteria_params,
      notes,
      name,
      str_id,
    } = body;

    this.dataUpdateCriteriaService.validateDataUpdateCriteria(
      custom_data_update_criteria_mode,
      data_update_criteria,
      custom_data_update_criteria
    );

    const data = await this.dataUpdateCriteriaService.updateDataUpdateCriteria(
      custom_data_update_criteria_mode,
      data_entity,
      data_update_criteria,
      custom_data_update_criteria,
      custom_data_update_criteria_params,
      notes,
      name,
      str_id,
      req.account_id,
      req.uid,
      req.ouid
    );

    res.json(data);
  }

  @Delete()
  async delete(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.DataUpdateCriteriaDeleteSchema)())
    body: dto.DataUpdateCriteriaDeleteDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    if (!req.account_id) throw new Error('Account id is required');
    const { ids } = body;
    const data = await this.dataUpdateCriteriaService.deleteOne(
      +ids[0],
      req.account_id,
      req.uid,
      req.ouid
    );

    res.json(data);
  }
}

export default withAuth(createHandler(Handler));
