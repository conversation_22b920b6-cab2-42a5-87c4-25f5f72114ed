import type { DateRangesTypes } from '@prisma/client';
import type { NextApiRequest, NextApiResponse } from 'next';
import {
  createHandler,
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
} from 'next-api-decorators';
import { convertDateFields } from 'common/helpers';

import dayjs from '@/lib/dayjs';
import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import {
  DateRangesService,
  type IDateRangesService,
} from '@/services/date-ranges';
import type { DateRange, ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import {
  CompGridRatesService,
  type ICompGridRatesService,
} from '@/services/comp-grids/rates';
import {
  CompGridCriteriaService,
  type ICompGridCriteriaService,
} from '@/services/comp-grids/criteria';

class Handler extends BaseHandler {
  private dateRangesService: DateRangesService;
  private compGridRatesService: CompGridRatesService;
  private compGridCriteriaService: CompGridCriteriaService;

  constructor() {
    super();
    this.dateRangesService =
      container.get<DateRangesService>(DateRangesService);
    this.compGridRatesService =
      container.get<CompGridRatesService>(CompGridRatesService);
    this.compGridCriteriaService = container.get<CompGridCriteriaService>(
      CompGridCriteriaService
    );
  }

  @Get()
  async get(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const type = req.query.type as DateRangesTypes;
    const compGrids = (req.query.comp_grids?.split(',') ?? []).map(Number);
    return await getManyByType(
      req,
      res,
      type,
      compGrids,
      this.dateRangesService
    );
  }

  @Post()
  async post(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await createOne(req, res, this.dateRangesService);
  }

  @Patch()
  async patch(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await updateOne(req, res, this.dateRangesService);
  }

  @Delete()
  async delete(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await deleteOne(
      req,
      res,
      this.dateRangesService,
      this.compGridRatesService,
      this.compGridCriteriaService
    );
  }
}

const getManyByType = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  type: DateRangesTypes,
  compGrids: number[],
  dateRangesService: DateRangesService
) => {
  const dateRangesList = await dateRangesService.getDateRangesByType(
    type,
    compGrids,
    req.account_id
  );

  return res.json(dateRangesList);
};

const createOne = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  dateRangesService: DateRangesService
) => {
  let dateRange = req.body as DateRange;
  dateRange.account_id = req.account_id;
  dateRange.created_by = req.uid;
  dateRange.created_proxied_by = req.ouid;

  if (dayjs(dateRange.start_date).isAfter(dayjs(dateRange.end_date))) {
    return res.status(400).json({
      success: false,
      statusText: 'error',
      error: 'Start date must be before end date',
    });
  }

  dateRange = convertDateFields(dateRange) as DateRange;

  const createdDateRange = await dateRangesService.createOne(dateRange);

  return res.json(createdDateRange);
};

const updateOne = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  dateRangesService: DateRangesService
) => {
  const dateRange = convertDateFields(req.body) as DateRange;
  dateRange.account_id = req.account_id;
  dateRange.updated_by = req.uid;
  dateRange.updated_proxied_by = req.ouid;

  const updatedDateRange = await dateRangesService.updateOne(dateRange);
  if (!updatedDateRange) {
    return res.status(500).json({
      success: false,
      statusText: 'error',
      error: 'Date range cannot overlap with existing date ranges',
    });
  }

  return res.json(updatedDateRange);
};

const deleteOne = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  dateRangesService: IDateRangesService,
  compGridRatesService: ICompGridRatesService,
  compGridCriteriaService: ICompGridCriteriaService
) => {
  const { id } = req.body;

  // Delete associated criteria and rates
  await compGridRatesService.deleteRatesByDateRange(id, req.uid, req.ouid);
  await compGridCriteriaService.deleteCriteriaByDateRange(
    id,
    req.uid,
    req.ouid
  );

  const deletedDateRange = await dateRangesService.deleteOne(
    id,
    req.uid,
    req.ouid
  );

  return res.json(deletedDateRange);
};

export default withAuth(createHandler(Handler));
