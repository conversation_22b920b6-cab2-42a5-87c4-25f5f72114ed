import * as Sentry from '@sentry/nextjs';
import { nanoid } from 'nanoid';
import type {
  company_products as CompanyProducts,
  Prisma,
} from '@prisma/client';
import {
  createHandler,
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
} from 'next-api-decorators';
import type { NextApiRequest, NextApiResponse } from 'next';
import { customViewDefault } from 'common/constants/account_role_settings';

import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import type {
  ExtAccountInfo,
  ExtNextApiRequest,
  ExtNextApiResponse,
} from '@/types';
import { container } from '@/ioc';
import { SyncFieldService } from '@/pages/api/data_processing/sync/syncFieldService';
import { BaseHandler } from '@/lib/baseHandler';
import { Guard } from '@/services/permission/decorator';
import { CrudAction, EntityType } from '@/services/permission/interface';
import { calculateSkipAndTake } from '@/prisma';

type WhereClause = Prisma.company_productsWhereInput & {
  company?: {
    state?: string;
  };
  str_id?: string;
  company_id?: number;
};

class Handler extends BaseHandler {
  @Get()
  @Guard(CrudAction.READ, EntityType.PRODUCTS)
  async getData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await getProductsData(req, res);
  }

  @Post()
  @Guard(CrudAction.CREATE, EntityType.PRODUCTS)
  async createData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await postData(req, res);
  }

  @Patch()
  @Guard(CrudAction.UPDATE, EntityType.PRODUCTS)
  async updateData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await patchData(req, res);
  }

  @Delete()
  @Guard(CrudAction.DELETE, EntityType.PRODUCTS)
  async deleteData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await deleteData(req, res);
  }
}

export default withAuth(createHandler(Handler));

const TABLE = 'company_products';

export const _getData = async (req) => {
  const {
    cids,
    comp_grid_id = undefined,
    company_name = undefined,
    is_dynamic_select,
    limit,
    order_by,
    order,
    page,
    q: _q = '',
    qc = undefined,
    product_name,
  } = req.query;

  const { take, skip } = calculateSkipAndTake({ page, limit });

  const q = _q.trim();

  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let company_str_ids;
  if (cids) {
    company_str_ids = Array.isArray(cids) ? cids : [cids];
  }

  const accountSettings = await prisma.account_role_settings.findFirst({
    where: {
      account_id: String(req.account_id),
      role_id: parseInt(req.role_id),
      custom_view_name: customViewDefault,
    },
  });

  // Used for dynamic select
  let productNameWhere = {};
  if (Array.isArray(product_name)) {
    productNameWhere = {
      product_name: {
        in: product_name,
      },
    };
  }

  const where: WhereClause = {
    AND: [
      {
        OR: [
          { account_id: req.account_id, state: 'active' },
          {
            AND: [
              { company: { access: 'global', state: 'active' } },
              {
                company: {
                  company_name: {
                    in: [
                      ...(accountSettings?.companies_view
                        ? (accountSettings?.companies_view as string[])
                        : []),
                    ],
                  },
                },
              },
              { state: 'active' },
            ],
          },
        ],
      },
      q
        ? {
            OR: [
              { product_name: { contains: q, mode: 'insensitive' } },
              { product_type: { contains: q, mode: 'insensitive' } },
              { notes: { contains: q, mode: 'insensitive' } },
            ],
          }
        : {},
      qc === 'contains_products'
        ? { comp_grid_products: { some: { state: 'active' } } }
        : qc === 'missing_products'
          ? { comp_grid_products: { none: { state: 'active' } } }
          : {},
      productNameWhere,
    ],
    company: {
      state: 'active',
      company_name: company_name,
      str_id: company_str_ids ? { in: company_str_ids } : undefined,
      comp_grids: comp_grid_id ? { some: { id: comp_grid_id } } : undefined,
    },
    str_id: req.query?.id,
    company_id: req.query?.company_id
      ? Number(req.query.company_id)
      : undefined,
  };

  const orderBy = order_by
    ? { [order_by]: order ?? 'asc' }
    : [{ company: { company_name: 'asc' } }, { product_name: 'asc' }];

  if (is_dynamic_select) {
    const data = await prisma[TABLE].findMany({
      where,
      select: {
        id: true,
        str_id: true,
        sync_worker: true,
        product_name: true,
        company_id: true,
        product_type: true,
        company: {
          select: {
            company_name: true,
          },
        },
      },
    });

    return data;
  }

  const findManyOperation = prisma[TABLE].findMany({
    where,
    orderBy,
    skip,
    take,
    include: {
      company: {
        select: { id: true, company_name: true },
      },
      comp_grid_products: {
        where: { state: 'active' },
        select: {
          id: true,
          str_id: true,
          name: true,
        },
      },
    },
  });
  const countOperation = prisma[TABLE].count({ where });
  const [data, count] = await Promise.all([findManyOperation, countOperation]);

  return { data: data, count: count };
};

const getProductsData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  try {
    const { company_id, company_name, cids } = req.query || {};

    if ((company_name || company_id) && !cids) {
      let companyId = +company_id;
      if (!companyId || Number.isNaN(companyId)) {
        const company = await prisma.companies.findFirst({
          where: {
            OR: [
              {
                account_id: req.account_id,
              },
              { access: 'global' },
            ],
            state: 'active',
            company_name,
          },
          select: {
            id: true,
          },
        });

        if (!company)
          return res.json([{ product_name: 'Product not available' }]);
        else companyId = company.id;

        const data = await prisma[TABLE].findMany({
          where: {
            OR: [
              { account_id: req.account_id },
              { company: { access: 'global' } },
            ],
            state: 'active',
            company_id: companyId,
          },
          select: {
            product_name: true,
          },
        });

        return res.json(data);
      }
    }

    const data = await _getData(req);
    res.json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`An error occurred when getting products: ${error.message}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const postData = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const body = req.body as CompanyProducts;
  try {
    if (!body.company_id) throw new Error('Company is required');
    const { id: _id, company_id, ...rest } = body;
    const data = await prisma[TABLE].create({
      data: {
        ...rest,
        str_id: nanoid(),
        uid: req.uid,
        account_id: req.account_id,
        company: { connect: { id: company_id } },
      },
    });
    res.status(201).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error('An error occurred when creating a new product', error);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const patchData = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const body = req.body as CompanyProducts;
  try {
    if (!body.id) throw new Error('Missing id');
    const { id, company_id, product_type, product_name, notes } = body;
    const updateData = {
      company_id,
      product_type,
      product_name,
      notes,
      updated_at: new Date(),
      updated_by: req.uid,
      updated_proxied_by: req.ouid,
    };
    await container.get<SyncFieldService>(SyncFieldService).canUpdateIfChanged({
      newData: updateData,
      tableName: TABLE,
      id: Number(id),
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      config: body.config as any,
      account: { account_id: req.account_id } as ExtAccountInfo,
    });
    const data = await prisma[TABLE].update({
      where: { id: Number(id), account_id: String(req.account_id) },
      data: updateData,
    });
    res.status(200).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error('Error updating product', error);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const deleteData = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const { ids } = req.body;
  try {
    if (!Array.isArray(ids) || ids.length === 0) throw new Error('Missing ids');
    // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
      throw new Error('Invalid ids');
    const promises = ids.map((_id) => {
      return prisma[TABLE].update({
        where: { id: Number(_id), account_id: String(req.account_id) },
        data: {
          state: 'deleted',
        },
      });
    });
    await prisma.$transaction(promises);
    res.status(200).json({ status: 'OK' });
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error deleting product: ${error}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};
