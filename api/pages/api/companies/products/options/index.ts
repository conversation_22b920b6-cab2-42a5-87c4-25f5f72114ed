import * as Sentry from '@sentry/nextjs';
import { nanoid } from 'nanoid';
import type {
  company_product_options as CompanyProductOptions,
  Prisma,
} from '@prisma/client';
import {
  createHandler,
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
} from 'next-api-decorators';
import type { NextApiRequest, NextApiResponse } from 'next';
import { customViewDefault } from 'common/constants/account_role_settings';

import { BaseHandler } from '@/lib/baseHandler';
import { Guard } from '@/services/permission/decorator';
import { CrudAction, EntityType } from '@/services/permission/interface';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { calculateSkipAndTake } from '@/prisma';

type WhereClause = Prisma.company_product_optionsWhereInput & {
  product?: {
    state?: string;
  };
  str_id?: string;
  product_id?: number;
};

class Handler extends BaseHandler {
  @Get()
  @Guard(CrudAction.READ, EntityType.OPTIONS)
  async getData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await getData(req, res);
  }

  @Post()
  @Guard(CrudAction.CREATE, EntityType.OPTIONS)
  async createData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await postData(req, res);
  }

  @Patch()
  @Guard(CrudAction.UPDATE, EntityType.OPTIONS)
  async updateData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await patchData(req, res);
  }

  @Delete()
  @Guard(CrudAction.DELETE, EntityType.OPTIONS)
  async deleteData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await deleteData(req, res);
  }
}

export default withAuth(createHandler(Handler));

const TABLE = 'company_product_options';

export const _getData = async (req) => {
  const { page, limit, q = '', is_dynamic_select } = req.query;

  const { take, skip } = calculateSkipAndTake({ page, limit });

  const accountSettings = await prisma.account_role_settings.findFirst({
    where: {
      account_id: String(req.account_id),
      role_id: parseInt(req.role_id),
      custom_view_name: customViewDefault,
    },
  });

  const where: WhereClause = {
    AND: [
      {
        OR: [
          { account_id: req.account_id, state: 'active' },
          {
            AND: [
              { product: { company: { access: 'global', state: 'active' } } },
              {
                product: {
                  company: {
                    company_name: {
                      in: [
                        ...(accountSettings?.companies_view
                          ? (accountSettings?.companies_view as string[])
                          : []),
                      ],
                    },
                  },
                },
              },
              { state: 'active' },
            ],
          },
        ],
      },
      q
        ? {
            OR: [
              { name: { contains: q, mode: 'insensitive' } },
              { notes: { contains: q, mode: 'insensitive' } },
            ],
          }
        : {},
    ],
    product: {
      state: 'active',
    },
    str_id: req.query?.id,
    product_id: req.query?.product_id
      ? Number(req.query.product_id)
      : undefined,
  };

  if (is_dynamic_select) {
    const data = await prisma[TABLE].findMany({
      where,
      select: {
        id: true,
        str_id: true,
        name: true,
        product_id: true,
        product: {
          select: {
            product_name: true,
          },
        },
      },
    });

    return data;
  }

  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let orderBy;
  if (req.query?.orderBy && req.query?.sort) {
    orderBy = {
      [req.query.orderBy]: req.query.sort,
    };
  }

  const findManyOperation = prisma[TABLE].findMany({
    where,
    skip,
    take,
    include: {
      product: {
        select: { product_name: true },
      },
    },
    orderBy,
  });
  const countOperation = prisma[TABLE].count({ where });
  const [data, count] = await Promise.all([findManyOperation, countOperation]);

  return { data: data, count: count };
};

const getData = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  try {
    if (req.query?.company_id) {
      const data = await prisma[TABLE].findMany({
        where: {
          OR: [
            { account_id: req.account_id },
            { product: { company: { access: 'global' } } },
          ],
          product: {
            company_id: Number(req.query.company_id),
            state: 'active',
          },
          state: 'active',
        },
        include: {
          product: true,
        },
      });
      return res.json(data);
    }

    const data = await _getData(req);
    res.json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`An error occurred when getting products: ${error.message}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const postData = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const body = req.body as CompanyProductOptions;
  try {
    if (!body.product_id) throw new Error('Product is required');
    const { id: _id, product_id, ...rest } = body;
    const data = await prisma[TABLE].create({
      data: {
        ...rest,
        str_id: nanoid(),
        uid: req.uid,
        account_id: req.account_id,
        product: { connect: { id: product_id } },
      },
    });
    res.status(201).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(
      'An error occurred when creating a new product option',
      error
    );
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const patchData = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const body = req.body as CompanyProductOptions;
  try {
    if (!body.id) throw new Error('Missing id');
    const { product_id, name, notes } = body;
    const updateData = {
      product_id,
      name,
      notes,
      updated_at: new Date(),
      updated_by: req.uid,
      updated_proxied_by: req.ouid,
    };

    const data = await prisma[TABLE].update({
      where: { id: Number(body.id), account_id: String(req.account_id) },
      data: updateData,
    });
    res.status(200).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error('Error updating product option', error);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const deleteData = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const { ids } = req.body;
  try {
    if (!Array.isArray(ids) || ids.length === 0) throw new Error('Missing ids');
    // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
      throw new Error('Invalid ids');
    const promises = ids.map((_id) => {
      return prisma[TABLE].update({
        where: { id: Number(_id), account_id: String(req.account_id) },
        data: {
          state: 'deleted',
        },
      });
    });
    await prisma.$transaction(promises);
    res.status(200).json({ status: 'OK' });
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error deleting product option: ${error}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};
