import * as Sentry from '@sentry/nextjs';
import { nanoid } from 'nanoid';
import {
  createHandler,
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
} from 'next-api-decorators';
import type { NextApiRequest, NextApiResponse } from 'next';
import { ImportStatuses } from 'common/globalTypes';

import { BaseHandler } from '@/lib/baseHandler';
import { Guard } from '@/services/permission/decorator';
import { CrudAction, EntityType } from '@/services/permission/interface';
import prisma from '@/lib/prisma';
import { withAuth } from '@/lib/middlewares';
import {
  DataStates,
  type ExtAccountInfo,
  type ExtNextApiRequest,
  type ExtNextApiResponse,
  Roles,
} from '@/types';
import { container } from '@/ioc';
import { SyncFieldService } from '@/pages/api/data_processing/sync/syncFieldService';
import { CompaniesService } from '@/services/companies';
import { limitConcurrency } from '@/lib/helpers';
import type { CompaniesFilter } from '@/services/companies/types';
import type { SettingsService } from '@/services/settings';
import type { ContactsService } from '@/services/contacts';
import type { UserService } from '@/services/user';

class Handler extends BaseHandler {
  private companiesService: CompaniesService;

  constructor() {
    super();
    this.companiesService = container.get<CompaniesService>(CompaniesService);
  }

  @Get()
  @Guard(CrudAction.READ, EntityType.COMPANIES)
  async getData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await this.getCompanies(req, res);
  }

  @Post()
  @Guard(CrudAction.CREATE, EntityType.COMPANIES)
  async createData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await postCompany(req, res);
  }

  @Patch()
  @Guard(CrudAction.UPDATE, EntityType.COMPANIES)
  async updateData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await patchCompany(req, res);
  }

  @Delete()
  @Guard(CrudAction.DELETE, EntityType.COMPANIES)
  async deleteData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await deleteCompany(req, res);
  }

  private async getCompanies(req: ExtNextApiRequest, res: ExtNextApiResponse) {
    try {
      const data = await _getData(
        req,
        res,
        undefined,
        undefined,
        undefined,
        this.companiesService,
        undefined
      );
      res.json(data);
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(
        `An error occurred when getting companies: ${error.message}`
      );
      Sentry.captureException(error);
      res.status(500).json({ error: error.message });
    }
  }
}

export default withAuth(createHandler(Handler));

const TABLE = 'companies';

export const _getData = async (
  req: ExtNextApiRequest,
  _res: ExtNextApiResponse,
  _s: SettingsService,
  _c: ContactsService,
  _u: UserService,
  companiesService: CompaniesService,
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  _q: any
) => {
  const filter: CompaniesFilter = {
    accountId: req.account_id,
    adminMode: Boolean(req.query?.admin_mode),
    access: req.query?.access,
    q: req.query?.q ?? '',
    type: req.query?.type,
    strId: req.query?.str_id,
    companyNames: Array.isArray(req.query?.companyNames)
      ? req.query?.companyNames
      : undefined,
    all: req.query?.all === '1',
    isDynamicSelect: Boolean(req.query?.is_dynamic_select),
    limit: req.query?.limit ? Number(req.query.limit) : undefined,
    orderBy: req.query?.order_by,
    order: req.query?.order,
    page: req.query?.page ? Number(req.query.page) : undefined,
    roleId: req.role_id,
  };
  return await companiesService.fetchCompaniesByFilter(filter);
};

/**
 * Company payload
 */
// interface CompanyPayload {
//   id: number;
//   str_id: string;
//   account_id: string;
//   uid: string;
//   state: string;
//   created_at: string;
//   created_by: any;
//   updated_at: any;
//   updated_by: any;
//   address: any;
//   company_id: any;
//   company_name: string;
//   email: string;
//   group_id: any;
//   notes: any;
//   phone: any;
//   type: any;
//   website: string;
//   alias_list: string;
//   access: string;
//   [key: string]: string;
// }

export const postCompany = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const {
    processor_str_ids = [],
    companies_processors = [],
    profile_str_ids = [],
    companies_document_profiles,
    ...companyData
  } = {
    ...req.body,
    alias_list:
      typeof req.body.alias_list === 'string'
        ? JSON.parse(req.body.alias_list)
        : req.body.alias_list,
  };

  try {
    const data = await prisma.$transaction(async (tx) => {
      const company = await tx[TABLE].create({
        data: {
          ...companyData,
          str_id: nanoid(),
          uid: req.uid,
          account_id: req.account_id,
          created_by: req.uid,
          created_proxied_by: req.ouid,
        },
      });

      if (companies_processors.length > 0) {
        await limitConcurrency(
          async (proc) => {
            await tx.companies_processors.create({
              data: {
                company_str_id: company.str_id,
                processor_str_id: proc.processor_str_id,
                import_status: proc.import_status || ImportStatuses.NONE,
                account_id: req.account_id,
                created_at: new Date(),
                created_by: req.uid,
                created_proxied_by: req.ouid,
                updated_at: new Date(),
                updated_by: req.uid,
                updated_proxied_by: req.ouid,
              },
            });
          },
          companies_processors,
          15
        );
      } else if (processor_str_ids.length > 0) {
        await limitConcurrency(
          async (processor_str_id) => {
            await tx.companies_processors.create({
              data: {
                company_str_id: company.str_id,
                processor_str_id,
                import_status: ImportStatuses.NONE,
                account_id: req.account_id,
                created_at: new Date(),
                created_by: req.uid,
                created_proxied_by: req.ouid,
                updated_at: new Date(),
                updated_by: req.uid,
                updated_proxied_by: req.ouid,
              },
            });
          },
          processor_str_ids,
          15
        );
      }

      if (
        companies_document_profiles &&
        companies_document_profiles.length > 0
      ) {
        await limitConcurrency(
          async (profileRelation) => {
            await tx.companies_document_profiles.create({
              data: {
                company_str_id: company.str_id,
                document_profile_str_id:
                  profileRelation.document_profile_str_id,
                auto_mapping_id: profileRelation.auto_mapping_id || null,
                account_id: req.account_id,
                created_at: new Date(),
                created_by: req.uid,
                created_proxied_by: req.ouid,
                updated_at: new Date(),
                updated_by: req.uid,
                updated_proxied_by: req.ouid,
              },
            });
          },
          companies_document_profiles,
          15
        );
      } else if (profile_str_ids.length > 0) {
        await limitConcurrency(
          async (profile_str_id) => {
            await tx.companies_document_profiles.create({
              data: {
                company_str_id: company.str_id,
                document_profile_str_id: profile_str_id,
                account_id: req.account_id,
                created_at: new Date(),
                created_by: req.uid,
                created_proxied_by: req.ouid,
                updated_at: new Date(),
                updated_by: req.uid,
                updated_proxied_by: req.ouid,
              },
            });
          },
          profile_str_ids,
          15
        );
      }

      return company;
    });

    res.status(201).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(
      `An error occurred when creating a new company: ${error.message}`
    );
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const patchCompany = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  try {
    const {
      id: companyId,
      str_id: companyStrId = null,
      processor_str_ids = [],
      companies_processors = [],
      profile_str_ids = [],
      companies_document_profiles,
      document_profiles: _document_profiles,
      canonical_id,
      potential_match: _,
      ...updateData
    } = {
      ...req.body,
      alias_list:
        typeof req.body.alias_list === 'string'
          ? JSON.parse(req.body.alias_list)
          : req.body.alias_list,
    };

    if (!companyId) throw new Error('Missing id');

    let data = {};
    await container.get<SyncFieldService>(SyncFieldService).canUpdateIfChanged({
      newData: updateData,
      tableName: 'companies',
      id: Number(companyId),
      config: updateData.config,
      account: { account_id: req.account_id } as ExtAccountInfo,
    });

    let finalCompanyStrId = companyStrId;
    if (!finalCompanyStrId) {
      const companyData = await prisma.companies.findUnique({
        where: { id: Number(companyId) },
        select: { str_id: true },
      });
      if (!companyData) throw new Error('Company not found');
      finalCompanyStrId = companyData.str_id;
    }

    const isAdmin = parseInt(req.role_id) === Roles.FINTARY_ADMIN;
    const isAccountAdmin = parseInt(req.role_id) === Roles.ACCOUNT_ADMIN;

    if (canonical_id) {
      updateData.canonical_company = {
        connect: { id: canonical_id },
      };
    } else if (canonical_id === null) {
      updateData.canonical_company = { disconnect: true };
    }

    if (isAdmin || isAccountAdmin) {
      data = await prisma.$transaction(async (tx) => {
        const updateWhere = isAdmin
          ? { id: Number(companyId) }
          : { id: Number(companyId), account_id: String(req.account_id) };

        const updatedCompany = await tx[TABLE].update({
          where: updateWhere,
          data: {
            ...updateData,
            updated_at: new Date(),
            updated_by: req.uid,
            updated_proxied_by: req.ouid,
          },
        });

        const deleteProcessorWhere = isAdmin
          ? { company_str_id: finalCompanyStrId }
          : {
              company_str_id: finalCompanyStrId,
              account_id: String(req.account_id),
            };

        await tx.companies_processors.deleteMany({
          where: deleteProcessorWhere,
        });

        if (companies_processors.length > 0) {
          await limitConcurrency(
            async (proc) => {
              await tx.companies_processors.upsert({
                where: {
                  company_str_id_processor_str_id_account_id: {
                    company_str_id: finalCompanyStrId,
                    processor_str_id: proc.processor_str_id,
                    account_id: req.account_id,
                  },
                },
                update: {
                  import_status: proc.import_status || ImportStatuses.NONE,
                  updated_at: new Date(),
                  updated_by: req.uid,
                  updated_proxied_by: req.ouid,
                },
                create: {
                  company_str_id: finalCompanyStrId,
                  processor_str_id: proc.processor_str_id,
                  import_status: proc.import_status || ImportStatuses.NONE,
                  account_id: req.account_id,
                  created_at: new Date(),
                  created_by: req.uid,
                  created_proxied_by: req.ouid,
                  updated_at: new Date(),
                  updated_by: req.uid,
                  updated_proxied_by: req.ouid,
                },
              });
            },
            companies_processors,
            15
          );
        } else if (processor_str_ids.length > 0) {
          await limitConcurrency(
            async (processor_str_id) => {
              await tx.companies_processors.upsert({
                where: {
                  company_str_id_processor_str_id_account_id: {
                    company_str_id: finalCompanyStrId,
                    processor_str_id: processor_str_id,
                    account_id: req.account_id,
                  },
                },
                update: {
                  import_status: ImportStatuses.NONE,
                  updated_at: new Date(),
                  updated_by: req.uid,
                  updated_proxied_by: req.ouid,
                },
                create: {
                  company_str_id: finalCompanyStrId,
                  processor_str_id,
                  import_status: ImportStatuses.NONE,
                  account_id: req.account_id,
                  created_at: new Date(),
                  created_by: req.uid,
                  created_proxied_by: req.ouid,
                  updated_at: new Date(),
                  updated_by: req.uid,
                  updated_proxied_by: req.ouid,
                },
              });
            },
            processor_str_ids,
            15
          );
        }

        if (
          companies_document_profiles &&
          companies_document_profiles.length > 0
        ) {
          const newProfileIds = companies_document_profiles.map(
            (p) => p.document_profile_str_id
          );
          const deleteProfileWhere = isAdmin
            ? {
                company_str_id: finalCompanyStrId,
                document_profile_str_id: { notIn: newProfileIds },
              }
            : {
                company_str_id: finalCompanyStrId,
                account_id: String(req.account_id),
                document_profile_str_id: { notIn: newProfileIds },
              };

          await tx.companies_document_profiles.deleteMany({
            where: deleteProfileWhere,
          });

          await limitConcurrency(
            async (profileRelation) => {
              await tx.companies_document_profiles.upsert({
                where: {
                  document_profile_str_id_company_str_id: {
                    document_profile_str_id:
                      profileRelation.document_profile_str_id,
                    company_str_id: finalCompanyStrId,
                  },
                },
                update: {
                  auto_mapping_id: profileRelation.auto_mapping_id || null,
                  updated_at: new Date(),
                  updated_by: req.uid,
                  updated_proxied_by: req.ouid,
                },
                create: {
                  company_str_id: finalCompanyStrId,
                  document_profile_str_id:
                    profileRelation.document_profile_str_id,
                  auto_mapping_id: profileRelation.auto_mapping_id || null,
                  account_id: req.account_id,
                  created_at: new Date(),
                  created_by: req.uid,
                  created_proxied_by: req.ouid,
                  updated_at: new Date(),
                  updated_by: req.uid,
                  updated_proxied_by: req.ouid,
                },
              });
            },
            companies_document_profiles,
            15
          );
        } else if (profile_str_ids.length > 0) {
          const deleteProfileWhere = isAdmin
            ? {
                company_str_id: finalCompanyStrId,
                document_profile_str_id: { notIn: profile_str_ids },
              }
            : {
                company_str_id: finalCompanyStrId,
                account_id: String(req.account_id),
                document_profile_str_id: { notIn: profile_str_ids },
              };

          await tx.companies_document_profiles.deleteMany({
            where: deleteProfileWhere,
          });

          await limitConcurrency(
            async (profile_str_id) => {
              await tx.companies_document_profiles.upsert({
                where: {
                  document_profile_str_id_company_str_id: {
                    document_profile_str_id: profile_str_id,
                    company_str_id: finalCompanyStrId,
                  },
                },
                update: {
                  updated_at: new Date(),
                  updated_by: req.uid,
                  updated_proxied_by: req.ouid,
                },
                create: {
                  company_str_id: finalCompanyStrId,
                  document_profile_str_id: profile_str_id,
                  account_id: req.account_id,
                  created_at: new Date(),
                  created_by: req.uid,
                  created_proxied_by: req.ouid,
                  updated_at: new Date(),
                  updated_by: req.uid,
                  updated_proxied_by: req.ouid,
                },
              });
            },
            profile_str_ids,
            15
          );
        } else {
          const deleteProfileWhere = isAdmin
            ? { company_str_id: finalCompanyStrId }
            : {
                company_str_id: finalCompanyStrId,
                account_id: String(req.account_id),
              };

          await tx.companies_document_profiles.deleteMany({
            where: deleteProfileWhere,
          });
        }

        return updatedCompany;
      });
    }

    res.status(200).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error updating company: ${error}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const deleteCompany = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const { ids } = req.body;
  try {
    if (!Array.isArray(ids) || ids.length === 0) throw new Error('Missing ids');
    // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
      throw new Error('Invalid ids');

    const companiesToDelete = await prisma.companies.findMany({
      where: {
        id: { in: ids.map((id) => Number(id)) },
        account_id: String(req.account_id),
      },
      select: { str_id: true },
    });

    const companyStrIds = companiesToDelete.map((c) => c.str_id);

    await prisma.$transaction(async (tx) => {
      if (companyStrIds.length > 0) {
        await tx.companies_processors.deleteMany({
          where: {
            company_str_id: { in: companyStrIds },
            account_id: String(req.account_id),
          },
        });

        await tx.companies_document_profiles.deleteMany({
          where: {
            company_str_id: { in: companyStrIds },
            account_id: String(req.account_id),
          },
        });
      }

      await limitConcurrency(
        async (_id) => {
          return tx[TABLE].update({
            where: { id: Number(_id), account_id: String(req.account_id) },
            data: {
              state: DataStates.DELETED,
              updated_at: new Date(),
              updated_by: req.uid,
              updated_proxied_by: req.ouid,
            },
          });
        },
        ids,
        15
      );
    });

    res.status(200).json({ status: 'OK' });
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error deleting company: ${error}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};
