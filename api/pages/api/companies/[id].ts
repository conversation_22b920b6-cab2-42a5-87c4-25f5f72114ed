import * as Sentry from '@sentry/nextjs';

import prisma from '@/lib/prisma';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';

const handler = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  switch (req.method) {
    case 'GET':
      await getCompanies(req, res);
      break;
    default:
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
};

export default withAuth(handler);

export const _getCompaniesData = async (_req) => {
  const where = {
    OR: [{ state: 'active' }, { access: 'global', state: 'active' }],
  };

  const data = await prisma.companies.findMany({
    where,
    select: {
      id: true,
      str_id: true,
      company_name: true,
      email: true,
      alias_list: true,
    },
  });
  return data;
};

const getCompanies = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  try {
    const data = await _getCompaniesData(req);
    res.json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`An error occurred when getting companies: ${error.message}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};
