import * as Sentry from '@sentry/nextjs';
import { nanoid } from 'nanoid';
import type { NextApiRequest, NextApiResponse } from 'next';
import {
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
  createHandler,
} from 'next-api-decorators';

import { BaseHand<PERSON> } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { calculateSkipAndTake } from '@/prisma';

class Handler extends BaseHandler {
  @Get()
  async get(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      const data = await _getData(req);
      res.json(data);
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`An error occurred when getting data: ${error.message}`);
      Sentry.captureException(error);
      res.status(500).json({ error: error.message });
    }
  }

  @Post()
  async post(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await createOne(req, res);
  }

  @Patch()
  async patch(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await updateOne(req, res);
  }

  @Delete()
  async delete(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await deleteMany(req, res);
  }
}

export default withAuth(createHandler(Handler));

export const _getData = async (req: ExtNextApiRequest & NextApiRequest) => {
  const { page, limit, orderBy = 'created_at', sort = 'desc' } = req.query;
  const where = {
    account_id: req.account_id,
    state: { not: 'deleted' },
    str_id: req.query?.id,
    OR: [],
  };

  const _query = req.query.q as string;
  if (_query) {
    where.OR = [
      {
        document_str_id: {
          contains: _query,
          mode: 'insensitive',
        },
      },
    ];
  }

  const { take, skip } = calculateSkipAndTake({ page, limit });

  const data = await prisma.data_imports.findMany({
    where,
    orderBy: { [orderBy]: sort },
    skip,
    take,
    include: {
      company: {
        select: {
          str_id: true,
          company_name: true,
        },
      },
      processor: {
        select: {
          str_id: true,
          name: true,
        },
      },
      mapping: {
        select: {
          str_id: true,
          name: true,
        },
      },
    },
  });
  const count = await prisma.data_imports.count({ where });
  return {
    count,
    data,
  };
};

// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
export const createImportsData = async (req: ExtNextApiRequest, data: any) => {
  try {
    const {
      document_str_id,
      company_str_id,
      mapping_str_id,
      processor_str_id,
      ...cleanedData
    } = data;

    const result = await prisma.data_imports.create({
      data: {
        str_id: nanoid(),
        account_id: req.account_id,
        created_by: req.uid,
        created_proxied_by: req.ouid,
        created_at: new Date(),
        ...cleanedData,
        ...(document_str_id && {
          document: { connect: { str_id: document_str_id } },
        }),
        ...(company_str_id && {
          company: { connect: { str_id: company_str_id } },
        }),
        ...(mapping_str_id && {
          mapping: { connect: { str_id: mapping_str_id } },
        }),
        ...(processor_str_id && {
          processor: { connect: { str_id: processor_str_id } },
        }),
      },
    });
    return {
      data: result,
    };
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error creating new record: ${error.message}`);
    Sentry.captureException(error);
    return { error: error.message };
  }
};

const createOne = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const body = req.body;
  const result = await createImportsData(req, body);
  if (result.error) {
    req.logger.error('Error creating data', result.error);
    res.status(500).json({ error: result.error });
  } else {
    res.status(200).json(result.data);
  }
};

const updateOne = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const body = req.body;
  try {
    const { id, ..._body } = body;
    if (!id) throw new Error('Missing id');
    let data = {};
    data = await prisma.data_imports.update({
      where: { id, account_id: String(req.account_id) },
      data: {
        ..._body,
        updated_at: new Date(),
        updated_by: req.uid,
        updated_proxied_by: req.ouid,
      },
    });
    res.status(200).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error updating data: ${error}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

const deleteMany = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const { ids } = req.body;
  try {
    if (!Array.isArray(ids) || ids.length === 0) throw new Error('Missing ids');
    // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
      throw new Error('Invalid ids');
    const promises = ids.map((_id) => {
      return prisma.data_imports.update({
        where: { id: Number(_id), account_id: String(req.account_id) },
        data: {
          state: 'deleted',
          updated_at: new Date(),
          updated_by: req.uid,
          updated_proxied_by: req.ouid,
        },
      });
    });
    await prisma.$transaction(promises);
    res.status(200).json({ status: 'OK' });
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error deleting data: ${error}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};
