import * as Sentry from '@sentry/nextjs';
import { nanoid } from 'nanoid';

import prisma from '@/lib/prisma';
import { withAuth } from '@/lib/middlewares';
import {
  type ExtNextApiRequest,
  type ExtNextApiResponse,
  AccountAccessLevels,
} from '@/types';

enum CommentEntityType {
  account = 'account',
  processor = 'processor',
}

const handler = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  switch (req.method) {
    case 'GET':
      await queryMany(req, res);
      break;
    case 'POST':
      await createOne(req, res);
      break;
    case 'PATCH':
      await patchOne(req, res);
      break;
    case 'DELETE':
      await deleteOnCondition(req, res);
      break;
    default:
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
};

export const getComments = async (req) => {
  const where = {
    processor_str_id: undefined,
    account_str_id: undefined,
    state: 'active',
    OR: undefined,
  };

  if (!req.isFintaryAdmin) {
    where.OR = [
      {
        account_id: req.account_id,
      },
      {
        access: AccountAccessLevels.GLOBAL,
      },
    ];
  }

  const { entity, id } = req.query;
  if (entity === CommentEntityType.processor) {
    where.processor_str_id = id;
  } else if (entity === CommentEntityType.account) {
    where.account_str_id = id;
  }

  const data = await prisma.comments.findMany({
    where,
    orderBy: {
      created_at: 'asc',
    },
  });

  return data;
};

const queryMany = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const data = await getComments(req);
  res.json(data);
};

const createOne = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const { uid: _uid, ...post } = req.body;
  try {
    const data = await prisma.comments.create({
      data: {
        ...post,
        user_str_id: req.ouid || req.uid,
        created_proxied_by: req.ouid,
        str_id: nanoid(),
        created_at: new Date(),
      },
    });
    res.status(201).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(error);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

// PATCH /comments
const patchOne = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const body = req.body;
  const { id } = body;
  try {
    if (!id) throw new Error('Missing id');
    const data = await prisma.comments.update({
      where: { id },
      data: {
        ...body,
        updated_proxied_by: req.ouid,
      },
    });
    res.status(200).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(error);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

// DELETE /comments
const deleteOnCondition = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  try {
    const { ids } = req.body || {};
    const existMappings = await prisma.comments.findMany({
      where: { id: { in: ids } },
    });
    const existIds = existMappings.map((r) => r.id);
    const resp = await prisma.comments.updateMany({
      where: { id: { in: existIds } },
      data: {
        state: 'deleted',
      },
    });
    res.status(200).json(resp);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(error);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

export default withAuth(handler);
