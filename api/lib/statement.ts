import type { Prisma } from '@prisma/client';
import { policyDataIfEmptyFields } from 'common/constants/report_data';

import FieldValuesCache from '@/lib/field-values/FieldValuesCache';
import normalizeFieldValues from '@/lib/field-values/normalizeFieldValues';
import prisma from '@/lib/prisma';
import type { ExtNextApiRequest } from '@/types';

const filterFieldsCache = new FieldValuesCache('statement_data');
export const queryFieldValues = async (
  account_id: string,
  filterList: string[],
  where: Prisma.statement_dataWhereInput
) => {
  const values = await filterFieldsCache.get(
    account_id,
    filterList.join(','),
    where,
    async () => {
      const reportSelect = policyDataIfEmptyFields.filter((key) =>
        filterList.includes(key)
      );
      const statementData = await prisma.statement_data.findMany({
        select: {
          ...Object.fromEntries(filterList.map((key) => [key, true])),
          report:
            reportSelect.length > 0
              ? {
                  select: {
                    ...Object.fromEntries(
                      reportSelect.map((key) => [key, true])
                    ),
                  },
                }
              : undefined,
        },
        where,
      });

      const data = statementData.reduce((acc, item) => {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        filterList.forEach((key) => {
          acc[key] = acc[key] || [];
          acc[key].push(item[key]);
          if (item?.report?.[key]) {
            acc[key].push(item?.report?.[key]);
          }
        });
        return acc;
      }, {});
      const result = Object.entries(data).map(([key, value]) => {
        return [key, normalizeFieldValues(value as string[])];
      });

      return Object.fromEntries(result);
    }
  );

  return values;
};

/**
 * Base filter options
 */
export function loadFilterOption(
  req: ExtNextApiRequest,
  excludeNullDates = false
) {
  const {
    query: { q = '', id = '' },
  } = req;

  const globalWhere = {
    AND: [
      { account_id: String(req.account_id) },
      { OR: [{ state: 'active' }] },
      {
        OR: [
          { account_type: { contains: q, mode: 'insensitive' } },
          { agent_name: { contains: q, mode: 'insensitive' } },
          { aggregation_id: { contains: q, mode: 'insensitive' } },
          { carrier_name: { contains: q, mode: 'insensitive' } },
          { compensation_type: { contains: q, mode: 'insensitive' } },
          { customer_name: { contains: q, mode: 'insensitive' } },
          { document_id: { contains: q, mode: 'insensitive' } },
          { group_id: { contains: q, mode: 'insensitive' } },
          { internal_id: { contains: q, mode: 'insensitive' } },
          { notes: { contains: q, mode: 'insensitive' } },
          { payment_status: { contains: q, mode: 'insensitive' } },
          { policy_id: { contains: q, mode: 'insensitive' } },
          { product_name: { contains: q, mode: 'insensitive' } },
          { product_type: { contains: q, mode: 'insensitive' } },
          { str_id: { contains: q, mode: 'insensitive' } },
          { writing_carrier_name: { contains: q, mode: 'insensitive' } },
          { import_id: { contains: q, mode: 'insensitive' } },
        ],
      },
      { payment_date: undefined },
      { processing_date: undefined },
      { invoice_date: undefined },
      { effective_date: undefined },
      { str_id: undefined },
    ],
  };

  if (excludeNullDates) {
    globalWhere.AND[3].payment_date = { not: null };
  }

  if (id) {
    globalWhere.AND[4].str_id = id;
  }

  return JSON.parse(JSON.stringify(globalWhere));
}
export const buildGlobalWhere = (
  accountId: string,
  contactAndChildrenStrIds: string[],
  contact_str_id: string | undefined,
  q: string,
  id: string | undefined,
  incl_dupes: boolean,
  incl_linked: boolean,
  currentRecord: { id: number; parent_id: number } = null,
  show_allocated_commissions: boolean
) => {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const globalWhere: { AND: any[] } = {
    AND: [
      { account_id: accountId },
      { OR: [{ state: 'active' }] },
      { id: undefined, str_id: undefined },
      {
        OR: [
          {
            contacts:
              contactAndChildrenStrIds.length > 0
                ? { hasSome: contactAndChildrenStrIds }
                : contact_str_id
                  ? { has: contact_str_id }
                  : undefined,
          },
          ...(contactAndChildrenStrIds.length > 0
            ? contactAndChildrenStrIds.map((strId) => ({
                agent_commissions: { path: [strId], not: null },
              }))
            : contact_str_id
              ? [{ agent_commissions: { path: [contact_str_id], not: null } }]
              : []),
        ],
      },
    ],
  };

  if (q) {
    globalWhere.AND.push({
      OR: [
        { account_type: { contains: q, mode: 'insensitive' } },
        { agent_name: { contains: q, mode: 'insensitive' } },
        { carrier_name: { contains: q, mode: 'insensitive' } },
        { compensation_type: { contains: q, mode: 'insensitive' } },
        { customer_name: { contains: q, mode: 'insensitive' } },
        { import_id: { contains: q, mode: 'insensitive' } },
        { document_id: { contains: q, mode: 'insensitive' } },
        { group_id: { contains: q, mode: 'insensitive' } },
        { internal_id: { contains: q, mode: 'insensitive' } },
        { notes: { contains: q, mode: 'insensitive' } },
        { payment_status: { contains: q, mode: 'insensitive' } },
        { policy_id: { contains: q, mode: 'insensitive' } },
        { product_name: { contains: q, mode: 'insensitive' } },
        { product_type: { contains: q, mode: 'insensitive' } },
        { str_id: { contains: q, mode: 'insensitive' } },
        { transaction_type: { contains: q, mode: 'insensitive' } },
        { writing_carrier_name: { contains: q, mode: 'insensitive' } },
        { report: { internal_id: { contains: q, mode: 'insensitive' } } },
        { report: { notes: { contains: q, mode: 'insensitive' } } },
        { report: { policy_id: { contains: q, mode: 'insensitive' } } },
        { report: { product_type: { contains: q, mode: 'insensitive' } } },
        {
          report: { transaction_type: { contains: q, mode: 'insensitive' } },
        },
        {
          report: {
            product_name: { contains: q, mode: 'insensitive' },
          },
        },
      ],
    });
  }

  if (id || incl_dupes || incl_linked || show_allocated_commissions) {
    const orStates = [{ state: 'active' }];
    if (incl_dupes) {
      orStates.push({ state: 'duplicate' });
    }
    if (incl_linked) {
      orStates.push({ state: 'grouped' });
    }
    if (show_allocated_commissions) {
      orStates.push({ state: 'allocated' });
    }

    if (id) {
      if (/^\d+$/.test(id)) {
        globalWhere.AND[3].id = +id;
      } else {
        if (currentRecord) {
          if (currentRecord.parent_id) {
            globalWhere.AND[3].OR.push({ id: currentRecord.parent_id });
            globalWhere.AND[3].OR.push({ parent_id: currentRecord.parent_id });
          } else {
            globalWhere.AND[3].OR.push({ parent_id: currentRecord.id });
          }
          globalWhere.AND[3].OR.push({ str_id: id });
        } else {
          globalWhere.AND[3].str_id = id;
        }
      }
      orStates.push({ state: 'duplicate' });
      orStates.push({ state: 'grouped' });
    }
    globalWhere.AND[1] = {
      OR: orStates,
    };
  }

  return globalWhere;
};

// Function to filter out specific fields
const filterFields = (
  fields: Record<string, boolean>,
  keysToRemove: string[]
) => {
  return Object.keys(fields)
    .filter((key) => !keysToRemove.includes(key))
    .reduce((obj, key) => {
      obj[key] = fields[key];
      return obj;
    }, {});
};

export const getSelectStatement = async (commissionsAccountSettings) => {
  let selectStatement = {};
  const selectedFields = {
    str_id: true,
    created_at: true,
    created_by: true,
    updated_at: true,
    updated_by: true,
    state: true,
    document_id: true,
    reconciliation_status: true,
    import_id: true,
    agent_payout_rate_override: true,
    flags: true,
    flags_log: true,
    group_name: true,
    is_virtual: true,
    virtual_type: true,
    master_id: true,
    transaction_type: true,
  };

  const keysToRemove = [
    'comp_calc',
    'comp_calc_log',
    'comp_calc_status',
    'receivable_value_agency_rate',
    'receivable_value_agent_rate',
    'receivable_value_override_rate',
    'expected_result',
  ];

  if (
    Array.isArray(
      commissionsAccountSettings?.pages_settings?.commissions?.fields
    ) &&
    commissionsAccountSettings?.pages_settings?.commissions?.fields.length > 0
  ) {
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    commissionsAccountSettings?.pages_settings?.commissions?.fields.forEach(
      (item: string) => {
        selectedFields[item] = true;
      }
    );

    selectStatement = filterFields(selectedFields, keysToRemove);
  } else {
    const commissions_fields = await prisma.fields.findMany({
      where: {
        model: 'statements',
        state: 'active',
      },
      select: {
        key: true,
      },
    });
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    commissions_fields.forEach((field: { key: string }) => {
      selectedFields[field.key] = true;
    });

    selectStatement = filterFields(selectedFields, keysToRemove);
  }
  return selectStatement;
};
