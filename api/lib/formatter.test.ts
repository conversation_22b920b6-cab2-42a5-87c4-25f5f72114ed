import { describe, expect, it } from 'vitest';
import { faker } from '@faker-js/faker';

import Formatter from '@/lib/Formatter';

describe('Formatter', () => {
  describe('asPercentage', () => {
    const inputs = [
      { input: 0.545, expected: '54.50%' },
      { input: 0.1, expected: '10.00%' },
      { input: -0.15, expected: '-15.00%' },
      { input: -0.855505, expected: '-85.55%' },
      { input: -440, expected: '-44000.00%' },
      { input: -0, expected: '0.00%' },
      { input: 50, expected: '5000.00%' },
      { input: 10070, expected: '1007000.00%' },
      { input: 1213921932913912, expected: '121392193291391200.00%' },
    ];

    it.each(inputs)(
      'should format the input correctly to the expected result: %s',
      ({ input, expected }) => {
        const result = Formatter.asPercentage(input);

        expect(result).toEqual(expected);
      }
    );

    it('should not change the input when it is formatted', () => {
      const random = faker.helpers.arrayElement(inputs);
      const inputCopied = Object.is(random.input, -0)
        ? -0
        : Number.parseFloat(`${random.input}`);

      Formatter.asPercentage(-0);

      expect(random.input).toEqual(inputCopied);
    });

    describe('invalid inputs', () => {
      const invalidInputs = [
        null,
        'string',
        [1, 2, 3],
        { a: 1 },
        new Date(),
        () => {
          return null;
        },
        new Error('an error'),
      ];

      it.each(invalidInputs)(
        'should return same input when it is not a number: %s',
        (input) => {
          const result = Formatter.asPercentage(input);

          expect(result).toEqual(input);
        }
      );
    });
  });

  describe('date', () => {
    it('should return the date correctly when input is an date object', () => {
      const result = Formatter.date(new Date(2025, 5, 7));

      expect(result).toEqual('06/07/2025');
    });

    describe('when inputs is number ', () => {
      const input = 45700;

      it('should return the date correctly input a number representing days', () => {
        const result = Formatter.date(input);

        expect(result).toEqual('02/12/2025');
      });

      it('should return the date correctly when startOfMonth is present in options', () => {
        const result = Formatter.date(input, { startOfMonth: true });

        expect(result).toEqual('02/01/2025');
      });
    });

    describe('when input is string', () => {
      const inputs = [
        { input: '02/12/2025', expected: '02/12/2025' },
        { input: '2025-12-02', expected: '12/02/2025' },
        { input: '2025/12/02', expected: '12/02/2025' },
        { input: '2025.12.02', expected: '12/02/2025' },
        { input: '2025-12-02T00:00:00Z', expected: '12/02/2025' },
        { input: '2025-12-02T00:00:00+00:00', expected: '12/02/2025' },
        { input: '2025-12-02T00:00:00+01:00', expected: '12/01/2025' },
      ];

      it.each(inputs)(
        'should return the date correctly for the input: %s',
        ({ input, expected }) => {
          const result = Formatter.date(input);

          expect(result).toEqual(expected);
        }
      );

      it('should return the date correctly when startOfMonth is present in options', () => {
        const result = Formatter.date('02/12/2025', { startOfMonth: true });

        expect(result).toEqual('02/01/2025');
      });
    });

    describe('invalid inputs', () => {
      it('should return empty value when input is empty', () => {
        expect(Formatter.date(undefined)).toEqual('');
        expect(Formatter.date(null)).toEqual('');
        expect(Formatter.date('')).toEqual('');
      });
    });
  });

  describe('contacts', () => {
    const inputs = [
      {
        input: {
          ids: [1],
          options: [
            { str_id: '1', id: 2, first_name: 'John', last_name: 'Doe' },
          ],
        },
        expected: ['John Doe'],
      },
      {
        input: {
          ids: [1],
          options: [
            { str_id: '1', id: 2, first_name: 'Doe', last_name: 'John' },
          ],
          userId: true,
        },
        expected: ['Doe John'],
      },
      {
        input: {
          ids: [10],
          options: [
            { str_id: '1', id: 2, first_name: 'Doe', last_name: 'John' },
          ],
        },
        expected: [''],
      },
      {
        input: {
          ids: [10],
          options: [
            { str_id: '1', id: 2, first_name: 'Doe', last_name: 'John' },
          ],
          userId: true,
        },
        expected: [''],
      },
    ];

    it.each(inputs)(
      'should return the contacts formatted correctly for the input: %s',
      ({ input, expected }) => {
        const result = Formatter.contacts(input.ids, input.options);

        expect(result).toEqual(expected);
      }
    );

    it('should return an empty array when is an invalid input', () => {
      expect(Formatter.contacts(undefined)).toEqual([]);
      expect(Formatter.contacts(null)).toEqual([]);
      expect(Formatter.contacts([])).toEqual([]);
    });
  });

  describe('statementDocuments', () => {
    it('should return the contacts formatted correctly for the input: %s', () => {
      const input = {
        str_id: 1,
        options: [{ str_id: '1', filename: 'file-name-1.txt' }],
      };
      const expected = input.options[0].filename;

      const result = Formatter.statementDocuments(input.str_id, input.options);

      expect(result).toEqual(expected);
    });

    it('should return an empty value when options is an empty list', () => {
      const result = Formatter.statementDocuments(1, []);

      expect(result).toEqual('');
    });
  });

  describe('reportDocuments', () => {
    it('should return the contacts formatted correctly for the input: %s', () => {
      const input = {
        str_id: 1,
        options: [{ str_id: '1', filename: 'file-name-1.txt' }],
      };
      const expected = input.options[0].filename;

      const result = Formatter.statementDocuments(input.str_id, input.options);

      expect(result).toEqual(expected);
    });

    it('should return an empty value when options is an empty list', () => {
      const result = Formatter.statementDocuments(1, []);

      expect(result).toEqual('');
    });
  });

  describe('fieldMatcher', () => {
    it('should return the criteria formatted correctly', () => {
      const result = Formatter.fieldMatcher({
        field: 'field_name',
        op: 'and',
        value: 'something',
      });

      expect(result).toEqual('"field_name" and "something"');
    });
  });

  describe('type', () => {
    const inputs = [
      {
        input: ['hello there', 'test be doing'],
        expected: 'Hello there',
      },
      {
        input: {
          name: 'hi',
        },
        expected: '{"name":"hi"}',
      },
      {
        input: 'hi there',
        expected: 'Hi there',
      },
    ];

    it.each(inputs)(
      'should return the contacts formatted correctly for the input: %s',
      ({ input, expected }) => {
        const result = Formatter.type(input);

        expect(result).toEqual(expected);
      }
    );

    it('should return an empty value when is an invalid input', () => {
      expect(Formatter.type(undefined)).toEqual('');
      expect(Formatter.type(null)).toEqual('');
      expect(Formatter.type('')).toEqual('');
    });
  });

  describe('dateStringToLocaleString', () => {
    it('should format the date as string to iso string', () => {
      const result = Formatter.dateStringToLocaleString(
        new Date(2025, 5, 7).toString()
      );

      expect(result).toEqual('2025-06-07T00:00:00.000Z');
    });

    it('should return an empty value when is an invalid input', () => {
      expect(Formatter.type(undefined)).toEqual('');
      expect(Formatter.type(null)).toEqual('');
      expect(Formatter.type('')).toEqual('');
    });
  });

  describe('getDynamicSelectFormatter', () => {
    it('should format the date as string to iso string', () => {
      const result = Formatter.dateStringToLocaleString(
        new Date(2025, 5, 7).toString()
      );

      expect(result).toEqual('2025-06-07T00:00:00.000Z');
    });

    it('should return an empty value when is an invalid input', () => {
      expect(Formatter.type(undefined)).toEqual('');
      expect(Formatter.type(null)).toEqual('');
      expect(Formatter.type('')).toEqual('');
    });
  });

  describe('criteriaFormatter', () => {
    it('should format the input correctly', () => {
      const input = {
        company: {
          company_name: 'fintary',
        },
        comp_grid_product: { type: 'test' },
      };

      const expected =
        'Company: fintary • test • n/a • Years (any) • Ages (any)';

      const result = Formatter.criteriaFormatter(input);

      expect(result).toEqual(expected);
    });

    it('should return an empty value when is an invalid input', () => {
      expect(Formatter.type(undefined)).toEqual('');
      expect(Formatter.type(null)).toEqual('');
      expect(Formatter.type('')).toEqual('');
    });
  });
});
