import { createMiddlewareDecorator } from 'next-api-decorators';

import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { requestFilter } from '@/lib/middlewares';

export const WithAuth = (options) => {
  return createMiddlewareDecorator(
    async (req: ExtNextApiRequest, res: ExtNextApiResponse, next) => {
      await requestFilter(req, res, options);
      next();
    }
  );
};
