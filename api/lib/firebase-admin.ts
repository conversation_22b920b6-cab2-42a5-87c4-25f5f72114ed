import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { getStorage } from 'firebase-admin/storage';

const isProduction = process.env.PROJECT_ID === 'fintary-prod';
const useEmulator =
  !isProduction && process.env.USE_FIREBASE_EMULATOR === 'true';

if (!getApps().length) {
  if (useEmulator) {
    // When using emulators, we don't need service account credentials.
    // The SDK will automatically connect to the emulators if the
    // FIREBASE_*_EMULATOR_HOST environment variables are set.
    initializeApp({
      // A project ID is required, but it can be a dummy one for emulators.
      projectId: process.env.PROJECT_ID || 'fintary-dev',
    });
  } else {
    initializeApp({
      credential: cert(JSON.parse(process.env.FIREBASE_JSON)),
    });
  }
}

const auth = getAuth();
const firestore = getFirestore();
const storage = getStorage().bucket(process.env.STORAGE_BUCKET);

export * from 'firebase-admin/app';
export * from 'firebase-admin/auth';
export * from 'firebase-admin/firestore';
export * from 'firebase-admin/storage';
export { auth, firestore, storage };
