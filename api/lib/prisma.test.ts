import { describe, it, expect, vi, beforeEach } from 'vitest';

import {
  checkIfStateIsPresent,
  OPTIONAL_ARGS_OPERATIONS,
  prismaClient,
} from '@/lib/prisma';
import { asyncLocalStorage } from '@/services/logger/appLogger';
import { testGuard } from '@/lib/testGuard';
import { DataStates } from '@/types';

describe('Prisma', () => {
  describe('checkIfStateIsPresent', () => {
    it('should detect state in deeply nested AND/OR queries', () => {
      const query = {
        AND: [
          { account_id: 'XN9U5UtGrD5aovyEwNlHj' },
          {
            AND: [
              { account_id: 'XN9U5UtGrD5aovyEwNlHj' },
              {
                OR: [
                  { state: DataStates.ACTIVE },
                  { state: DataStates.GROUPED },
                ],
              },
            ],
          },
        ],
      };

      expect(checkIfStateIsPresent(query)).toBe(true);
    });

    it('should return false when no state is present in complex query', () => {
      const query = {
        AND: [
          { account_id: 'XN9U5UtGrD5aovyEwNlHj' },
          {
            OR: [
              { account_type: { contains: '********' } },
              { agent_name: { contains: '********' } },
            ],
          },
        ],
      };

      expect(checkIfStateIsPresent(query)).toBe(false);
    });

    it('should handle empty objects and arrays', () => {
      const query = {
        AND: [{}, { OR: [] }, { AND: [{}] }],
      };

      expect(checkIfStateIsPresent(query)).toBe(false);
    });

    it('should handle nested report objects', () => {
      const query = {
        AND: [
          {
            report: {
              OR: [
                { state: DataStates.ACTIVE },
                { internal_id: { contains: '********' } },
              ],
            },
          },
        ],
      };

      expect(checkIfStateIsPresent(query)).toBe(true);
    });

    it('should handle null and undefined values', () => {
      const query = {
        AND: [
          { field: null },
          { other_field: undefined },
          { OR: [{ state: DataStates.ACTIVE }] },
        ],
      };

      expect(checkIfStateIsPresent(query)).toBe(true);
    });
  });

  // The following tests are skipped because they need database connection, skip it for online building
  testGuard(() => {
    describe('Proxy', () => {
      beforeEach(async () => {
        vi.clearAllMocks();
        await Promise.all([
          prismaClient.statement_data.deleteMany(),
          prismaClient.data_processing.deleteMany(),
          prismaClient.documents.deleteMany(),
        ]);
      });
      describe('Account Inject', () => {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        OPTIONAL_ARGS_OPERATIONS.forEach((operation) => {
          it(`should inject account_id into the ${operation} query`, async () => {
            await asyncLocalStorage.run(
              {
                traceId: '123',
                account: {
                  uid: '',
                  ouid: '',
                  account_id: 'XN9U5UtGrD5aovyEwNlHj',
                  role_id: '1',
                },
              },
              async () => {
                const spy = vi.spyOn(prismaClient.statement_data, operation);
                const where = { state: DataStates.ACTIVE };
                // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                await (prismaClient.statement_data as any)[operation]({
                  where,
                });
                expect(spy).toHaveBeenCalledWith({
                  where: {
                    AND: [
                      {
                        account_id: 'XN9U5UtGrD5aovyEwNlHj',
                      },
                      where,
                    ],
                  },
                });
              }
            );
          });

          it(`should throw error if account_id is not present in the ${operation} query`, async () => {
            await asyncLocalStorage.run(
              {
                traceId: '123',
                account: {
                  uid: '',
                  ouid: '',
                  account_id: '',
                  role_id: '1',
                },
              },
              async () => {
                const where = { state: DataStates.ACTIVE };
                await expect(async () => {
                  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  await (prismaClient.statement_data as any)[operation]({
                    where,
                  });
                }).rejects.toThrowError(/Account ID is required/);
              }
            );
          });
        });
      });
      describe('Created & Updated Fields', () => {
        describe('Create', () => {
          it(`should inject created_by and updated_by fields into the create query`, async () => {
            const spy = vi.spyOn(prismaClient.statement_data, 'create');
            await asyncLocalStorage.run(
              {
                traceId: '123',
                account: {
                  uid: '1',
                  ouid: '2',
                  account_id: 'XN9U5UtGrD5aovyEwNlHj',
                  role_id: '1',
                },
              },
              async () => {
                await prismaClient.statement_data.create({
                  data: { state: DataStates.ACTIVE },
                });
                expect(spy).toHaveBeenCalledWith({
                  data: {
                    created_by: '1',
                    updated_by: '1',
                    state: DataStates.ACTIVE,
                    created_proxied_by: '2',
                    updated_proxied_by: '2',
                  },
                });
              }
            );
          });

          it(`should not override created_by & updated_by fields for create operation if they are provided`, async () => {
            const spy = vi.spyOn(prismaClient.statement_data, 'create');
            await asyncLocalStorage.run(
              {
                traceId: '123',
                account: {
                  uid: '1',
                  ouid: '2',
                  account_id: 'XN9U5UtGrD5aovyEwNlHj',
                  role_id: '1',
                },
              },
              async () => {
                await prismaClient.statement_data.create({
                  data: {
                    state: DataStates.ACTIVE,
                    created_by: '3',
                    created_proxied_by: '4',
                    updated_by: '3',
                    updated_proxied_by: '4',
                  },
                });
                expect(spy).toHaveBeenCalledWith({
                  data: {
                    created_by: '3',
                    created_proxied_by: '4',
                    updated_by: '3',
                    updated_proxied_by: '4',
                    state: DataStates.ACTIVE,
                  },
                });
              }
            );
          });

          describe('Handle relationnal fields', () => {
            it('connect to a relationnal field', async () => {
              const spy = vi.spyOn(prismaClient.data_processing, 'create');
              await asyncLocalStorage.run(
                {
                  traceId: '123',
                  account: {
                    uid: '1',
                    ouid: '2',
                    account_id: 'XN9U5UtGrD5aovyEwNlHj',
                    role_id: '1',
                  },
                },
                async () => {
                  await prismaClient.data_processing.create({
                    data: {
                      status: DataStates.ACTIVE,
                      type: 'agent_commission_calc',
                    },
                  });
                  expect(spy).toHaveBeenCalledWith({
                    data: {
                      user: { connect: { uid: '1' } },
                      updated_by: '1',
                      status: DataStates.ACTIVE,
                      type: 'agent_commission_calc',
                      proxy_user: { connect: { uid: '2' } },
                      updated_proxied_by: '2',
                    },
                  });
                }
              );
            });
            it('connect to a relationnal field with other relational fields', async () => {
              const spy = vi.spyOn(prismaClient.documents, 'create');
              await asyncLocalStorage.run(
                {
                  traceId: '123',
                  account: {
                    uid: '1',
                    ouid: '2',
                    account_id: 'XN9U5UtGrD5aovyEwNlHj',
                    role_id: '1',
                  },
                },
                async () => {
                  await prismaClient.documents.create({
                    data: {
                      account_id: '123',
                      company_str_id: '123',
                      type: 'agent_commission_calc',
                      validations: {
                        is_valid: true,
                      },
                    },
                  });
                  expect(spy).toHaveBeenCalledWith({
                    data: {
                      account_id: '123',
                      company_str_id: '123',
                      type: 'agent_commission_calc',
                      created_proxied_by: '2',
                      updated_proxied_by: '2',
                      validations: {
                        is_valid: true,
                      },
                    },
                  });
                }
              );
            });
          });
        });
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        ['createMany', 'createManyAndReturn'].forEach((operation) => {
          it(`should inject created_by & created_proxied_by fields into the ${operation} query`, async () => {
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            const spy = vi.spyOn(prismaClient.statement_data, operation as any);
            await asyncLocalStorage.run(
              {
                traceId: '123',
                account: {
                  uid: '1',
                  ouid: '2',
                  account_id: 'XN9U5UtGrD5aovyEwNlHj',
                  role_id: '1',
                },
              },
              async () => {
                await prismaClient.statement_data[operation]({
                  data: [
                    { state: DataStates.ACTIVE },
                    { state: DataStates.ACTIVE },
                  ],
                });
                expect(spy).toHaveBeenCalledWith({
                  data: [
                    {
                      created_by: '1',
                      created_proxied_by: '2',
                      state: DataStates.ACTIVE,
                    },
                    {
                      created_by: '1',
                      created_proxied_by: '2',
                      state: DataStates.ACTIVE,
                    },
                  ],
                });
              }
            );
          });

          it(`should not inject created_by & created_proxied_by fields into the ${operation} query if no account is present`, async () => {
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            const spy = vi.spyOn(prismaClient.statement_data, operation as any);
            await prismaClient.statement_data[operation]({
              data: [{ state: DataStates.ACTIVE }],
            });
            expect(spy).toHaveBeenCalledWith({
              data: [{ state: DataStates.ACTIVE }],
            });
          });
          it(`should not override created_by & created_proxied_by fields if they are provided`, async () => {
            await asyncLocalStorage.run(
              {
                traceId: '123',
                account: {
                  uid: '1',
                  ouid: '2',
                  account_id: 'XN9U5UtGrD5aovyEwNlHj',
                  role_id: '1',
                },
              },
              async () => {
                const spy = vi.spyOn(
                  prismaClient.statement_data,
                  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  operation as any
                );
                await prismaClient.statement_data[operation]({
                  data: [
                    {
                      state: DataStates.ACTIVE,
                      created_by: '3',
                      created_proxied_by: '4',
                    },
                  ],
                });

                expect(spy).toHaveBeenCalledWith({
                  data: [
                    {
                      state: DataStates.ACTIVE,
                      created_by: '3',
                      created_proxied_by: '4',
                    },
                  ],
                });
              }
            );
          });
          it('not connect to a relationnal field', async () => {
            const spy = vi.spyOn(
              prismaClient.data_processing,
              // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              operation as any
            );
            await asyncLocalStorage.run(
              {
                traceId: '123',
                account: {
                  uid: '1',
                  ouid: '2',
                  account_id: 'XN9U5UtGrD5aovyEwNlHj',
                  role_id: '1',
                },
              },
              async () => {
                await prismaClient.data_processing[operation]({
                  data: [
                    {
                      status: DataStates.ACTIVE,
                      type: 'agent_commission_calc',
                    },
                    {
                      status: DataStates.ACTIVE,
                      type: 'agent_commission_calc',
                    },
                  ],
                });
                expect(spy).toHaveBeenCalledWith({
                  data: [
                    {
                      created_by: '1',
                      created_proxied_by: '2',
                      status: DataStates.ACTIVE,
                      type: 'agent_commission_calc',
                    },
                    {
                      created_by: '1',
                      created_proxied_by: '2',
                      status: DataStates.ACTIVE,
                      type: 'agent_commission_calc',
                    },
                  ],
                });
              }
            );
          });
        });
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        ['update', 'updateMany'].forEach((operation) => {
          it(`should inject updated_by & updated_proxied_by fields into the ${operation} query`, async () => {
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            const spy = vi.spyOn(prismaClient.statement_data, operation as any);
            // Mock the create operation to return a fake statement data
            const mockedStatementData = {
              id: 1,
              state: DataStates.ACTIVE,
              policy_id: '123',
              created_by: '1',
              updated_by: '1',
              // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            } as any;
            vi.spyOn(prismaClient.statement_data, 'create').mockResolvedValue(
              mockedStatementData
            );

            await asyncLocalStorage.run(
              {
                traceId: '123',
                account: {
                  uid: '1',
                  ouid: '2',
                  account_id: 'XN9U5UtGrD5aovyEwNlHj',
                  role_id: '1',
                },
              },
              async () => {
                const statementData = await prismaClient.statement_data.create({
                  data: { state: DataStates.ACTIVE, policy_id: '123' },
                });
                expect(statementData.created_by).toBe('1');
                // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                await (prismaClient.statement_data[operation] as any)({
                  where: { id: statementData.id },
                  data: { state: DataStates.ACTIVE },
                });
                expect(spy).toHaveBeenCalledWith({
                  where: { id: 1 },
                  data: {
                    updated_by: '1',
                    state: DataStates.ACTIVE,
                    updated_proxied_by: '2',
                  },
                });
              }
            );
          });
          it(`should not inject updated_by & updated_proxied_by fields into the ${operation} query if no account is present`, async () => {
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            const spy = vi.spyOn(prismaClient.statement_data, operation as any);
            await prismaClient.statement_data[operation]({
              data: { state: DataStates.ACTIVE },
            });
            expect(spy).toHaveBeenCalledWith({
              data: { state: DataStates.ACTIVE },
            });
          });
          it(`should not override updated_by & updated_proxied_by fields if they are provided`, async () => {
            await asyncLocalStorage.run(
              {
                traceId: '123',
                account: {
                  uid: '1',
                  ouid: '2',
                  account_id: 'XN9U5UtGrD5aovyEwNlHj',
                  role_id: '1',
                },
              },
              async () => {
                const spy = vi.spyOn(
                  prismaClient.statement_data,
                  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  operation as any
                );
                await prismaClient.statement_data[operation]({
                  where: { id: 1 },
                  data: {
                    state: DataStates.ACTIVE,
                    updated_by: '1',
                    updated_proxied_by: '3',
                  },
                });
                expect(spy).toHaveBeenCalledWith({
                  where: { id: 1 },
                  data: {
                    state: DataStates.ACTIVE,
                    updated_by: '1',
                    updated_proxied_by: '3',
                  },
                });
              }
            );
          });
        });

        describe('Upsert', () => {
          it(`should inject created_by & created_proxied_by & updated_by & updated_proxied_by fields into the upsert query if account is present`, async () => {
            const spy = vi.spyOn(prismaClient.statement_data, 'upsert');
            await asyncLocalStorage.run(
              {
                traceId: '123',
                account: {
                  uid: '1',
                  ouid: '2',
                  account_id: 'XN9U5UtGrD5aovyEwNlHj',
                  role_id: '1',
                },
              },
              async () => {
                await prismaClient.statement_data.upsert({
                  where: { id: 1 },
                  update: { state: DataStates.ACTIVE },
                  create: { state: DataStates.ACTIVE },
                });
                expect(spy).toHaveBeenCalledWith({
                  where: { id: 1 },
                  update: {
                    state: DataStates.ACTIVE,
                    updated_by: '1',
                    updated_proxied_by: '2',
                  },
                  create: {
                    state: DataStates.ACTIVE,
                    updated_by: '1',
                    updated_proxied_by: '2',
                    created_by: '1',
                    created_proxied_by: '2',
                  },
                });
              }
            );
          });

          it(`should not inject created_by & created_proxied_by & updated_by & updated_proxied_by fields into the upsert query if no account is present`, async () => {
            const spy = vi.spyOn(prismaClient.statement_data, 'upsert');
            await prismaClient.statement_data.upsert({
              where: { id: 1 },
              update: { state: DataStates.ACTIVE },
              create: { state: DataStates.ACTIVE },
            });
            expect(spy).toHaveBeenCalledWith({
              where: { id: 1 },
              update: { state: DataStates.ACTIVE },
              create: { state: DataStates.ACTIVE },
            });
          });

          it(`should not override created_by & created_proxied_by & updated_by & updated_proxied_by fields if they are provided in the create and update`, async () => {
            const spy = vi.spyOn(prismaClient.statement_data, 'upsert');
            await asyncLocalStorage.run(
              {
                traceId: '123',
                account: {
                  uid: '3',
                  ouid: '4',
                  account_id: 'XN9U5UtGrD5aovyEwNlHj',
                  role_id: '1',
                },
              },
              async () => {
                await prismaClient.statement_data.upsert({
                  where: { id: 1 },
                  update: {
                    state: DataStates.ACTIVE,
                    updated_by: '1',
                    updated_proxied_by: '2',
                  },
                  create: {
                    state: DataStates.ACTIVE,
                    created_by: '1',
                    created_proxied_by: '2',
                  },
                });
                expect(spy).toHaveBeenCalledWith({
                  where: { id: 1 },
                  update: {
                    state: DataStates.ACTIVE,
                    updated_by: '1',
                    updated_proxied_by: '2',
                  },
                  create: {
                    state: DataStates.ACTIVE,
                    created_by: '1',
                    created_proxied_by: '2',
                    updated_by: '3',
                    updated_proxied_by: '4',
                  },
                });
              }
            );
          });
        });
      });

      describe('Trim Proxy Fields', () => {
        it('should remove fields that are not in the fields array from the data', async () => {
          const data = {
            state: DataStates.ACTIVE,
            created_by: '1',
            created_proxied_by: '2',
            updated_by: '3',
            updated_proxied_by: '4',
            fake: 'fake',
          };
          const spy = vi.spyOn(prismaClient.statement_data, 'create');
          await prismaClient.statement_data.create({
            data,
          });
          expect(spy).toHaveBeenCalledWith({
            data: {
              state: DataStates.ACTIVE,
              created_by: '1',
              created_proxied_by: '2',
              updated_by: '3',
              updated_proxied_by: '4',
            },
          });
        });
      });
    });
  });
});
