import { Catch, type NextFunction, UseMiddleware } from 'next-api-decorators';
import type { NextApiRequest } from 'next';
import type { Pagination } from 'common/types/pagination';
import { SortOrder } from 'common/globalTypes';
import { isValidString } from 'common/helpers';

import {
  AuthenticationException,
  exceptionHandler,
  openAPIExceptionHandler,
} from '@/lib/exceptionHandler';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { prismaClient } from '@/lib/prisma';

@Catch(exceptionHandler)
class BaseHandler {
  whereClauseBuilder = (fields: string[], query: string, baseCondition) => {
    // Build the OR conditions for each column
    return {
      AND: [
        { OR: [baseCondition] },
        {
          OR: fields.map((field: string) => {
            // Build nested contains query if the field is like account_user_roles.user.email, the object should like
            // { account_user_roles: { user: { email: { contains: query, mode: 'insensitive' } } } }
            // don't use reducer, can create separate function for nested fields
            return this.buildNestedObject(field, query);
          }),
        },
      ],
    };
  };

  buildNestedObject(field, query) {
    const fields = field.split('.');
    let nestedObject = {};

    fields.reduceRight((acc, current, index) => {
      const newObj = {};
      if (index === fields.length - 1) {
        newObj[current] = { contains: query, mode: 'insensitive' };
      } else {
        if (index === 1) {
          newObj[current] = acc;
        } else {
          newObj[current] = acc ? { some: acc } : { some: {} };
        }
      }
      nestedObject = newObj;
      return newObj;
    }, null);

    return nestedObject;
  }

  queryNestedContacts = (
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    initialObject: any,
    depth: number,
    relationshipType: 'parent' | 'child',
    getParentChildren = false
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): any => {
    if (depth === 0) {
      return initialObject;
    }

    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let nestedObject;
    if (relationshipType === 'parent') {
      nestedObject = {
        where: { state: 'active', parent_id: { not: null }, AND: [] },
        select: {
          id: true,
          start_date: true,
          end_date: true,
          split_percentage: true,
          contact_group_id: true,
          sync_id: true,
          config: true,
          sync_worker: true,
          parent: {
            select: {
              id: true,
              str_id: true,
              first_name: true,
              last_name: true,
              email: true,
              contact_level: true,
              sync_id: true,
              config: true,
              status: true,
              child_relationships: getParentChildren,
              parent_relationships: initialObject,
            },
          },
        },
      };
    } else if (relationshipType === 'child') {
      nestedObject = {
        where: { state: 'active', AND: [] },
        select: {
          id: true,
          start_date: true,
          end_date: true,
          split_percentage: true,
          contact_group_id: true,
          contact: {
            select: {
              id: true,
              str_id: true,
              status: true,
              first_name: true,
              last_name: true,
              email: true,
              contact_level: true,
              child_relationships: initialObject,
            },
          },
        },
      };
    }

    return this.queryNestedContacts(
      nestedObject,
      depth - 1,
      relationshipType,
      getParentChildren
    );
  };

  /**
   * Extracts pagination parameters (page and limit) from the request query.
   *
   * This method checks for 'page' (or '?page') and 'limit' query parameters in the
   * provided request object. It converts them to numbers and returns them in a
   * pagination object.
   *
   * @param req The incoming API request object, which should contain the query parameters.
   * @returns A `Pagination` object containing the page and limit if both are found and are valid numbers, otherwise returns `undefined`.
   */
  getPagination(
    req: ExtNextApiRequest & NextApiRequest
  ): Pagination | undefined {
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const page = req?.query && Number(req.query['?page'] || req.query['page']);
    const limit = req?.query && Number(req.query.limit);

    if (!page || !limit) {
      return undefined;
    }

    return { page, limit };
  }

  /**
   * Parses the sorting order from the request's query parameters.
   *
   *
   * @param req - The incoming API request object, expected to contain query parameters.
   * @returns The corresponding `SortOrder` enum value (e.g., `SortOrder.ASC` or `SortOrder.DESC`)
   * if a valid `sort` query parameter is found. Returns `undefined` if the parameter
   * is missing or its value does not match any key in the `SortOrder` enum.
   *
   * @example
   * // For a request to /api/items?sort=asc
   * const sortOrder = getSort(req); // returns SortOrder.ASC
   *
   * @example
   * // For a request to /api/items
   * const sortOrder = getSort(req); // returns undefined
   */
  getSort(req: ExtNextApiRequest & NextApiRequest): SortOrder | undefined {
    return SortOrder[req?.query?.sort?.toUpperCase()];
  }

  /**
   * Extracts a text search query from an API request.
   *
   * This method checks the request's query parameters for a 'q' or 'search' key
   * to retrieve the search term. It ensures the retrieved value is a valid string.
   *
   * @param req - The incoming API request object.
   * @returns The text search string if a valid one is found; otherwise, it returns `undefined`.
   */
  getTextSearch(req: ExtNextApiRequest & NextApiRequest): string | undefined {
    const search = req?.query?.q || req?.query?.search;
    return isValidString(search) ? search : undefined;
  }
}

const OpenApiAuthGuard = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  next: NextFunction
) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new AuthenticationException(
      'Missing or invalid authorization header'
    );
  }
  const apiKey = authHeader.split(' ')[1];
  const record = await prismaClient.api_key.findUnique({
    where: { api_key: apiKey, state: 'active' },
  });
  if (!record) {
    throw new AuthenticationException('Invalid API key');
  }

  const clientIP =
    (req.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
    req.socket.remoteAddress;

  if (
    record.ip_allow_list.length > 0 &&
    !record.ip_allow_list.includes(clientIP)
  ) {
    throw new AuthenticationException('Unauthorized IP address');
  }

  const account = await prismaClient.accounts.findUnique({
    where: { str_id: record?.account_id, state: 'active' },
  });
  if (!account) {
    throw new AuthenticationException('Account not found');
  }
  res.setHeader('x-trace-id', req.logger.getTraceId());
  req.account_id = account.str_id;
  req.uid = record.uid;
  next();
};

@Catch(openAPIExceptionHandler)
@UseMiddleware(OpenApiAuthGuard)
class BaseOpenAPIHandler {}

export { BaseHandler, BaseOpenAPIHandler };
