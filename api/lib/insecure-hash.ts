/**
 * Cyrb53 (c) 2018 bryc (github.com/bryc)
 * License: Public domain (or MIT if needed). Attribution appreciated.
 * A fast and simple 53-bit string hash function with decent collision resistance.
 * Largely inspired by MurmurHash2/3, but with a focus on speed/simplicity.
 *
 * Source: https://github.com/bryc/code/blob/master/jshash/experimental/cyrb53.js
 */
export const cyrb53 = (str, seed = 0) => {
  let h1 = 0xdeadbeef ^ seed,
    h2 = 0x41c6ce57 ^ seed;
  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  for (let i = 0, ch; i < str.length; i++) {
    ch = str.charCodeAt(i);
    h1 = Math.imul(h1 ^ ch, **********);
    h2 = Math.imul(h2 ^ ch, **********);
  }
  h1 = Math.imul(h1 ^ (h1 >>> 16), **********);
  h1 ^= Math.imul(h2 ^ (h2 >>> 13), **********);
  h2 = Math.imul(h2 ^ (h2 >>> 16), **********);
  h2 ^= Math.imul(h1 ^ (h1 >>> 13), **********);
  return ********** * (2097151 & h2) + (h1 >>> 0);
};
