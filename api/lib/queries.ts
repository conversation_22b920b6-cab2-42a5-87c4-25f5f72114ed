import prisma from '@/lib/prisma';

const getEffectiveDateCondition = (effectiveDate) => ({
  AND: [
    {
      OR: [
        ...(effectiveDate ? [{ start_date: { lte: effectiveDate } }] : []),
        { start_date: null },
      ],
    },
    {
      OR: [
        ...(effectiveDate ? [{ end_date: { gte: effectiveDate } }] : []),
        { end_date: null },
      ],
    },
  ],
});

const recursiveSelect = (depth: number, vars?: { effectiveDate: Date }) => {
  if (depth === 0) {
    return {
      select: {
        parent: {
          include: {
            parent_relationships: {
              where: {
                state: 'active',
                parent_id: { not: null },
                ...getEffectiveDateCondition(vars.effectiveDate),
              },
            },
          },
        },
        split_percentage: true,
      },
    };
  }

  return {
    select: {
      parent: {
        include: {
          parent_relationships: {
            where: {
              state: 'active',
              ...getEffectiveDateCondition(vars.effectiveDate),
            },
            ...recursiveSelect(depth - 1, vars),
          },
        },
      },
      split_percentage: true,
    },
  };
};

export const findContactInclAncestors = async (
  strId,
  effectiveDate,
  timerStats
) => {
  const start = timerStats.start();
  const query = {
    where: {
      str_id: strId,
      state: 'active',
    },
    accountInject: false,
    include: {
      parent_relationships: {
        where: {
          state: 'active',
          ...getEffectiveDateCondition(effectiveDate),
        },
        ...recursiveSelect(9, { effectiveDate }),
      },
    },
  };

  const result = await prisma.contacts.findFirst(query);
  const duration = timerStats.end('findContactInclAncestors', start);
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.info(
    `findContactInclAncestors took ${duration.toFixed(2)}s (total: ${timerStats
      .get('findContactInclAncestors')
      .toFixed(2)}s)\n${timerStats.logString('findContactInclAncestors')}`
  );
  return result;
};
