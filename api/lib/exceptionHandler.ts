import type { NextApiRequest, NextApiResponse } from 'next';
import * as Sentry from '@sentry/nextjs';
import { BadRequestException, HttpException } from 'next-api-decorators';
import { ZodError } from 'zod';
import { PrismaClientValidationError } from '@prisma/client/runtime/library';
import { ResponseAction } from 'common/constants';

import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { createOpenAPIResponse } from '@/constants';

export const exceptionHandler = (
  error: Error,
  req: NextApiRequest & ExtNextApiRequest,
  res: NextApiResponse & ExtNextApiResponse
) => {
  req.logger.error(error.message, error);
  if (error instanceof SessionExpiredException) {
    res.status(401).json({
      success: false,
      statusText: error.message,
      message: error.message,
      action: ResponseAction.LOG_OUT,
    });
    return;
  }
  if (error instanceof BusinessException) {
    res.status(200).json({
      success: false,
      statusText: error.message,
      message: error.message,
    });
    return;
  }
  if (error instanceof ZodError) {
    const message = JSON.parse(error.message)
      .map((r) => [r.path, r.message].join(': '))
      .join(',');
    return res.status(400).json({
      success: false,
      statusText: message,
      message: message,
    });
  }
  // BadRequestException is throw by data validation by class-validator
  if (error instanceof BadRequestException) {
    res.status(400).json({
      success: false,
      statusText: error.message,
      message: error.message,
      error: error.message,
    });
    return;
  }
  Sentry.captureException(error);

  if (error instanceof MethodNotAllowedException) {
    res.status(405).json({
      success: false,
      statusText: 'error',
      message: error.message || `Method ${req.method} Not Allowed`,
    });
    return;
  }

  const traceId = req.logger.getTraceId();
  const message =
    // Since we only set NODE_ENV to production in the Dockerfile.prod, we can use this to check if we are in production and allow us to show the error message in local development
    process.env.NODE_ENV !== 'production'
      ? (error.message ?? 'An unknown error occurred')
      : // biome-ignore lint/style/useTemplate: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        'An unknown error occurred. Request ID: ' + traceId;
  res
    .status((error as HttpException)?.statusCode || 500)
    .json({ success: false, statusText: 'error', message });
};

export class MethodNotAllowedException extends HttpException {
  public constructor(message: string = 'Method Not Allowed') {
    super(405, message);
  }
}

export class ConfigNotFoundException extends HttpException {
  public constructor(message: string = 'Config Not Found') {
    super(404, message);
  }
}

export class BusinessException extends HttpException {
  public constructor(message: string = 'Business Exception') {
    super(200, message);
  }
  static from(message: string) {
    return new BusinessException(message);
  }
}

export class UnsupportedCalculationParallelLevelException extends BusinessException {
  public constructor(
    message: string = 'Unsupported calculation parallel level'
  ) {
    super(message);
  }
}

export class ServiceException extends HttpException {
  public constructor(message: string = 'Service Exception') {
    super(500, message);
  }
  static from(message: string) {
    return new ServiceException(message);
  }
}

export class AuthenticationException extends HttpException {
  public constructor(message: string = 'Authentication Exception') {
    super(401, message);
  }
}
export class SessionExpiredException extends HttpException {
  public constructor(message: string = 'Session expired') {
    super(401, message);
  }
}

export const openAPIExceptionHandler = (
  error: Error,
  req: NextApiRequest & ExtNextApiRequest,
  res: NextApiResponse & ExtNextApiResponse
) => {
  req.logger.error(error.message, error);
  if (error instanceof ZodError) {
    return res.status(400).json(
      createOpenAPIResponse(
        null,
        JSON.parse(error.message)
          .map((r) => [r.path, r.message].join(': '))
          .join(','),
        400
      )
    );
  }
  if (error instanceof BusinessException) {
    return res
      .status(200)
      .json(createOpenAPIResponse(null, error.message, error.statusCode));
  }
  if (error instanceof HttpException) {
    return res
      .status(error.statusCode)
      .json(createOpenAPIResponse(null, error.message, error.statusCode));
  }
  if (error instanceof PrismaClientValidationError) {
    Sentry.captureException(error);
    return res.status(500).json(
      createOpenAPIResponse(
        null,
        process.env.NODE_ENV !== 'production'
          ? error.message
          : // biome-ignore lint/style/useTemplate: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            'Prisma validation error, traceId: ' + req.logger.getTraceId(),
        500
      )
    );
  }
  Sentry.captureException(error);
  return res.status(200).json(createOpenAPIResponse(null, error.message, 500));
};
