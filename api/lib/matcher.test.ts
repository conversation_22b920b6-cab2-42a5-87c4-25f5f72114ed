import { describe, expect, it } from 'vitest';
import {
  FieldMatchertDateOperatorOptions,
  FieldMatchertUnitOptions,
  FiltersOperators,
} from 'common/globalTypes';

import { filterMatcher, isWithinOneYear } from '@/lib/matcher';

describe('Matcher', () => {
  describe('filterMatcher', () => {
    it('Should return true when op is "gt" and recordValue is greater than filterValue', () => {
      const filter = {
        op: FiltersOperators.GT,
        field: 'amount',
        value: '100',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ amount: 150 })).to.eq(true);
      expect(matcher({ amount: 50 })).to.eq(false);
    });

    it('Should return true when op is "lt" and recordValue is less than filterValue', () => {
      const filter = {
        op: FiltersOperators.LT,
        field: 'amount',
        value: '100',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ amount: 50 })).to.eq(true);
      expect(matcher({ amount: 150 })).to.eq(false);
    });

    it('Should return true when op is "gte" and recordValue is greater than or equal to filterValue', () => {
      const filter = {
        op: FiltersOperators.GTE,
        field: 'amount',
        value: '100',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ amount: 150 })).to.eq(true);
      expect(matcher({ amount: 100 })).to.eq(true);
      expect(matcher({ amount: 50 })).to.eq(false);
    });

    it('Should return true when op is "lte" and recordValue is less than or equal to filterValue', () => {
      const filter = {
        op: FiltersOperators.LTE,
        field: 'amount',
        value: '100',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ amount: 50 })).to.eq(true);
      expect(matcher({ amount: 100 })).to.eq(true);
      expect(matcher({ amount: 150 })).to.eq(false);
    });
    it('Should handle negative values correctly with "gt" operator', () => {
      const filter = {
        op: FiltersOperators.GT,
        field: 'amount',
        value: '-100',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ amount: -50 })).to.eq(true); // -50 > -100
      expect(matcher({ amount: 0 })).to.eq(true); // 0 > -100
      expect(matcher({ amount: -150 })).to.eq(false); // -150 !> -100
    });

    it('Should handle negative values correctly with "lt" operator', () => {
      const filter = {
        op: FiltersOperators.LT,
        field: 'amount',
        value: '-100',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ amount: -150 })).to.eq(true); // -150 < -100
      expect(matcher({ amount: -50 })).to.eq(false); // -50 !< -100
      expect(matcher({ amount: 0 })).to.eq(false); // 0 !< -100
    });

    it('Should handle negative record values with positive filter value', () => {
      const filter = {
        op: FiltersOperators.LT,
        field: 'amount',
        value: '100',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ amount: -50 })).to.eq(true); // -50 < 100
      expect(matcher({ amount: -1000 })).to.eq(true); // -1000 < 100
    });

    it('Should handle negative values correctly with equality operators', () => {
      const filter = {
        op: FiltersOperators.EQ,
        field: 'amount',
        value: '-100',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ amount: -100 })).to.eq(true); // -100 == -100
      expect(matcher({ amount: 100 })).to.eq(false); // 100 != -100
      expect(matcher({ amount: -101 })).to.eq(false); // -101 != -100
    });

    it('Should handle zero and negative zero correctly', () => {
      const filter = {
        op: FiltersOperators.EQ,
        field: 'amount',
        value: '0',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ amount: 0 })).to.eq(true); // 0 == 0
      expect(matcher({ amount: -0 })).to.eq(true); // -0 == 0
    });
    it('Should return true when op is "eq" and recordValue is equal to filterValue', () => {
      const filter = {
        op: FiltersOperators.EQ,
        field: 'status',
        value: 'active',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ status: 'active' })).to.eq(true);
      expect(matcher({ status: 'inactive' })).to.eq(false);
    });

    it('Should return true when op is "neq" and recordValue is not equal to filterValue', () => {
      const filter = {
        op: FiltersOperators.NEQ,
        field: 'status',
        value: 'active',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ status: 'inactive' })).to.eq(true);
      expect(matcher({ status: 'active' })).to.eq(false);
    });

    it('Should return true when op is "startswith" and recordValue starts with filterValue', () => {
      const filter = {
        op: FiltersOperators.STARTSWITH,
        field: 'name',
        value: 'John',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ name: 'John Doe' })).to.eq(true);
      expect(matcher({ name: 'Jane Doe' })).to.eq(false);
    });

    it('Should return true when op is "endswith" and recordValue ends with filterValue', () => {
      const filter = {
        op: FiltersOperators.ENDSWITH,
        field: 'name',
        value: 'Doe',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ name: 'John Doe' })).to.eq(true);
      expect(matcher({ name: 'Jane Smith' })).to.eq(false);
    });

    it('Should return true when op is "contains" and recordValue contains filterValue', () => {
      const filter = {
        op: FiltersOperators.CONTAINS,
        field: 'name',
        value: 'Doe',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ name: 'John Doe' })).to.eq(true);
      expect(matcher({ name: 'Jane Smith' })).to.eq(false);
    });

    it('Should handle comma-separated values for "contains"', () => {
      const filter = {
        op: FiltersOperators.CONTAINS,
        field: 'text',
        value: 'apple,banana,orange',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ text: 'I like apple pie' })).to.eq(true);
      expect(matcher({ text: 'I have a banana' })).to.eq(true);
      expect(matcher({ text: 'Fresh orange juice' })).to.eq(true);
      expect(matcher({ text: 'I prefer grapes' })).to.eq(false);
    });

    it('Should return true if any value matches for "contains"', () => {
      const filter = {
        op: FiltersOperators.CONTAINS,
        field: 'tags',
        value: 'urgent, important, follow-up',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ tags: 'This is very important' })).to.eq(true);
      expect(matcher({ tags: 'Not matching anything' })).to.eq(false);
    });

    it('Should return true when op is "ncontains" and recordValue does not contain filterValue', () => {
      const filter = {
        op: FiltersOperators.NCONTAINS,
        field: 'name',
        value: 'Doe',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ name: 'Jane Smith' })).to.eq(true);
      expect(matcher({ name: 'John Doe' })).to.eq(false);
    });

    it('Should handle comma-separated values for "ncontains"', () => {
      const filter = {
        op: FiltersOperators.NCONTAINS,
        field: 'text',
        value: 'apple,banana,orange',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ text: 'I like grape pie' })).to.eq(true);
      expect(matcher({ text: 'I have a banana' })).to.eq(false);
      expect(matcher({ text: 'Fresh orange juice' })).to.eq(false);
    });

    it('Should return true only if none of the values match for "ncontains"', () => {
      const filter = {
        op: FiltersOperators.NCONTAINS,
        field: 'description',
        value: 'critical, error, warning',
      };
      const matcher = filterMatcher(filter);
      expect(
        matcher({ description: 'Operation completed successfully' })
      ).to.eq(true);
      expect(
        matcher({ description: 'Warning: Operation completed with issues' })
      ).to.eq(false);
    });

    it('Should return true when op is "containedin" and recordValue is contained in filterValue', () => {
      const filter = {
        op: FiltersOperators.CONTAINEDIN,
        field: 'category',
        value: '["electronics", "appliances"]',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ category: 'electronics' })).to.eq(true);
      expect(matcher({ category: 'appliances' })).to.eq(true);
      expect(matcher({ category: 'furniture' })).to.eq(false);
    });

    it('Should return true when op is "ncontainedin" and recordValue is not contained in filterValue', () => {
      const filter = {
        op: FiltersOperators.NCONTAINEDIN,
        field: 'category',
        value: '["electronics", "appliances"]',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ category: 'furniture' })).to.eq(true);
      expect(matcher({ category: 'electronics' })).to.eq(false);
      expect(matcher({ category: 'appliances' })).to.eq(false);
    });

    it('Should return true when op is "before" and recordValue is before filterValue', () => {
      const filter = {
        op: FiltersOperators.BEFORE,
        field: 'date',
        value: '2022-01-01',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2021-12-31' })).to.eq(true);
      expect(matcher({ date: '2022-01-01' })).to.eq(false);
      expect(matcher({ date: '2022-01-02' })).to.eq(false);
    });

    it('Should correctly evaluate "before" operator with a fixed date', () => {
      const filter = {
        op: FiltersOperators.BEFORE,
        field: 'date',
        value: '2022-01-01',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2021-12-31' })).to.eq(true);
      expect(matcher({ date: '2022-01-01' })).to.eq(false);
      expect(matcher({ date: '2022-01-02' })).to.eq(false);
    });

    it('Should correctly evaluate "before" operator with date adjustments based on an existing field, number, and unit', () => {
      const filter = {
        op: FiltersOperators.BEFORE,
        field: 'date',
        value: 'processing_date',
        from: FieldMatchertDateOperatorOptions.FromDateField,
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(
        matcher({ date: '2022-01-06', processing_date: '2022-01-02' })
      ).to.eq(true);
      expect(
        matcher({ date: '2022-01-07', processing_date: '2022-01-02' })
      ).to.eq(false);
      expect(
        matcher({ date: '2021-12-31', processing_date: '2022-01-02' })
      ).to.eq(true);

      filter.unit = FieldMatchertUnitOptions.Months;
      const monthlyMatcher = filterMatcher(filter);
      expect(
        monthlyMatcher({ date: '2022-06-01', processing_date: '2022-01-02' })
      ).to.eq(true);
      expect(
        monthlyMatcher({ date: '2022-06-02', processing_date: '2022-01-02' })
      ).to.eq(false);

      filter.unit = FieldMatchertUnitOptions.Years;
      const yearlyMatcher = filterMatcher(filter);
      expect(
        yearlyMatcher({ date: '2027-01-01', processing_date: '2022-01-02' })
      ).to.eq(true);
      expect(
        yearlyMatcher({ date: '2027-01-02', processing_date: '2022-01-02' })
      ).to.eq(false);
    });

    it('Should correctly evaluate "before" operator with date adjustments based on a fixed date, number, and unit', () => {
      const filter = {
        op: FiltersOperators.BEFORE,
        field: 'date',
        value: '2022-01-01',
        from: FieldMatchertDateOperatorOptions.FromFixedDate,
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-05' })).to.eq(true);
      expect(matcher({ date: '2022-01-06' })).to.eq(false);
      expect(matcher({ date: '2021-12-31' })).to.eq(true);

      filter.unit = FieldMatchertUnitOptions.Months;
      const monthlyMatcher = filterMatcher(filter);
      expect(monthlyMatcher({ date: '2022-05-31' })).to.eq(true);
      expect(monthlyMatcher({ date: '2022-06-01' })).to.eq(false);

      filter.unit = FieldMatchertUnitOptions.Years;
      const yearlyMatcher = filterMatcher(filter);
      expect(yearlyMatcher({ date: '2026-12-31' })).to.eq(true);
      expect(yearlyMatcher({ date: '2027-01-01' })).to.eq(false);
    });

    it('Should return false for invalid date formats in "before" operator', () => {
      const filter = {
        op: FiltersOperators.BEFORE,
        field: 'date',
        value: 'invalid_date',
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-01' })).to.eq(false);
    });

    it('Should return false for "before" operator when recordValue is null or undefined', () => {
      const filter = {
        op: FiltersOperators.BEFORE,
        field: 'date',
        value: '2022-01-01',
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: null })).to.eq(false);
      expect(matcher({ date: undefined })).to.eq(false);
    });

    it('Should return false for "before" operator when filterValue is null or undefined', () => {
      const filter = {
        op: FiltersOperators.BEFORE,
        field: 'date',
        value: null,
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-01' })).to.eq(false);
    });

    it('Should return true when op is "beforeEquals" and recordValue is before or equal to filterValue', () => {
      const filter = {
        op: FiltersOperators.BEFORE_EQUALS,
        field: 'date',
        value: '2022-01-01',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2021-12-31' })).to.eq(true);
      expect(matcher({ date: '2022-01-01' })).to.eq(true);
      expect(matcher({ date: '2022-01-02' })).to.eq(false);
    });

    it('Should correctly evaluate "before_equals" operator with a fixed date', () => {
      const filter = {
        op: FiltersOperators.BEFORE_EQUALS,
        field: 'date',
        value: '2022-01-01',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-01' })).to.eq(true);
      expect(matcher({ date: '2021-12-31' })).to.eq(true);
      expect(matcher({ date: '2022-01-02' })).to.eq(false);
    });

    it('Should correctly evaluate "before_equals" operator with date adjustments based on an existing field, number, and unit', () => {
      const filter = {
        op: FiltersOperators.BEFORE_EQUALS,
        field: 'date',
        value: 'processing_date',
        from: FieldMatchertDateOperatorOptions.FromDateField,
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(
        matcher({ date: '2022-01-07', processing_date: '2022-01-02' })
      ).to.eq(true);
      expect(
        matcher({ date: '2022-01-08', processing_date: '2022-01-02' })
      ).to.eq(false);
      expect(
        matcher({ date: '2021-12-31', processing_date: '2022-01-02' })
      ).to.eq(true);

      filter.unit = FieldMatchertUnitOptions.Months;
      const monthlyMatcher = filterMatcher(filter);
      expect(
        monthlyMatcher({ date: '2022-06-02', processing_date: '2022-01-02' })
      ).to.eq(true);
      expect(
        monthlyMatcher({ date: '2022-06-03', processing_date: '2022-01-02' })
      ).to.eq(false);

      filter.unit = FieldMatchertUnitOptions.Years;
      const yearlyMatcher = filterMatcher(filter);
      expect(
        yearlyMatcher({ date: '2027-01-02', processing_date: '2022-01-02' })
      ).to.eq(true);
      expect(
        yearlyMatcher({ date: '2027-01-03', processing_date: '2022-01-02' })
      ).to.eq(false);
    });

    it('Should correctly evaluate "before_equals" operator with date adjustments based on a fixed date, number, and unit', () => {
      const filter = {
        op: FiltersOperators.BEFORE_EQUALS,
        field: 'date',
        value: '2022-01-01',
        from: FieldMatchertDateOperatorOptions.FromFixedDate,
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-06' })).to.eq(true);
      expect(matcher({ date: '2022-01-07' })).to.eq(false);
      expect(matcher({ date: '2021-12-31' })).to.eq(true);

      filter.unit = FieldMatchertUnitOptions.Months;
      const monthlyMatcher = filterMatcher(filter);
      expect(monthlyMatcher({ date: '2022-06-01' })).to.eq(true);
      expect(monthlyMatcher({ date: '2022-06-02' })).to.eq(false);

      filter.unit = FieldMatchertUnitOptions.Years;
      const yearlyMatcher = filterMatcher(filter);
      expect(yearlyMatcher({ date: '2027-01-01' })).to.eq(true);
      expect(yearlyMatcher({ date: '2027-01-02' })).to.eq(false);
    });

    it('Should return false for invalid date formats in "before_equals" operator', () => {
      const filter = {
        op: FiltersOperators.BEFORE_EQUALS,
        field: 'date',
        value: 'invalid_date',
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-01' })).to.eq(false);
    });

    it('Should return false for "before_equals" operator when recordValue is null or undefined', () => {
      const filter = {
        op: FiltersOperators.BEFORE_EQUALS,
        field: 'date',
        value: '2022-01-01',
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: null })).to.eq(false);
      expect(matcher({ date: undefined })).to.eq(false);
    });

    it('Should return false for "before_equals" operator when filterValue is null or undefined', () => {
      const filter = {
        op: FiltersOperators.BEFORE_EQUALS,
        field: 'date',
        value: null,
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-01' })).to.eq(false);
    });

    it('Should return true when op is "after" and recordValue is after filterValue', () => {
      const filter = {
        op: FiltersOperators.AFTER,
        field: 'date',
        value: '2022-01-01',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-02' })).to.eq(true);
      expect(matcher({ date: '2022-01-01' })).to.eq(false);
      expect(matcher({ date: '2021-12-31' })).to.eq(false);
    });

    it('Should correctly evaluate "after" operator with date adjustments based on an existing field, number, and unit', () => {
      const filter = {
        op: FiltersOperators.AFTER,
        field: 'date',
        value: 'processing_date',
        from: FieldMatchertDateOperatorOptions.FromDateField,
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(
        matcher({ date: '2022-01-08', processing_date: '2022-01-02' })
      ).to.eq(true);
      expect(
        matcher({ date: '2022-01-02', processing_date: '2022-01-02' })
      ).to.eq(false);
      expect(
        matcher({ date: '2021-12-01', processing_date: '2021-12-05' })
      ).to.eq(false);

      filter.unit = FieldMatchertUnitOptions.Months;
      const monthlyMatcher = filterMatcher(filter);
      expect(
        monthlyMatcher({ date: '2022-06-03', processing_date: '2022-01-02' })
      ).to.eq(true);
      expect(
        monthlyMatcher({ date: '2022-06-02', processing_date: '2022-01-02' })
      ).to.eq(false);
      expect(
        monthlyMatcher({ date: '2021-12-01', processing_date: '2021-12-05' })
      ).to.eq(false);

      filter.unit = FieldMatchertUnitOptions.Years;
      const yearlyMatcher = filterMatcher(filter);
      expect(
        yearlyMatcher({ date: '2027-01-03', processing_date: '2022-01-02' })
      ).to.eq(true);
      expect(
        yearlyMatcher({ date: '2027-01-02', processing_date: '2022-01-02' })
      ).to.eq(false);
      expect(
        yearlyMatcher({ date: '2021-12-01', processing_date: '2021-12-05' })
      ).to.eq(false);
    });

    it('Should correctly evaluate "after" operator with date adjustments based on fixed date, number, and unit', () => {
      const filter = {
        op: FiltersOperators.AFTER,
        field: 'date',
        value: '2022-01-01',
        from: FieldMatchertDateOperatorOptions.FromFixedDate,
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-08' })).to.eq(true);
      expect(matcher({ date: '2022-01-01' })).to.eq(false);
      expect(matcher({ date: '2021-01-06' })).to.eq(false);

      filter.unit = FieldMatchertUnitOptions.Months;
      const montlyMatcher = filterMatcher(filter);
      expect(montlyMatcher({ date: '2022-06-02' })).to.eq(true);
      expect(montlyMatcher({ date: '2022-06-01' })).to.eq(false);
      expect(montlyMatcher({ date: '2021-12-31' })).to.eq(false);

      filter.unit = FieldMatchertUnitOptions.Years;
      const yearlyMatcher = filterMatcher(filter);
      expect(yearlyMatcher({ date: '2027-01-02' })).to.eq(true);
      expect(yearlyMatcher({ date: '2027-01-01' })).to.eq(false);
      expect(yearlyMatcher({ date: '2021-12-31' })).to.eq(false);
    });

    it('Should correctly evaluate "after" operator when number or unit is missing', () => {
      const filter = {
        op: FiltersOperators.AFTER,
        field: 'date',
        value: 'processing_date',
        from: FieldMatchertDateOperatorOptions.FromDateField,
      };
      const matcher = filterMatcher(filter);
      expect(
        matcher({ date: '2022-01-03', processing_date: '2022-01-02' })
      ).to.eq(true);
      expect(
        matcher({ date: '2022-01-01', processing_date: '2022-01-02' })
      ).to.eq(false);
    });

    it('Should correctly evaluate "after" operator when "from" is missing', () => {
      const filter = {
        op: FiltersOperators.AFTER,
        field: 'date',
        value: '2022-01-01',
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-08' })).to.eq(true);
      expect(matcher({ date: '2022-01-01' })).to.eq(false);
    });

    it('Should return false for invalid date formats in "after" operator', () => {
      const filter = {
        op: FiltersOperators.AFTER,
        field: 'date',
        value: 'invalid_date',
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-08' })).to.eq(false);
    });

    it('Should return false for "after" operator when recordValue is null or undefined', () => {
      const filter = {
        op: FiltersOperators.AFTER,
        field: 'date',
        value: '2022-01-01',
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: null })).to.eq(false);
      expect(matcher({ date: undefined })).to.eq(false);
    });

    it('Should return false for "after" operator when filterValue is null or undefined', () => {
      const filter = {
        op: FiltersOperators.AFTER,
        field: 'date',
        value: null,
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-08' })).to.eq(false);
    });

    it('Should correctly evaluate "after" operator with leap year adjustments', () => {
      const filter = {
        op: FiltersOperators.AFTER,
        field: 'date',
        value: '2020-02-27',
        number: '1',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2020-02-29' })).to.eq(true); // Leap year
      expect(matcher({ date: '2020-02-28' })).to.eq(false);
    });

    it('Should correctly evaluate "after" operator with large date adjustments', () => {
      const filter = {
        op: FiltersOperators.AFTER,
        field: 'date',
        value: '2022-01-01',
        number: '1000',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2024-09-28' })).to.eq(true); // 1000 days later
      expect(matcher({ date: '2024-09-26' })).to.eq(false);
    });

    it('Should return true when op is "afterEquals" and recordValue is after or equal to filterValue', () => {
      const filter = {
        op: FiltersOperators.AFTER_EQUALS,
        field: 'date',
        value: '2022-01-01',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-02' })).to.eq(true);
      expect(matcher({ date: '2022-01-01' })).to.eq(true);
      expect(matcher({ date: '2021-12-31' })).to.eq(false);
    });

    it('Should correctly evaluate "AFTER_EQUALS" operator for effective_date >= payment_date - 9 months', () => {
      const filter = {
        op: FiltersOperators.AFTER_EQUALS,
        field: 'effective_date',
        value: 'payment_date',
        from: FieldMatchertDateOperatorOptions.FromDateField,
        number: '-9',
        unit: FieldMatchertUnitOptions.Months,
      };

      const matcher = filterMatcher(filter);

      expect(
        matcher({ effective_date: '2022-07-01', payment_date: '2023-04-01' })
      ).to.eq(true); // Within 9 months
      expect(
        matcher({ effective_date: '2022-06-30', payment_date: '2023-04-01' })
      ).to.eq(false); // Before 9 months
      expect(
        matcher({ effective_date: '2023-04-01', payment_date: '2023-04-01' })
      ).to.eq(true); // Equal to payment_date
    });

    it('Should return true when op is "after_equals" and recordValue is after or equal to filterValue', () => {
      const filter = {
        op: FiltersOperators.AFTER_EQUALS,
        field: 'date',
        value: '2022-01-01',
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-02' })).to.eq(true);
      expect(matcher({ date: '2022-01-01' })).to.eq(true);
      expect(matcher({ date: '2021-12-31' })).to.eq(false);
    });

    it('Should correctly evaluate "after_equals" operator with date adjustments based on an existing field, number, and unit', () => {
      const filter = {
        op: FiltersOperators.AFTER_EQUALS,
        field: 'date',
        value: 'processing_date',
        from: FieldMatchertDateOperatorOptions.FromDateField,
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(
        matcher({ date: '2022-01-07', processing_date: '2022-01-02' })
      ).to.eq(true);
      expect(
        matcher({ date: '2022-01-06', processing_date: '2022-01-01' })
      ).to.eq(true);
      expect(
        matcher({ date: '2022-01-01', processing_date: '2022-01-02' })
      ).to.eq(false);

      filter.unit = FieldMatchertUnitOptions.Months;
      const monthlyMatcher = filterMatcher(filter);
      expect(
        monthlyMatcher({ date: '2022-06-02', processing_date: '2022-01-02' })
      ).to.eq(true);
      expect(
        monthlyMatcher({ date: '2022-06-01', processing_date: '2022-01-01' })
      ).to.eq(true);
      expect(
        monthlyMatcher({ date: '2022-05-31', processing_date: '2022-01-02' })
      ).to.eq(false);

      filter.unit = FieldMatchertUnitOptions.Years;
      const yearlyMatcher = filterMatcher(filter);
      expect(
        yearlyMatcher({ date: '2027-01-02', processing_date: '2022-01-02' })
      ).to.eq(true);
      expect(
        yearlyMatcher({ date: '2027-01-01', processing_date: '2022-01-01' })
      ).to.eq(true);
      expect(
        yearlyMatcher({ date: '2026-12-31', processing_date: '2022-01-02' })
      ).to.eq(false);
    });

    it('Should correctly evaluate "after_equals" operator with date adjustments based on a fixed date, number, and unit', () => {
      const filter = {
        op: FiltersOperators.AFTER_EQUALS,
        field: 'date',
        value: '2022-01-01',
        from: FieldMatchertDateOperatorOptions.FromFixedDate,
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-06' })).to.eq(true);
      expect(matcher({ date: '2022-01-06' })).to.eq(true);
      expect(matcher({ date: '2022-01-04' })).to.eq(false);

      filter.unit = FieldMatchertUnitOptions.Months;
      const monthlyMatcher = filterMatcher(filter);
      expect(monthlyMatcher({ date: '2022-06-01' })).to.eq(true);
      expect(monthlyMatcher({ date: '2022-05-31' })).to.eq(false);

      filter.unit = FieldMatchertUnitOptions.Years;
      const yearlyMatcher = filterMatcher(filter);
      expect(yearlyMatcher({ date: '2027-01-01' })).to.eq(true);
      expect(yearlyMatcher({ date: '2026-12-31' })).to.eq(false);
    });

    it('Should return false for invalid date formats in "after_equals" operator', () => {
      const filter = {
        op: FiltersOperators.AFTER_EQUALS,
        field: 'date',
        value: 'invalid_date',
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-01' })).to.eq(false);
    });

    it('Should return false for "after_equals" operator when recordValue is null or undefined', () => {
      const filter = {
        op: FiltersOperators.AFTER_EQUALS,
        field: 'date',
        value: '2022-01-01',
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: null })).to.eq(false);
      expect(matcher({ date: undefined })).to.eq(false);
    });

    it('Should return false for "after_equals" operator when filterValue is null or undefined', () => {
      const filter = {
        op: FiltersOperators.AFTER_EQUALS,
        field: 'date',
        value: null,
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-01' })).to.eq(false);
    });

    it('Should correctly evaluate "within" operator with a fixed date, number, and unit', () => {
      const filter = {
        op: FiltersOperators.WITHIN,
        field: 'date',
        value: '2022-01-01',
        from: FieldMatchertDateOperatorOptions.FromFixedDate,
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-05' })).to.eq(true); // Within 5 days
      expect(matcher({ date: '2022-01-06' })).to.eq(true); // Exactly 5 days
      expect(matcher({ date: '2022-01-07' })).to.eq(false); // Beyond 5 days
      expect(matcher({ date: '2021-12-15' })).to.eq(false); // Before the base date

      filter.unit = FieldMatchertUnitOptions.Months;
      const monthlyMatcher = filterMatcher(filter);
      expect(monthlyMatcher({ date: '2022-06-01' })).to.eq(true); // Within 5 months
      expect(monthlyMatcher({ date: '2022-06-02' })).to.eq(false); // Beyond 5 months

      filter.unit = FieldMatchertUnitOptions.Years;
      const yearlyMatcher = filterMatcher(filter);
      expect(yearlyMatcher({ date: '2027-01-01' })).to.eq(true); // Within 5 years
      expect(yearlyMatcher({ date: '2027-01-02' })).to.eq(false); // Beyond 5 years
    });

    it('Should correctly evaluate "Within" operator for effective_date >= payment_date - 9 months', () => {
      const filter = {
        op: FiltersOperators.WITHIN,
        field: 'effective_date',
        value: 'payment_date',
        from: FieldMatchertDateOperatorOptions.FromDateField,
        number: '-9',
        unit: FieldMatchertUnitOptions.Months,
      };

      const matcher = filterMatcher(filter);

      expect(
        matcher({ effective_date: '2022-07-01', payment_date: '2023-04-01' })
      ).to.eq(true); // Within 9 months
      expect(
        matcher({ effective_date: '2022-06-30', payment_date: '2023-04-01' })
      ).to.eq(false); // Before 9 months
      expect(
        matcher({ effective_date: '2023-04-01', payment_date: '2023-04-01' })
      ).to.eq(true); // Equal to payment_date
    });

    it('Should correctly evaluate "within" operator with date adjustments based on an existing field, number, and unit', () => {
      const filter = {
        op: FiltersOperators.WITHIN,
        field: 'date',
        value: 'processing_date',
        from: FieldMatchertDateOperatorOptions.FromDateField,
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(
        matcher({ date: '2022-01-07', processing_date: '2022-01-02' })
      ).to.eq(true); // Within 5 days
      expect(
        matcher({ date: '2022-01-08', processing_date: '2022-01-02' })
      ).to.eq(false); // Beyond 5 days
      expect(
        matcher({ date: '2022-01-01', processing_date: '2022-01-02' })
      ).to.eq(false); // Before the base date

      filter.unit = FieldMatchertUnitOptions.Months;
      const monthlyMatcher = filterMatcher(filter);
      expect(
        monthlyMatcher({ date: '2022-06-02', processing_date: '2022-01-02' })
      ).to.eq(true); // Within 5 months
      expect(
        monthlyMatcher({ date: '2022-06-03', processing_date: '2022-01-02' })
      ).to.eq(false); // Beyond 5 months

      filter.unit = FieldMatchertUnitOptions.Years;
      const yearlyMatcher = filterMatcher(filter);
      expect(
        yearlyMatcher({ date: '2027-01-02', processing_date: '2022-01-02' })
      ).to.eq(true); // Within 5 years
      expect(
        yearlyMatcher({ date: '2027-01-03', processing_date: '2022-01-02' })
      ).to.eq(false); // Beyond 5 years
    });

    it('Should return false for invalid date formats in "within" operator', () => {
      const filter = {
        op: FiltersOperators.WITHIN,
        field: 'date',
        value: 'invalid_date',
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-01' })).to.eq(false);
    });

    it('Should return false for "within" operator when recordValue is null or undefined', () => {
      const filter = {
        op: FiltersOperators.WITHIN,
        field: 'date',
        value: '2022-01-01',
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: null })).to.eq(false);
      expect(matcher({ date: undefined })).to.eq(false);
    });

    it('Should return false for "within" operator when filterValue is null or undefined', () => {
      const filter = {
        op: FiltersOperators.WITHIN,
        field: 'date',
        value: null,
        number: '5',
        unit: FieldMatchertUnitOptions.Days,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ date: '2022-01-01' })).to.eq(false);
    });

    it('Should return true when op is "withinOneYear" and recordValue is within one year of filterValue', () => {
      const filter = {
        op: FiltersOperators.WITHIN_ONE_YEAR,
        field: 'startDate',
        value: 'endDate',
      };
      const matcher = filterMatcher(filter);
      expect(
        matcher({
          startDate: new Date('2021-01-01'),
          endDate: new Date('2021-12-31'),
        })
      ).to.eq(true);
      expect(
        matcher({
          startDate: new Date('2021-01-01'),
          endDate: new Date('2022-01-01'),
        })
      ).to.eq(true);
      expect(
        matcher({
          startDate: new Date('2021-01-01'),
          endDate: new Date('2022-01-02'),
        })
      ).to.eq(false);
    });

    it('Should return true when op is "atLeastOneYear" and recordValue is at least one year from filterValue', () => {
      const filter = {
        op: FiltersOperators.AT_LEAST_ONE_YEAR,
        field: 'startDate',
        value: 'endDate',
      };
      const matcher = filterMatcher(filter);
      expect(
        matcher({
          startDate: new Date('2021-01-01'),
          endDate: new Date('2022-01-02'),
        })
      ).to.eq(true);
      expect(
        matcher({
          startDate: new Date('2021-01-01'),
          endDate: new Date('2022-01-01'),
        })
      ).to.eq(false);
      expect(
        matcher({
          startDate: new Date('2021-01-01'),
          endDate: new Date('2021-12-31'),
        })
      ).to.eq(false);
    });

    it('Should return the same value value when op is "unknown"', () => {
      const filter = {
        op: FiltersOperators.CUSTOM,
        field: 'status',
        value: true,
      };
      const matcher = filterMatcher(filter);
      expect(matcher({ status: 'any value' })).to.eq(filter.value);
    });
  });

  describe('isWithinOneYear', () => {
    it('Should return true if endDate is within one year of startDate', () => {
      const startDate = new Date('2021-01-01');
      const endDate = new Date('2021-12-31');
      expect(isWithinOneYear(startDate, endDate)).to.eq(true);
    });

    it('Should return true if endDate is exactly one year from startDate', () => {
      const startDate = new Date('2021-01-01');
      const endDate = new Date('2022-01-01');
      expect(isWithinOneYear(startDate, endDate)).to.eq(true);
    });

    it('Should return false if endDate is more than one year from startDate', () => {
      const startDate = new Date('2021-01-01');
      const endDate = new Date('2022-01-02');
      expect(isWithinOneYear(startDate, endDate)).to.eq(false);
    });
  });
});
