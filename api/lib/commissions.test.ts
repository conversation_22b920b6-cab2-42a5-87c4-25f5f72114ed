import { describe, it, expect, vi, beforeEach } from 'vitest';

import * as commissions from '@/lib/commissions';
import { prismaClient } from '@/lib/prisma';
import { LRUCacheService } from '@/services/cache/lru';

vi.mock('@/lib/prisma', () => ({
  prismaClient: {
    comp_grids: {
      findMany: vi.fn(),
    },
    company_mappings: {
      findMany: vi.fn(),
    },
    companies: {
      findMany: vi.fn(),
    },
    comp_grid_levels: {
      findMany: vi.fn(),
    },
  },
}));

vi.mock('@/lib/commissions', async () => {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const actual = await vi.importActual<any>('@/lib/commissions');
  return {
    ...actual,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    isNill: (val: any) => val === null || val === undefined,
    DataStates: {
      ACTIVE: 'active',
    },
  };
});

const validScheduleConfig = {
  name: 'Sample Schedule',
  commission_schedule: [
    {
      year: '2022',
      rate: '0.05',
      breakdown: [
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
      ],
      max_commission: '1000',
    },
    {
      year: '2023',
      rate: '0.05',
      breakdown: [
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
      ],
      max_commission: '1000',
    },
  ],
};

const invalidScheduleConfig = {
  name: 'Sample Schedule',
  commission_schedule: [
    {
      year: '2022',
      rate: '0.05',
      breakdown: [
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
        '1/12',
      ],
      max_commission: 'not a number',
    },
  ],
};

describe('validateCommissionSchedule', () => {
  it('should return true for a valid commission schedule', () => {
    const result = commissions.validateCommissionSchedule(validScheduleConfig);
    expect(result.isValid).toBe(true);
  });

  it('should return false for an invalid commission schedule', () => {
    const result = commissions.validateCommissionSchedule(
      invalidScheduleConfig
    );
    expect(result.isValid).toBe(false);
  });
});

describe('getApplicableCompProfiles', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('returns a matching profile when all checks pass', async () => {
    const mockProfile = {
      start_date: new Date('2020-01-01'),
      end_date: new Date('2030-01-01'),
      agent_commission_schedule_profile: {
        name: 'Test Profile',
        schedules: [{ match_criteria: [] }],
        single_carrier_mode: true,
      },
    };

    const agent = {
      compProfiles: [mockProfile],
      agentStrId: 'agent-001',
      statementContactStrId: 'contact-001',
    };

    const statement = {
      id: 123,
      effective_date: new Date('2025-01-01'),
      report: {},
    };

    const lookupData = {
      companyProducts: [],
      companyProductOptions: [],
    };

    vi.spyOn(commissions, 'profileCompanyCheck').mockResolvedValue({
      matchedCompany: true,
      firstValidatorCheck: true,
      secondValidatorCheck: true,
    });

    vi.spyOn(commissions, 'checkIssueAge').mockReturnValue({
      match: true,
      noMatchReason: [],
    });

    vi.spyOn(commissions, 'checkTextRule').mockReturnValue({
      match: true,
      noMatchReason: [],
    });

    vi.spyOn(commissions, 'checkArrayIdsToTextRule').mockReturnValue({
      match: true,
      noMatchReason: [],
    });

    const result = await commissions.getApplicableCompProfiles(
      agent,
      statement,
      lookupData,
      new Date('2025-01-01'),
      false,
      {}
    );

    expect(result).toBeDefined();
    expect(result?.agentProfileConfig).toBe(mockProfile);
  });

  it('returns undefined when no profiles are provided', async () => {
    const agent = {
      compProfiles: [],
      agentStrId: 'agent-002',
      statementContactStrId: 'contact-002',
    };

    const result = await commissions.getApplicableCompProfiles(
      agent,
      {},
      {},
      new Date(),
      false,
      {}
    );

    expect(result).toBeUndefined();
  });
});

describe('getCompGridCriteria', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('returns criteria from lookupData in single_carrier_mode', async () => {
    const profile = {
      agent_commission_schedule_profile: {
        single_carrier_mode: true,
        comp_grid_id: 'grid-1',
      },
    };
    const rule = {
      comp_grid_criteria_id: ['crit-1', 'crit-2'],
    };
    const lookupData = {
      compGridCriteria: {
        'crit-1': { id: 'crit-1' },
        'crit-2': { id: 'crit-2' },
      },
    };

    const result = await commissions.getCompGridCriteria(
      profile,
      rule,
      lookupData,
      null,
      new LRUCacheService({ max: 300 })
    );

    expect(result).toHaveLength(2);
  });

  it('returns comp grid criteria and levels from DB in multi-carrier mode', async () => {
    const profile = {
      agent_commission_schedule_profile: {
        single_carrier_mode: false,
        payer_grid_level_name: 'payer',
        payee_grid_level_name: 'payee',
        companies: [{ id: 'c1' }],
      },
    };

    const rule = {};
    const lookupData = { compGridCriteria: {} };
    const matchedCompany = { id: 'c1' };

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (prismaClient.comp_grids.findMany as any).mockResolvedValue([
      {
        comp_grid_criteria: [{ id: 'crit-a' }],
        comp_grid_levels: [{ name: 'payer' }, { name: 'payee' }],
      },
    ]);

    const result = await commissions.getCompGridCriteria(
      profile,
      rule,
      lookupData,
      matchedCompany,
      new LRUCacheService({ max: 300 })
    );

    expect(result).toHaveLength(1);
  });
});

describe('profileCompanyCheck', () => {
  it('handles single_carrier_mode with matching company name', async () => {
    const profile = {
      agent_commission_schedule_profile: {
        single_carrier_mode: true,
        company: { company_name: 'Acme Inc' },
      },
    };
    const statement = {
      carrier_name: 'acme inc',
      writing_carrier_name: 'Other Co',
    };

    const result = commissions.profileCompanyCheck(profile, statement);
    expect(result.firstValidatorCheck).toBe(true);
    expect(result.secondValidatorCheck).toBe(true);
  });

  it('handles multi-carrier mode with no companies', async () => {
    const profile = {
      agent_commission_schedule_profile: {
        single_carrier_mode: false,
        companies: [],
      },
    };
    const statement = {
      carrier_name: 'Unknown',
      writing_carrier_name: 'Unknown',
    };

    const result = commissions.profileCompanyCheck(profile, statement);
    expect(result.matchedCompany).toBe(null);
    expect(result.firstValidatorCheck).toBe(true);
    expect(result.secondValidatorCheck).toBe(true);
  });

  it('finds a matched company in multi-carrier mode', async () => {
    const profile = {
      agent_commission_schedule_profile: {
        single_carrier_mode: false,
        companies: [{ company_name: 'Alpha' }, { company_name: 'Beta' }],
      },
    };
    const statement = {
      carrier_name: 'Beta',
      writing_carrier_name: '',
    };

    const result = commissions.profileCompanyCheck(profile, statement);
    expect(result.matchedCompany?.company_name).toBe('Beta');
    expect(result.firstValidatorCheck).toBe(true);
    expect(result.secondValidatorCheck).toBe(true);
  });
});

describe('fetchProfileCompanies', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('does nothing if compProfiles is undefined', async () => {
    await expect(
      commissions.fetchProfileCompanies(undefined)
    ).resolves.toBeUndefined();
  });

  it('assigns companies to multi-carrier profiles and sets empty array for single-carrier profiles', async () => {
    const multiCarrierProfile = {
      agent_commission_schedule_profile: {
        id: 'p1',
        account_id: 'a1',
        single_carrier_mode: false,
        companies: [],
      },
    };

    const singleCarrierProfile = {
      agent_commission_schedule_profile: {
        id: 'p2',
        account_id: 'a1',
        single_carrier_mode: true,
        companies: [],
      },
    };

    const compProfiles = [multiCarrierProfile, singleCarrierProfile];

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (prismaClient.company_mappings.findMany as any).mockResolvedValue([
      { record_id: 'p1', company_id: 'c1' },
      { record_id: 'p1', company_id: 'c2' },
    ]);

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (prismaClient.companies.findMany as any).mockResolvedValue([
      { id: 'c1', company_name: 'Company A' },
      { id: 'c2', company_name: 'Company B' },
    ]);

    await commissions.fetchProfileCompanies(compProfiles);

    expect(
      multiCarrierProfile.agent_commission_schedule_profile.companies
    ).toEqual([
      { id: 'c1', company_name: 'Company A' },
      { id: 'c2', company_name: 'Company B' },
    ]);

    expect(
      singleCarrierProfile.agent_commission_schedule_profile.companies
    ).toEqual([]);
  });

  it('handles missing companies gracefully', async () => {
    const profile = {
      agent_commission_schedule_profile: {
        id: 'p3',
        account_id: 'a3',
        single_carrier_mode: false,
        companies: [],
      },
    };

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (prismaClient.company_mappings.findMany as any).mockResolvedValue([
      { record_id: 'p3', company_id: 'c99' },
    ]);

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (prismaClient.companies.findMany as any).mockResolvedValue([]);

    await commissions.fetchProfileCompanies([profile]);

    expect(profile.agent_commission_schedule_profile.companies).toEqual([]);
  });
});

describe('flatCompProfileSets', () => {
  it('should flatten and deduplicate comp profiles by profile ID', () => {
    const mockSet = [
      {
        id: 'set-1',
        extraField: 'some-meta',
        agent_commission_schedule_profiles_sets: {
          commission_profiles: [
            { id: 'p1', name: 'Profile 1' },
            { id: 'p2', name: 'Profile 2' },
            { id: 'p1', name: 'Profile 1 (duplicate)' },
          ],
        },
      },
      {
        id: 'set-2',
        extraField: 'meta-2',
        agent_commission_schedule_profiles_sets: {
          commission_profiles: [
            { id: 'p3', name: 'Profile 3' },
            { id: 'p2', name: 'Profile 2 (duplicate)' },
          ],
        },
      },
    ];

    const result = commissions.flatCompProfileSets(mockSet);

    expect(result).toHaveLength(3);

    const profileIds = result.map(
      (p: { agent_commission_schedule_profile: { id: string } }) =>
        p.agent_commission_schedule_profile.id
    );
    expect(profileIds).toContain('p1');
    expect(profileIds).toContain('p2');
    expect(profileIds).toContain('p3');

    for (const item of result) {
      expect(item).toHaveProperty('agent_commission_schedule_profile');
      expect(item).toHaveProperty('agent_commission_schedule_profile_id');
      expect(item).toHaveProperty('extraField');
    }
  });

  it('should return an empty array if no profiles exist', () => {
    const mockSet = [
      {
        id: 'empty',
        agent_commission_schedule_profiles_sets: {
          commission_profiles: [],
        },
      },
    ];

    const result = commissions.flatCompProfileSets(mockSet);
    expect(result).toEqual([]);
  });

  it('should handle undefined agent_commission_schedule_profiles_sets gracefully', () => {
    const mockSet = [
      {
        id: 'null-case',
        agent_commission_schedule_profiles_sets: null,
      },
    ];

    const result = commissions.flatCompProfileSets(mockSet);
    expect(result).toEqual([]);
  });

  it('should return an empty array for an empty input array', () => {
    const mockSet = [];

    const result = commissions.flatCompProfileSets(mockSet);
    expect(result).toEqual([]);
  });

  it('should return an empty array for null or undefined input', () => {
    const resultForNull = commissions.flatCompProfileSets(null);
    expect(resultForNull).toEqual([]);

    const resultForUndefined = commissions.flatCompProfileSets(undefined);
    expect(resultForUndefined).toEqual([]);
  });
});

describe('filterGridsByLevelName', () => {
  const compGridsRaw = [
    {
      comp_grid_levels: [
        { name: 'L1', id: 1, comp_grid_rates: [{ comp_grid_level_id: 1 }] },
        { name: 'L2', id: 2, comp_grid_rates: [{ comp_grid_level_id: 2 }] },
      ],
    },
    {
      comp_grid_levels: [
        { name: 'L2', id: 2, comp_grid_rates: [{ comp_grid_level_id: 2 }] },
        { name: 'L3', id: 3, comp_grid_rates: [{ comp_grid_level_id: 3 }] },
      ],
    },
    {
      comp_grid_levels: [
        { name: 'L1', id: 1, comp_grid_rates: [{ comp_grid_level_id: 1 }] },
        { name: 'L3', id: 3, comp_grid_rates: [{ comp_grid_level_id: 3 }] },
      ],
    },
  ];

  it('should filter grids matching any level name', () => {
    const result = commissions.filterGridsByLevelName(compGridsRaw, ['L1']);
    expect(result).toHaveLength(2);
    expect(result).toEqual([
      {
        comp_grid_levels: [
          { name: 'L1', id: 1, comp_grid_rates: [{ comp_grid_level_id: 1 }] },
        ],
      },
      {
        comp_grid_levels: [
          { name: 'L1', id: 1, comp_grid_rates: [{ comp_grid_level_id: 1 }] },
        ],
      },
    ]);
  });

  it('should filter grids matching all level names', () => {
    const result = commissions.filterGridsByLevelName(compGridsRaw, [
      'L1',
      'L2',
    ]);
    expect(result).toHaveLength(3);
    expect(result).toEqual([
      {
        comp_grid_levels: [
          { name: 'L1', id: 1, comp_grid_rates: [{ comp_grid_level_id: 1 }] },
          { name: 'L2', id: 2, comp_grid_rates: [{ comp_grid_level_id: 2 }] },
        ],
      },
      {
        comp_grid_levels: [
          { name: 'L2', id: 2, comp_grid_rates: [{ comp_grid_level_id: 2 }] },
        ],
      },
      {
        comp_grid_levels: [
          { name: 'L1', id: 1, comp_grid_rates: [{ comp_grid_level_id: 1 }] },
        ],
      },
    ]);
  });

  it('should return an empty array if no grids match any level name', () => {
    const result = commissions.filterGridsByLevelName(compGridsRaw, ['L4']);
    expect(result).toHaveLength(0);
  });

  it('should return filter grids matching grid level name L4', () => {
    const result = commissions.filterGridsByLevelName(compGridsRaw, [
      'L1',
      'L4',
    ]);
    expect(result).toHaveLength(2);
    expect(result).toEqual([
      {
        comp_grid_levels: [
          { name: 'L1', id: 1, comp_grid_rates: [{ comp_grid_level_id: 1 }] },
        ],
      },
      {
        comp_grid_levels: [
          { name: 'L1', id: 1, comp_grid_rates: [{ comp_grid_level_id: 1 }] },
        ],
      },
    ]);
  });

  it('should return an empty array if compGridsRaw is empty', () => {
    const result = commissions.filterGridsByLevelName([], ['L1']);
    expect(result).toHaveLength(0);
  });
});

describe('initializePayeeAndPayerCompGridLevels', () => {
  it('should initialize empty comp grid levels if none exist', () => {
    const profileData = {
      payee_comp_grid_level: null,
      payer_comp_grid_level: null,
    };
    commissions.initializeCompGridLevels(
      profileData,
      'payee_comp_grid_level',
      []
    );
    commissions.initializeCompGridLevels(
      profileData,
      'payer_comp_grid_level',
      []
    );

    expect(profileData.payee_comp_grid_level).toEqual({ comp_grid_rates: [] });
    expect(profileData.payer_comp_grid_level).toEqual({ comp_grid_rates: [] });
  });

  it('should populate comp grid rates from payeeCompGridsLevel', () => {
    const profileData = {
      payee_comp_grid_level: null,
      payer_comp_grid_level: null,
    };
    const payeeCompGridsLevel = [
      { comp_grid_rates: [{ rate: 40 }] },
      { comp_grid_rates: [{ rate: 50 }] },
    ];
    commissions.initializeCompGridLevels(
      profileData,
      'payee_comp_grid_level',
      payeeCompGridsLevel
    );
    commissions.initializeCompGridLevels(
      profileData,
      'payer_comp_grid_level',
      []
    );

    expect(profileData.payee_comp_grid_level.comp_grid_rates).toEqual([
      { rate: 40 },
      { rate: 50 },
    ]);
    expect(profileData.payer_comp_grid_level).toEqual({ comp_grid_rates: [] });
  });

  it('should populate comp grid rates from payerCompGridsLevel', () => {
    const profileData = {
      payee_comp_grid_level: null,
      payer_comp_grid_level: null,
    };
    const payerCompGridsLevel = [
      { comp_grid_rates: [{ rate: 60 }] },
      { comp_grid_rates: [{ rate: 70 }] },
    ];
    commissions.initializeCompGridLevels(
      profileData,
      'payee_comp_grid_level',
      []
    );
    commissions.initializeCompGridLevels(
      profileData,
      'payer_comp_grid_level',
      payerCompGridsLevel
    );

    expect(profileData.payee_comp_grid_level).toEqual({ comp_grid_rates: [] });
    expect(profileData.payer_comp_grid_level.comp_grid_rates).toEqual([
      { rate: 60 },
      { rate: 70 },
    ]);
  });

  it('should merge comp grid rates from both payee and payer levels', () => {
    const profileData = {
      payee_comp_grid_level: null,
      payer_comp_grid_level: null,
    };
    const payeeCompGridsLevel = [{ comp_grid_rates: [{ rate: 80 }] }];
    const payerCompGridsLevel = [{ comp_grid_rates: [{ rate: 90 }] }];
    commissions.initializeCompGridLevels(
      profileData,
      'payee_comp_grid_level',
      payeeCompGridsLevel
    );
    commissions.initializeCompGridLevels(
      profileData,
      'payer_comp_grid_level',
      payerCompGridsLevel
    );

    expect(profileData.payee_comp_grid_level.comp_grid_rates).toEqual([
      { rate: 80 },
    ]);
    expect(profileData.payer_comp_grid_level.comp_grid_rates).toEqual([
      { rate: 90 },
    ]);
  });
});

describe('fetchCompGrids', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const mockProfileData = {
    payer_grid_level_name: 'Level A',
    payee_grid_level_name: 'Level B',
  };

  const mockMatchedCompany = { id: 'company-123' };

  it('fetches grids with matched company', async () => {
    (
      prismaClient.comp_grids.findMany as unknown as jest.Mock
    ).mockResolvedValueOnce([
      {
        id: 'grid-1',
        comp_grid_levels: [],
      },
    ]);
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (prismaClient.comp_grid_levels.findMany as any).mockResolvedValueOnce([
      {
        id: 'level-1',
        name: 'Level A',
        comp_grid_rates: [],
        related_comp_grids: [{ id: 'grid-1' }],
      },
    ]);

    const result = await commissions.fetchCompGrids(
      mockProfileData,
      mockMatchedCompany,
      new LRUCacheService({ max: 300 })
    );

    expect(prismaClient.comp_grids.findMany).toHaveBeenCalledWith({
      where: {
        company_id: 'company-123',
        state: 'active',
      },
      include: expect.any(Object),
    });

    expect(result).toEqual([
      {
        id: 'grid-1',
        comp_grid_levels: [
          {
            id: 'level-1',
            name: 'Level A',
            comp_grid_rates: [],
          },
        ],
      },
    ]);
  });

  it('fetches grids without a matched company', async () => {
    (
      prismaClient.comp_grids.findMany as unknown as jest.Mock
    ).mockResolvedValueOnce([
      {
        id: 'grid-2',
        comp_grid_levels: [
          {
            id: 'level-2',
            name: 'Level B',
          },
        ],
      },
    ]);

    const result = await commissions.fetchCompGrids(
      mockProfileData,
      null,
      new LRUCacheService({ max: 300 })
    );

    expect(prismaClient.comp_grids.findMany).toHaveBeenCalledWith({
      where: {
        state: 'active',
      },
      include: expect.any(Object),
    });

    expect(result).toEqual([
      {
        id: 'grid-2',
        comp_grid_levels: [
          {
            id: 'level-2',
            name: 'Level B',
          },
        ],
      },
    ]);
  });

  it('handles empty levels gracefully', async () => {
    (
      prismaClient.comp_grids.findMany as unknown as jest.Mock
    ).mockResolvedValueOnce([
      {
        id: 'grid-3',
        comp_grid_levels: [],
      },
    ]);
    (
      prismaClient.comp_grid_levels.findMany as unknown as jest.Mock
    ).mockResolvedValueOnce([]);

    const result = await commissions.fetchCompGrids(
      mockProfileData,
      mockMatchedCompany,
      new LRUCacheService({ max: 300 })
    );

    expect(result).toEqual([
      {
        id: 'grid-3',
        comp_grid_levels: [],
      },
    ]);
  });
});
