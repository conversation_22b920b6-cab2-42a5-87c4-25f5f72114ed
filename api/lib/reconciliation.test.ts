import { describe, it, expect } from 'vitest';
import { Decimal } from '@prisma/client/runtime/library';
import type { statement_data, report_data } from '@prisma/client';

import {
  getKeyMethods,
  applyTransformers,
  funcLib,
  getScoreMethod,
  getFieldConfigs,
  digestReconciler,
} from './reconciliation';

const baseStatementData: statement_data = {
  id: 1,
  policy_id: 'foo',
  customer_name: '<PERSON>',
  agent_name: '<PERSON>',
  writing_carrier_name: 'Carrier X',
  carrier_name: 'Carrier X',
  product_type: 'Life',
  product_sub_type: null,
  effective_date: new Date('2024-01-01'),
  transaction_type: 'new',
  created_at: new Date(),
  updated_at: new Date(),
  str_id: '',
  account_id: '',
  uid: '',
  state: '',
  processing_status: '',
  created_by: '',
  created_proxied_by: '',
  updated_by: '',
  updated_proxied_by: '',
  account_type: '',
  advanced_commission_amount: new Decimal(0),
  agent_commission_payout_rate: '',
  agent_commissions: '',
  agent_commissions_log: '',
  agent_commissions_status: '',
  agent_commissions_status2: '',
  agent_commissions_v2: '',
  agent_id: '',
  agent_payout_rate: '',
  agent_payout_rate_override: '',
  aggregation_id: '',
  allocated_amount: new Decimal(0),
  bill_mode: '',
  carrier_rate: '',
  commission_amount: new Decimal(0),
  commission_basis: '',
  commission_paid_amount: new Decimal(0),
  commission_rate: '',
  commission_rate_percent: new Decimal(0),
  commissionable_premium_amount: new Decimal(0),
  compensation_type: '',
  contacts: [],
  customer_paid_premium_amount: new Decimal(0),
  document_id: '',
  fees: new Decimal(0),
  flags: '',
  flags_log: '',
  geo_state: '',
  group_id: '',
  group_name: '',
  import_id: '',
  internal_id: '',
  invoice_date: undefined,
  issue_age: 0,
  is_virtual: false,
  master_id: 0,
  member_count: 0,
  new_carrier_rate: new Decimal(0),
  new_commission_rate: new Decimal(0),
  notes: '',
  parent_id: 0,
  payment_date: undefined,
  payment_mode: '',
  payment_status: '',
  period_date: undefined,
  premium_amount: new Decimal(0),
  premium_type: '',
  processing_date: undefined,
  product_name: '',
  product_option_name: '',
  reconciled_at: undefined,
  reconciliation_method: '',
  reconciliation_stats: '',
  reconciliation_status: '',
  remain_amount: new Decimal(0),
  report_data_id: 0,
  split_percentage: new Decimal(0),
  standardized_customer_name: '',
  statement_number: '',
  status: '',
  sync_id: '',
  tags: [],
  type: '',
  virtual_type: 'grouped',
};

const baseReportData: report_data = {
  id: 2,
  policy_id: 'bar',
  customer_name: 'Bob',
  agent_name: 'Agent Jones',
  writing_carrier_name: 'Carrier Y',
  product_type: 'Health',
  product_sub_type: null,
  effective_date: new Date('2024-02-01'),
  notes: null,
  product_name: 'Health Plus',
  issue_age: 35,
  group_id: null,
  internal_id: null,
  transaction_type: 'renewal',
  policy_status: 'active',
  cancellation_date: null,
  reinstatement_date: null,
  agent_id: null,
  created_at: new Date(),
  updated_at: new Date(),
  str_id: '',
  account_id: '',
  uid: '',
  state: '',
  processing_status: '',
  created_by: '',
  created_proxied_by: '',
  updated_by: '',
  updated_proxied_by: '',
  account_type: '',
  agent_payout_rate_override: '',
  aggregation_id: '',
  commissionable_premium_amount: new Decimal(0),
  contacts: [],
  customer_paid_premium_amount: new Decimal(0),
  document_id: '',
  flags: '',
  flags_log: '',
  geo_state: '',
  group_name: '',
  import_id: '',
  is_virtual: false,
  parent_id: 0,
  payment_mode: '',
  premium_amount: new Decimal(0),
  product_option_name: '',
  reconciliation_method: '',
  reconciliation_stats: '',
  reconciliation_status: '',
  split_percentage: new Decimal(0),
  sync_id: '',
  tags: [],
  type: '',
  virtual_type: 'grouped',
  agent_comp_profiles_rates: '',
  aggregation_primary: false,
  commission_profile_id: '',
  commissions_expected: new Decimal(0),
  company_id: 0,
  company_product_id: 0,
  config: '',
  contacts_commission_split: '',
  contacts_split: '',
  customer_first_name: '',
  customer_id: 0,
  customer_last_name: '',
  dba: '',
  embedding_info: '',
  first_payment_date: undefined,
  first_processed_date: undefined,
  policy_date: undefined,
  policy_term_months: 0,
  excess_amount: new Decimal(0),
  receivable_schedule: '',
  signed_date: undefined,
  sync_worker: '',
};

describe('getKeyMethods', () => {
  it('Given default reconciler, should return functions that extract policy_id', () => {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const reconciler = { method_type: 'default' } as any;
    const { getStatementKey, getPolicyKey } = getKeyMethods(reconciler);

    expect(getStatementKey({ ...baseStatementData, policy_id: 'foo' })).toBe(
      'foo'
    );
    expect(getPolicyKey({ ...baseReportData, policy_id: 'bar' })).toBe('bar');
  });

  it('Given key-config reconciler, should return functions based on config', () => {
    const reconciler = {
      method_type: 'key-config',
      key_config_statement: JSON.stringify([
        { field: 'policy_id', transformers: [] },
      ]),
      key_config_report: JSON.stringify([
        { field: 'customer_name', transformers: [] },
      ]),
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any;
    const { getStatementKey, getPolicyKey } = getKeyMethods(reconciler);
    expect(getStatementKey({ ...baseStatementData, policy_id: 'baz' })).toBe(
      'baz'
    );
    expect(getPolicyKey({ ...baseReportData, customer_name: 'qux' })).toBe(
      'qux'
    );
  });

  it('Given key-custom reconciler, should return custom eval functions', () => {
    const reconciler = {
      method_type: 'key-custom',
      key_config_statement: `(statement) => statement.customer_name + '-custom'`,
      key_config_report: `(policy) => policy.agent_name + '-custom'`,
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any;
    const { getStatementKey, getPolicyKey } = getKeyMethods(reconciler);
    expect(
      getStatementKey({ ...baseStatementData, customer_name: 'Alice' })
    ).toBe('Alice-custom');
    expect(getPolicyKey({ ...baseReportData, agent_name: 'Agent Jones' })).toBe(
      'Agent Jones-custom'
    );
  });

  it('Given key-config reconciler with multiple fields, should concatenate keys', () => {
    const reconciler = {
      method_type: 'key-config',
      key_config_statement: JSON.stringify([
        { field: 'policy_id', transformers: [] },
        { field: 'customer_name', transformers: [] },
      ]),
      key_config_report: JSON.stringify([
        { field: 'policy_id', transformers: [] },
        { field: 'agent_name', transformers: [] },
      ]),
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any;
    const { getStatementKey, getPolicyKey } = getKeyMethods(reconciler);
    expect(
      getStatementKey({
        ...baseStatementData,
        policy_id: 'foo',
        customer_name: 'Alice',
      })
    ).toBe('foo::Alice');
    expect(
      getPolicyKey({
        ...baseReportData,
        policy_id: 'bar',
        agent_name: 'Agent Jones',
      })
    ).toBe('bar::Agent Jones');
  });
});

describe('applyTransformers', () => {
  it('Given standardizeProduct transformer, should map value using provided mapping', () => {
    const params = {
      standardizeProduct: {
        mapping: JSON.stringify({
          LIFE: 'LIFE,WHOLE LIFE,TERM LIFE',
          HEALTH: 'HEALTH,HEALTH PLUS',
        }),
      },
    };
    expect(
      applyTransformers('WHOLE LIFE', ['standardizeProduct'], params)
    ).toBe('LIFE');
    expect(
      applyTransformers('HEALTH PLUS', ['standardizeProduct'], params)
    ).toBe('HEALTH');
    expect(
      applyTransformers('DISABILITY', ['standardizeProduct'], params)
    ).toBe('DISABILITY');
  });

  it('Given standardizeProductType transformer, should map value using provided mapping', () => {
    const params = {
      standardizeProductType: {
        mapping: JSON.stringify({
          GROUP: 'GROUP,EMPLOYER',
          INDIVIDUAL: 'INDIVIDUAL,SINGLE',
        }),
      },
    };
    expect(
      applyTransformers('EMPLOYER', ['standardizeProductType'], params)
    ).toBe('GROUP');
    expect(
      applyTransformers('SINGLE', ['standardizeProductType'], params)
    ).toBe('INDIVIDUAL');
    expect(
      applyTransformers('FAMILY', ['standardizeProductType'], params)
    ).toBe('FAMILY');
  });
});

describe('funcLib', () => {
  it('Given identical strings, should return 1 for distanceLevenshtein', () => {
    expect(funcLib.distanceLevenshtein('foo', 'foo')).toBe(1);
  });

  it('Given a company name with suffix, should remove company suffixes in normalizeCompany', () => {
    expect(funcLib.normalizeCompany('Acme Inc.')).toBe('Acme');
  });

  it('Given a string with &, should replace & with and in normalizeSymbols', () => {
    expect(funcLib.normalizeSymbols('A & B')).toBe('A and B');
  });
});

describe('getScoreMethod', () => {
  it('Given default reconciler, should compare policy_id', () => {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const reconciler = { method_type: 'default' } as any;
    const scoreMethod = getScoreMethod(reconciler);
    expect(
      scoreMethod(
        { ...baseStatementData, policy_id: 'foo' },
        { ...baseReportData, policy_id: 'foo' },
        funcLib
      )
    ).toBe(1);
  });
});

describe('getFieldConfigs', () => {
  it('Given no mode, should return enabled field configs', () => {
    const configs = getFieldConfigs();
    expect(Array.isArray(configs)).toBe(true);
    expect(configs.every((c) => c.enabled)).toBe(true);
  });

  it('Given mode=insurance, should enable insurance fields', () => {
    const configs = getFieldConfigs('insurance');
    expect(configs.some((c) => c.fieldId === 'writing_carrier_name')).toBe(
      true
    );
  });
});

describe('digestReconciler', () => {
  it('Given a reconciler config, should return a hash string', () => {
    const hash = digestReconciler({ method_type: 'default' });
    expect(typeof hash).toBe('string');
    expect(hash.length).toBe(32);
  });
});
