import { describe, expect, it } from 'vitest';

import * as helper from '@/lib/helpers';

describe('helpers', () => {
  describe('getEffectiveDate', (it) => {
    it('should return the effective date in utc format', () => {
      const effectiveDate = helper.getValidDate('2024-12-12 12:00:00');
      expect(effectiveDate).be.instanceOf(Date);
      expect(effectiveDate.toJSON()).eq('2024-12-12T12:00:00.000Z');
    });

    it('fails when the date is invalid', () => {
      expect(helper.getValidDate('')).toBeNull();
      expect(helper.getValidDate('2100-12-12')).toBeNull();
      expect(helper.getValidDate('1899-01-01')).toBeNull();
      expect(helper.getValidDate('0001-01-01')).toBeNull();
    });
  });

  describe('limitConcurrency', () => {
    describe('it allows retrying', (it) => {
      it('should allow retrying', async () => {
        let count = 0;
        const handler = async () => {
          count++;
          // biome-ignore lint/suspicious/noDoubleEquals: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          if (count != 2) {
            throw new Error('error');
          }
          return true;
        };

        await helper.limitConcurrency(handler, [1], 1);
        expect(count).toBe(2);
      });
      it('calls onFail if exists', async () => {
        const handler = async () => {
          throw new Error('error');
        };

        const errors = [];
        const onFail = (context) => {
          errors.push(context.error);
          expect(context.error).to.be.instanceOf(Error);
        };
        await helper.limitConcurrency(handler, [1], 1, {
          onFail,
        });
        expect(errors.length).toBe(1);
      });
    });

    it('should retry the specified number of times', async () => {
      let attempts = 0;
      const handler = async () => {
        attempts++;
        if (attempts < 3) {
          throw new Error('error');
        }
        return true;
      };

      const result = await helper.limitConcurrency(handler, [1], 1, {
        retries: 2,
        // biome-ignore lint/suspicious/noEmptyBlockStatements: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        onFail: () => {},
      });
      expect(attempts).toBe(3); // 1 initial attempt + 2 retries
      expect(result).toEqual([true]);
    });

    it('should not retry when retries is set to 0', async () => {
      let attempts = 0;
      const handler = async () => {
        attempts++;
        throw new Error('error');
      };

      const errors = [];
      const onFail = (context) => {
        errors.push(context.error);
      };

      await helper.limitConcurrency(handler, [1], 1, { retries: 0, onFail });
      expect(attempts).toBe(1); // Only the initial attempt
      expect(errors.length).toBe(1);
      expect(errors[0]).toBeInstanceOf(Error);
    });

    it('should maintain order of input data in results', async () => {
      const inputData = [1, 2, 3, 4, 5];
      const handler = async (data: number) => {
        // Add random delay to simulate async operations completing in different order
        const delay = Math.random() * 100;
        await new Promise((resolve) => setTimeout(resolve, delay));
        return data * 2;
      };

      const results = await helper.limitConcurrency(handler, inputData, 2);
      expect(results).toEqual([2, 4, 6, 8, 10]);
    });

    it('should maintain order even with mixed success and failures', async () => {
      const inputData = [1, 2, 3, 4, 5];
      const handler = async (data: number) => {
        const delay = Math.random() * 50;
        await new Promise((resolve) => setTimeout(resolve, delay));

        if (data === 3) {
          throw new Error('Task failed');
        }
        return data * 2;
      };

      const errors = [];
      const onFail = (context) => {
        errors.push({
          index: inputData.indexOf(context.data),
          error: context.error,
        });
      };

      const finalResults = await helper.limitConcurrency(
        handler,
        inputData,
        2,
        { onFail }
      );

      // Check that successful results are in correct positions
      expect(finalResults[0]).toBe(2); // 1 * 2
      expect(finalResults[1]).toBe(4); // 2 * 2
      expect(finalResults[2]).toBeUndefined(); // Failed task
      expect(finalResults[3]).toBe(8); // 4 * 2
      expect(finalResults[4]).toBe(10); // 5 * 2

      // Check that error was recorded for the correct item
      expect(errors.length).toBe(1);
      expect(errors[0].index).toBe(2); // Index of item 3
    });

    it('should work with large datasets and maintain order', async () => {
      const inputData = Array.from({ length: 50 }, (_, i) => i + 1);
      const handler = async (data: number) => {
        // Simulate varying processing times
        const delay = Math.random() * 10;
        await new Promise((resolve) => setTimeout(resolve, delay));
        return data * 3;
      };

      const results = await helper.limitConcurrency(handler, inputData, 5);
      const expectedResults = inputData.map((x) => x * 3);
      expect(results).toEqual(expectedResults);
    });

    it('should handle concurrent operations with different data types', async () => {
      const inputData = ['apple', 'banana', 'cherry', 'date'];
      const handler = async (fruit: string) => {
        const delay = Math.random() * 30;
        await new Promise((resolve) => setTimeout(resolve, delay));
        return fruit.toUpperCase();
      };

      const results = await helper.limitConcurrency(handler, inputData, 3);
      expect(results).toEqual(['APPLE', 'BANANA', 'CHERRY', 'DATE']);
    });
  });
});
