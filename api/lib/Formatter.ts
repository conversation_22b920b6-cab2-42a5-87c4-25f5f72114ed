import * as chrono from 'chrono-node';
import { AccountIds } from 'common/constants';
import type { Document } from 'common/documents/documents.types';
import currency from 'currency.js';
import dayjs from 'dayjs';
import { capitalize } from 'lodash-es';

export interface ContactRecordWithId {
  id: number;
  str_id: string;
  first_name?: string | null;
  last_name?: string | null;
  email?: string | null;
}

export interface CompGridCriterionExport {
  company: {
    company_name: string;
  };
  comp_grid_product: {
    type: string;
    name?: string;
    product_name?: string;
    id?: string;
  };
  policy_year_start?: number;
  policy_year_end?: number;
  issue_age_start?: number;
  issue_age_end?: number;
  compensation_type?: string;
  transaction_type?: string;
}

export interface MatchCriteria {
  field: string;
  op: string; // The operator (e.g., '=', '!=', '>', '<', etc.)
  value: string;
}

export interface DateFormatterOptions {
  startOfMonth?: boolean;
}

export interface ContactFormatterOptions {
  account_id?: string; // Used to determine if the contact is from TransGlobal
  last_first?: boolean; // If true, format as "Last, First"
  incl_email?: boolean; // If true, include email in the format
  incl_id?: boolean; // If true, include contact ID in the format
}

// biome-ignore lint/complexity/noStaticOnlyClass: Likely deprecating/removing this file and moving to common/
class Formatter {
  /**
   * @deprecated Use formatCurrency from common/helpers/formatCurrency.ts
   * @param {} s
   * @returns
   */
  static currency = (s: string | number | undefined | unknown): string => {
    if (s === undefined || s === '') return '';
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let s1: any = s;
    if (typeof s1 === 'string') {
      s1 = s1.trim();
    }
    if (typeof s1 === 'string') {
      s1 = s1.replaceAll(',', '');
      s1 = s1.replaceAll('$', '');
    }
    if (typeof s1 === 'string' && s1.split('\n').length > 1) {
      [s1] = s1.split('\n');
    }
    if (typeof s1 === 'string' && s1.split(' ').length > 1) {
      [s1] = s1.split(' ');
    }
    if (typeof s1 === 'string' && s1.endsWith('-')) {
      s1 = `-${s1.slice(0, -1)}`;
    }
    if (typeof s1 === 'string' && s1.startsWith('(') && s1.endsWith(')')) {
      s1 = `-${s1.slice(1, -1)}`;
    }
    s1 = parseFloat(s1);
    if (Number.isNaN(s1)) {
      s1 = '';
    } else {
      // Using currency for now, but should be replaced with BigNumber
      s1 = currency(s1).format();

      // const bigNumber = new BigNumber(s1);
      // s1 = bigNumber.toFormat(2, BigNumber.ROUND_HALF_UP);

      // Example:
      // -0.375: Original value
      // -0.38: Rounded using BigNumber with ROUND_HALF_UP
      // -$0.37: Rounded and formatted using currency.js
    }
    return s1;
  };

  /**
   * @deprecated Use percentage from common/Formatter.ts
   * @param {} s
   * @returns
   */
  static percentage = (s: unknown): string | unknown => {
    if (typeof s === 'number') {
      if (Number.isFinite(s)) {
        return `${s.toFixed(3)}%`;
      } else {
        return 'n/a';
      }
    }
    return s;
  };

  static asPercentage = (s: unknown): string | unknown => {
    if (typeof s === 'number') {
      if (Number.isFinite(s)) {
        return `${(s * 100).toFixed(2)}%`;
      } else {
        return 'n/a';
      }
    }
    return s;
  };

  static date = (
    s: Date | number | string | null | undefined,
    options: DateFormatterOptions = {}
  ) => {
    let result = '';
    if (!s) {
      return '';
    }
    if (typeof s === 'object') {
      result = dayjs(
        new Date(s.getTime() + s.getTimezoneOffset() * 60 * 1000)
      ).format('MM/DD/YYYY');
    } else if (typeof s === 'number') {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error('Unsupported date format', s);
      let bom = dayjs('1/1/1900').add(s - 2, 'days');
      if (options.startOfMonth) {
        bom = bom.startOf('month');
      }
      result = bom.format('MM/DD/YYYY');
    } else if (typeof s === 'string') {
      const parsedDate = chrono.parseDate(s);
      if (parsedDate) {
        // Hack for time zone offset
        let bom = dayjs(
          new Date(
            parsedDate.getTime() + parsedDate.getTimezoneOffset() * 60 * 1000
          )
        );
        if (options.startOfMonth) {
          bom = bom.startOf('month');
        }
        result = bom.format('MM/DD/YYYY');
      } else {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.error(`Couldn't parse '${s}' as a date.`);
        result = '';
      }
    }
    return result;
  };

  static contact = (
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    contact: ContactRecordWithId | string | any,
    opts: ContactFormatterOptions = {}
  ) => {
    const isTransGlobal = opts?.account_id === AccountIds.TRANSGLOBAL;
    const _lastFirst = (isTransGlobal || opts?.last_first) ?? false;

    if (!contact) {
      return '';
    }
    const arr = [];
    const nameArr = [];
    if (contact.first_name) nameArr.push(contact.first_name.trim());
    if (contact.last_name) nameArr.push(contact.last_name.trim());
    if (_lastFirst) {
      nameArr.reverse();
      arr.push(nameArr.join(', '));
    } else {
      arr.push(nameArr.join(' '));
    }
    if (opts?.incl_id) {
      const nameStr = _lastFirst ? nameArr.join(', ') : nameArr.join(' ');
      return `${contact.str_id}::${nameStr}`;
    }
    if (opts?.incl_email && contact.email)
      arr.push(`(${contact.email.trim()})`);
    const str = arr.join(' ');
    return str;
  };

  static contacts = (
    ids: string[] | number[] | undefined,
    options: ContactRecordWithId[] = [],
    useId: boolean = false
  ) => {
    ids = ids || [];
    return ids.map((id) => {
      const contactData = options.find((contact) =>
        useId ? +contact.id === +id : contact.str_id === String(id)
      );
      if (!contactData) {
        return '';
      }
      return `${contactData.first_name} ${contactData.last_name}`;
    });
  };

  static statementDocuments = (
    str_id: string | number,
    documents: Document[]
  ) => {
    const documentData = documents.find(
      (document) => document.str_id === String(str_id)
    );
    if (!documentData) {
      return '';
    }
    return `${documentData.filename}`;
  };

  /**
   * @deprecated duplicated function, the function "statementDocuments" does the same thing
   */
  static reportDocuments = (str_id: string, documents: Document[]) => {
    const documentData = documents.find(
      (document) => document.str_id === str_id
    );
    if (!documentData) {
      return '';
    }
    return `${documentData.filename}`;
  };

  static fieldMatcher = (criteria: MatchCriteria) =>
    `"${criteria?.field}" ${criteria?.op} "${criteria?.value}"`;

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  static type = (type: any) => {
    if (!type) return '';

    if (Array.isArray(type)) {
      return this.uppercaseFirstLetter(type[0]?.toString());
    }

    if (typeof type === 'object') {
      return this.uppercaseFirstLetter(JSON.stringify(type));
    }

    return this.uppercaseFirstLetter(type);
  };

  static uppercaseFirstLetter(s: string) {
    return capitalize(s);
  }

  static dateStringToLocaleString(value: string | Date | null) {
    if (!value) {
      return '';
    }
    return dayjs(value).toISOString();
  }

  static getDynamicSelectFormatter =
    (
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      stringFn = (o: Record<string, any>) => o?.name,
      matchKey: string = 'id',
      defaultString: string = ''
    ) =>
    (val, collection) => {
      if (!Array.isArray(collection)) return '';
      const match =
        Array.isArray(collection) &&
        collection?.find((e) => e[matchKey] === val);
      const str = stringFn(match);
      return str || defaultString;
    };

  /**
   * @deprecated function is not being used nowhere
   */
  static compGridCriterionExport = (val: CompGridCriterionExport): string =>
    val
      ? `${val.comp_grid_product.type || '(No product type)'} • ${
          val.comp_grid_product.name ?? 'n/a'
        } • Years (${Formatter.numberRange(
          val.policy_year_start,
          val.policy_year_end
        )}) • Ages (${Formatter.numberRange(
          val.issue_age_start,
          val.issue_age_end
        )})${val.compensation_type ? ` • ${val.compensation_type}` : ''}${
          val.transaction_type ? ` • ${val.transaction_type}` : ''
        }`
      : '';

  /**
   * @deprecated function is not used outside of this file
   */
  static numberRange = (start, end) => {
    if (![null, undefined].includes(start) && start === end) {
      return start;
    }
    if (start && end) {
      return `${start}-${end}`;
    }
    if (start) {
      return `${start}+`;
    }
    if (end) {
      return `< ${end}`;
    }
    return 'any';
  };

  static criteriaFormatter = (val: CompGridCriterionExport) =>
    val
      ? `Company: ${val?.company?.company_name} • ${Formatter.compGridCriterionExport(val)}`
      : '';
}

export default Formatter;
