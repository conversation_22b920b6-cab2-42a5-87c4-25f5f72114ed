import pRetry from 'p-retry';
import Statsig from 'statsig-node';

// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
const checkFeature = async (name, account_id, defaultValue?: any) => {
  return await pRetry(
    async () => {
      await Statsig.initialize(process.env.STATSIG_API_KEY, {
        environment: { tier: process.env.ENVIRONMENT },
      });
      const user = {
        userID: account_id,
      };
      return Statsig.checkGateSync(user, name);
    },
    { retries: 3, minTimeout: 100 }
  ).catch((err) => {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.warn('Statsig checking failure: ', err);
    return defaultValue;
  });
};

export default checkFeature;
