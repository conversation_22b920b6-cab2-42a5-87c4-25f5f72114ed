import Formatter from 'common/Formatter';
import {
  TransactionParty,
  TransactionRate,
  TransactionType,
} from 'common/globalTypes';
// biome-ignore lint/correctness/noUndeclaredDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import isNil from 'lodash/isNil';
// biome-ignore lint/correctness/noUndeclaredDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import isString from 'lodash/isString';
// biome-ignore lint/correctness/noUndeclaredDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import setInObject from 'lodash/set';
// biome-ignore lint/style/useImportType: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import { ReceivablesAgentIdToRateMap } from 'common/types/receivableRates';

import type { AccountingTransactionDetails } from '../../pages/api/statement_data/types';

export type AddReceivableValuesToCommissionDataInput = {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  [key: string]: any;
} & {
  accounting_transaction_details?: Array<AccountingTransactionDetails>;
};

const setAgentIdToRateMap = (input: {
  row: AddReceivableValuesToCommissionDataInput;
  receivableFieldPath: string;
  agentId?: string;
  rate: number;
}) => {
  if (!input.agentId) {
    return;
  }

  const { row, receivableFieldPath, agentId, rate } = input;
  const currentMap = row[receivableFieldPath] || {};
  const updatedMap: ReceivablesAgentIdToRateMap = {
    ...currentMap,
    [agentId]: rate,
  };

  setInObject(row, receivableFieldPath, updatedMap);
};

const getRateFormatted = (
  rate: number,
  contact?: AddReceivableValuesToCommissionDataInput['accounting_transaction_details'][number]['contact']
) => {
  const percentageRate = isNil(rate)
    ? ''
    : `${Formatter.percentage(rate, { isPercentage: true })}`;

  const contactName = Formatter.contact(contact);
  const agentAndRateLabel = contactName
    ? `${contactName}: ${percentageRate}`
    : percentageRate;

  return { percentageRate, agentAndRateLabel };
};

export const set = (args: {
  row: AddReceivableValuesToCommissionDataInput;
  key: string;
  value: string;
  valueRaw?: number;
  agentId?: string;
  transactionDetailStrId?: string;
}) => {
  const { row, key, value, valueRaw } = args;
  const str_id = args.transactionDetailStrId;

  const getDataToSet = () => ({
    value,
    str_id,
    ...(!isNil(valueRaw) ? { valueRaw: valueRaw } : {}),
    ...(!isNil(args.agentId) ? { agentId: args.agentId } : {}),
  });

  if (!row[key]) {
    return setInObject(row, key, getDataToSet());
  }

  const currentValue = row[key];

  if (Array.isArray(currentValue)) {
    const isDuplicate = currentValue.some(
      (item) => item.value === value && item.str_id === str_id
    );
    if (!isDuplicate) {
      currentValue.push(getDataToSet());
    }
    return row;
  }

  if (typeof currentValue === 'object' && currentValue !== null) {
    const newArray = [currentValue];
    const isDuplicate = newArray.some(
      (item) => item.value === value && item.str_id === str_id
    );
    if (!isDuplicate) {
      newArray.push(getDataToSet());
    }
    row[key] = newArray;
    return row;
  }

  return setInObject(row, key, getDataToSet());
};

/**
 * This function adds receivable values to the commission data input object based
 * when accounting_transaction_details is provided. Then, the following
 * fields are set in the input object by specific values to the corresponding
 * mapping:
 * - `receivable_value_agent_rate`: Contains rates info for 'agent' party.
 * - `receivable_value_agency_rate`: Contains rates info for 'policy' party.
 * - `receivable_value_override_rate`: Contains rates info for 'agency' party.
 *
 * @example
 * ```
 * addReceivableValuesToCommissionData(input)
 * // returns {
 *      ...input,
 *      receivable_value_agent_rate: [{ value, str_id}, ...],
 *      receivable_value_agency_rate: [...],
 *      receivable_value_override_rate: [...],
 * }
 * ```
 */
export const addReceivableValuesToCommissionData = (
  row: AddReceivableValuesToCommissionDataInput
): void => {
  const hasValidTransactionDetails =
    row?.accounting_transaction_details &&
    Array.isArray(row?.accounting_transaction_details) &&
    row?.accounting_transaction_details.length > 0;

  if (!hasValidTransactionDetails) {
    return;
  }

  const isValidNonEmptyString = (...strings: string[]) => {
    return strings.every((str) => isString(str) && str.length > 0);
  };

  const paths = {
    [TransactionParty.AGENT]: TransactionRate.AGENT,
    [TransactionParty.AGENCY]: TransactionRate.AGENCY,
    [TransactionParty.POLICY]: TransactionRate.POLICY,
  };

  const receivableTransactionDetails =
    row.accounting_transaction_details.filter(
      (transactionDetails) =>
        transactionDetails?.type === TransactionType.RECEIVABLE
    );

  for (const transactionDetails of receivableTransactionDetails) {
    const { party, contact, logs } = transactionDetails || {};
    const rateRaw = transactionDetails?.rate || 0;
    const rateFormatted = getRateFormatted(rateRaw, contact);
    const receivableFieldKey = paths[party];
    // TODO: temporary map for receivable field, refactor this logic to have only one approach
    const receivableFieldKeyMap = `${paths[party]}_map`;

    switch (party) {
      case TransactionParty.AGENCY:
      case TransactionParty.AGENT:
        set({
          row,
          key: receivableFieldKey,
          agentId: contact?.str_id || logs?.contactStrId,
          value: rateFormatted.agentAndRateLabel,
          transactionDetailStrId: transactionDetails.str_id,
        });
        setAgentIdToRateMap({
          row,
          agentId: contact?.str_id || logs?.contactStrId,
          rate: transactionDetails.rate,
          receivableFieldPath: receivableFieldKeyMap,
        });
        break;

      case TransactionParty.POLICY: {
        set({
          row,
          key: receivableFieldKey,
          value: rateFormatted.agentAndRateLabel,
          valueRaw: rateRaw,
          transactionDetailStrId: transactionDetails.str_id,
        });
        break;
      }

      default: {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        [TransactionParty.POLICY].forEach((transactionParty) => {
          set({
            row,
            key: paths[transactionParty],
            valueRaw: rateRaw,
            value: isValidNonEmptyString(
              transactionParty,
              rateFormatted.agentAndRateLabel
            )
              ? `${transactionParty} - ${rateFormatted.agentAndRateLabel}`
              : undefined,
            transactionDetailStrId: transactionDetails.str_id,
          });
        });
        break;
      }
    }
  }
};

export const hasTransactionRateValues = (obj) => {
  if (!obj || Object.keys(obj).length === 0) return false;
  return (
    obj?.[TransactionRate.AGENT]?.value != null &&
    obj?.[TransactionRate.AGENCY]?.value != null &&
    obj?.[TransactionRate.POLICY]?.value != null
  );
};
