import type { Dayjs } from 'dayjs';

import dayjs from '@/lib/dayjs';

const MappingDateFormat = {
  day: (date: Dayjs) => date.format('YYYY/MM/DD'),
  month: (date: Dayjs) => date.format('YYYY/MM'),
  week: (date: Dayjs) => date.startOf('week').format('YYYY/MM/DD'),
};

export const getDateBucket = (
  date: Date | string,
  period: string = 'month'
) => {
  if (date instanceof Date || typeof date === 'string') {
    const formattedDate = dayjs(date);
    if (!formattedDate.isValid()) return null;

    return MappingDateFormat[period]?.(formattedDate);
  }
  return null;
};
