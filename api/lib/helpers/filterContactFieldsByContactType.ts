import type { SavedReportSnapshotData } from 'common/globalTypes';

type ContactsFieldItem = string[];

interface AgentCommissionsFieldItem {
  [key: string]: number;
  // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ['total']: number;
}

/**
 * Filters contact fields by contact type. Sales rep type contacts are excluded if they don't own the report.
 *
 * @param {string} field - The field to filter.
 * @param {ContactsFieldItem | AgentCommissionsFieldItem} item - The item containing the field to filter.
 * @param {SavedReportSnapshotData} data - The data containing contact information and field options.
 * @returns {any} The filtered field.
 */
export const filterContactFieldsByContactType = (
  field: string,
  item: ContactsFieldItem | AgentCommissionsFieldItem,
  data: SavedReportSnapshotData
): ContactsFieldItem | AgentCommissionsFieldItem => {
  if (field === 'contacts' && Array.isArray(item[field])) {
    const fieldOptionsContacts = data.data.fieldOptions.contacts;

    return item[field].filter((contactStrId) => {
      const reportContact = fieldOptionsContacts?.find(
        (contact) => contact.id === contactStrId
      );

      if (reportContact) {
        const { type } = reportContact;
        if (typeof type === 'string') {
          return type !== 'Sales rep';
        } else if (Array.isArray(type)) {
          return !type.includes('Sales rep');
        } else if (type === null) {
          return true;
        }
      }

      return false;
    });
  }

  return item[field];
};
