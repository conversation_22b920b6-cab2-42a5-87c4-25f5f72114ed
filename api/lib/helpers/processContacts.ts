import Formatter from 'common/Formatter';
import { uniq, set, isNil } from 'lodash-es';
import { isArray } from 'class-validator';

import { chunkArray, limitConcurrency } from '@/lib/helpers';
import prisma from '@/lib/prisma';

type Contact = {
  id: number;
  str_id: string;
  first_name?: string;
  last_name?: string;
  type?: string;
};

type ContactMutable = {
  id?: number;
  strId?: string;
};

type ContactParsed = {
  name: string;
  type: string;
};

/**
 * Joins two lists of IDs and str_ids from the same index into an array of objects.
 * If one list is shorter, the missing values will be undefined.
 *
 * @example
 * ```
 * getListOfIdAndStrIdItemJoinedFromSameIndex({
 *   allContactStrIds: ['str1'],
 *   allContactIds: [1, 2]
 * });
 * // returns [{ strId: 'str1', id: 1 }, { strId: undefined, id: 2 }]
 * ```
 */
const getListOfIdAndStrIdItemJoinedFromSameIndex = (args: {
  allContactStrIds: string[];
  allContactIds: number[];
}): Array<ContactMutable> => {
  const contactStrIds = uniq(args.allContactStrIds);
  const contactIds = uniq(args.allContactIds);

  const highestLength =
    contactStrIds.length >= contactIds.length
      ? contactStrIds.length
      : contactIds.length;

  const joinedStrIdAndIdList: Array<ContactMutable> = [];

  for (let i = 0; i < highestLength; i++) {
    joinedStrIdAndIdList.push({
      strId: contactStrIds[i],
      id: contactIds[i],
    });
  }

  return joinedStrIdAndIdList;
};

// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
const extractContactIdsFromCompCalc = (item: NonNullable<any>) => {
  const allCompCalcContactIds: number[] = [
    ...Object.keys(item.comp_calc || {}),
    ...Object.keys(item.comp_calc_log || {}),
    ...Object.keys(item.comp_calc_status || {}),
  ]
    .map((id) => Number(id))
    .filter((id) => id > 0 && !isNil(id));

  return allCompCalcContactIds;
};

// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
const extractContactStrIdsFromAgentComissions = (item: NonNullable<any>) => {
  const contactStrIds: string[] = [];

  if (isArray(item.contacts)) {
    contactStrIds.push(...item.contacts);
  }

  const allAgentCommissionContactStrIds = [
    ...Object.keys(item.agent_commissions || {}),
    ...Object.keys(item.agent_commissions_log || {}),
    ...Object.keys(item.agent_payout_rate || {}),
    ...Object.keys(item.agent_commission_payout_rate || {}),
    ...Object.keys(item.agent_commissions_status2 || {}),
    ...Object.keys(item.agent_payout_rate_override || {}),
    ...Object.keys(item.report_data?.contacts_split || {}),
  ].filter((id) => id !== 'total');

  contactStrIds.push(...allAgentCommissionContactStrIds);

  return contactStrIds;
};

const setEmptyAgentCommissionContactByContactStrId = (item, contactStrIds) => {
  if (contactStrIds.length > 0) {
    item.agentCommissionContacts = {};

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    contactStrIds.forEach((contactId) => {
      item.agentCommissionContacts[contactId] = '';
    });
  }
};

const fetchContactsInChunks = async (args: {
  contacts: ContactMutable[];
  chunckSize: number;
}): Promise<Contact[]> => {
  const chunkContacts = chunkArray(args.contacts, args.chunckSize);

  if (chunkContacts.length === 0) {
    return [];
  }

  const result = await limitConcurrency<Contact>(
    async (contacts: ContactMutable[]) => {
      const strIds = contacts.map((contact) => contact.strId).filter(Boolean);
      const idList = contacts.map((contact) => contact.id).filter(Boolean);

      return await prisma.contacts.findMany({
        where: {
          OR: [{ str_id: { in: strIds } }, { id: { in: idList } }],
        },
        select: {
          id: true,
          str_id: true,
          first_name: true,
          last_name: true,
          type: true,
        },
        accountInject: false,
      });
    },
    chunkContacts,
    10,
    {
      onFail: (error) => {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.error('Failed to process item:', error);
      },
    }
  );

  return result?.flat();
};

const createContactMaps = (contacts: Contact[]) => {
  const contactMapByStrId = new Map<string, ContactParsed>();
  const contactMapById = new Map<number, ContactParsed>();

  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  contacts.forEach((contact) => {
    if (!contact) {
      return;
    }

    const fullName = Formatter.contact({
      first_name: contact.first_name,
      last_name: contact.last_name,
    });

    const value = {
      name: fullName,
      type: contact.type,
    };

    contactMapByStrId.set(contact.str_id, value);
    contactMapById.set(contact.id, value);
  });

  return { contactMapByStrId, contactMapById };
};

const setCompCalcContactMapById = (
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  item: NonNullable<any>,
  contactMapById: Map<number, ContactParsed>
) => {
  const contactsIds = extractContactIdsFromCompCalc(item);

  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  contactsIds.forEach((contactId) => {
    const contact = contactMapById.get(contactId);

    if (!contact) {
      return;
    }

    if (!item.compCalcContactMapById) {
      set(item, 'compCalcContactMapById', { [contactId]: contact });
    } else {
      item.compCalcContactMapById = {
        ...item.compCalcContactMapById,
        [contactId]: contact,
      };
    }
  });
};

const populateContactDetails = async (
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  item: NonNullable<any>,
  contactMapByStrId: Map<string, ContactParsed>
) => {
  const contactNames = [];
  const contactNamesWithType = [];
  const contactsSplitInfo: string[] = [];

  if (isArray(item.contacts)) {
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    item.contacts.forEach((contactId) => {
      const contactInfo = contactMapByStrId.get(contactId);

      if (contactInfo) {
        contactNames.push(contactInfo.name);
        contactNamesWithType.push({
          name: contactInfo.name,
          type: contactInfo.type,
        });
      }
    });
  }

  if (item.agentCommissionContacts) {
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    Object.keys(item.agentCommissionContacts).forEach((contactId) => {
      const contactInfo = contactMapByStrId.get(contactId);
      item.agentCommissionContacts[contactId] = contactInfo?.name ?? '';
    });
  }

  if (item.report_data?.contacts_split) {
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    Object.entries(item.report_data.contacts_split ?? {}).forEach(
      ([contactId, value]) => {
        const contactInfo = contactMapByStrId.get(contactId);
        contactsSplitInfo.push(
          `${
            // biome-ignore lint/style/useTemplate: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            contactInfo?.name ?? contactId + ' not found'
          }: ${value}%`
        );
      }
    );
  }

  set(item, 'contactsSplitInfo', contactsSplitInfo);
  set(item, 'contactNames', contactNames);
  set(item, 'contactNamesWithType', contactNamesWithType);
  set(item, 'documentName', item.document?.filename);
};

/**
 * Processes a list of statementData items to extract, transform, and populate contact-related information.
 *
 * This function performs the following operations:
 * - Extracts contact string IDs and contact IDs from the provided data items.
 * - Sets empty agent commission contacts based on extracted contact string IDs.
 * - Joins contact string IDs and contact IDs into a unified list.
 * - Fetches contact details in chunks for efficient processing.
 * - Creates maps for quick lookup of contacts by ID and string ID.
 * - Populates contact details and updates computation-related mappings in the data items.
 *
 */

// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
const processContacts = async (data: any[]) => {
  if (!isArray(data) || data.length === 0) {
    return;
  }

  const allContactStrIds = [];
  const allContactIds = [];

  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  data.forEach((item) => {
    if (!item) {
      return;
    }

    const contactStrIds = extractContactStrIdsFromAgentComissions(item);
    const contactIds = extractContactIdsFromCompCalc(item);

    setEmptyAgentCommissionContactByContactStrId(item, contactStrIds);

    allContactStrIds.push(...contactStrIds);
    allContactIds.push(...contactIds);
  });

  const allContacts = getListOfIdAndStrIdItemJoinedFromSameIndex({
    allContactStrIds,
    allContactIds,
  });

  const contacts = await fetchContactsInChunks({
    contacts: allContacts,
    chunckSize: 1000,
  });

  const { contactMapById, contactMapByStrId } = createContactMaps(contacts);

  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  data.forEach((item) => {
    if (!item) {
      return;
    }

    if (contactMapByStrId.size > 0) {
      populateContactDetails(item, contactMapByStrId);
    }

    if (contactMapById.size > 0) {
      setCompCalcContactMapById(item, contactMapById);
    }
  });
};

export { processContacts };
/**
 * Exported default functions here only for unit tests,
 * once they are private and not used outside this file
 */
export default {
  createContactMaps,
  extractContactIdsFromCompCalc,
  extractContactStrIdsFromAgentComissions,
  fetchContactsInChunks,
  getListOfIdAndStrIdItemJoinedFromSameIndex,
  populateContactDetails,
  setCompCalcContactMapById,
  setEmptyAgentCommissionContactByContactStrId,
};
