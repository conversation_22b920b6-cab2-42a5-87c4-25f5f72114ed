/**
 * Filters the keys of the `fieldOptions` object based on the keys of `dataObject` and `excludedKeys`.
 *
 * @param {Object} dataObject - The object to compare keys with.
 * @param {Object} fieldOptions - The object to filter keys from.
 * @param {string[]} excludedKeys - The keys to exclude from the filtering.
 * @returns {Object} The `fieldOptions` object with keys not present in `dataObject` and not in `excludedKeys` removed.
 */
export const filterFieldOptions = (
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  dataObject: Record<string, any>,
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  fieldOptions: Record<string, any>,
  excludedKeys: string[]
) => {
  let dataKeys = [];
  if (dataObject) {
    dataKeys = Object.keys(dataObject);
  }
  const fieldOptionsKeys = Object.keys(fieldOptions);

  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  fieldOptionsKeys.forEach((key) => {
    if (
      !excludedKeys.includes(key) &&
      !dataKeys.includes(key) &&
      key !== 'comp_calc_status'
    ) {
      delete fieldOptions[key];
    }
  });

  return fieldOptions;
};

/**
 * Checks if a field name is allowed to be selected in the database.
 * These fields are not allowed because they don't exist in any table or view.
 *
 * @param {string} fieldName - The name of the field to check.
 * @returns {boolean} `true` if the field is allowed, `false` otherwise.
 */
export const isAllowedFieldToSelectInDatabase = (
  fieldName: string
): boolean => {
  const notAllowedFields = [
    'receivable_value_agency_rate',
    'receivable_value_agent_rate',
    'receivable_value_override_rate',
  ];

  return !notAllowedFields.includes(fieldName);
};

/**
 * Removes fields from the `select` object that are not allowed to be selected in the database.
 *
 * @param {Record<string, unknown>} select - The object containing fields to be filtered.
 * @returns {void} Modifies the `select` object by removing disallowed fields.
 */
export const removeNotAllowedFieldsForDatabaseSelect = (
  select: Record<string, unknown>
) => {
  if (!select || typeof select !== 'object') {
    return;
  }

  for (const key of Object.keys(select)) {
    if (!isAllowedFieldToSelectInDatabase(key)) {
      delete select[key];
    }
  }
};
