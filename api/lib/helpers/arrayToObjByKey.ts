/**
 * Transforms an array of objects into an object where each key is the value of the specified key in each item,
 * and each value is an array of items that have that key. If multiple items have the same key, they will all be included in the array.
 * If the `ignoreFalsy` parameter is true, items with falsy values for the specified key will be ignored.
 *
 * @param {Array} arr - The array to transform.
 * @param {string} key - The key to use for the transformed object.
 * @param {boolean} [ignoreFalsy=false] - Whether to ignore items with falsy values for the specified key.
 * @returns {Object} The transformed object.
 */
export const arrayToObjByKey = <T>(
  arr: T[],
  key: string,
  ignoreFalsy: boolean = false
) =>
  arr.reduce((acc, item) => {
    if (!ignoreFalsy || (ignoreFalsy && item[key])) {
      acc[item[key]] = [...(acc[item[key]] ?? []), item];
    }
    return acc;
  }, {});
