import type {
  AIRequestOptions,
  AIResponseData,
  AIServiceConfig,
} from './types';

/**
 * Base interface for all AI services
 */
export interface AIService {
  /**
   * Initialize the AI service with configuration
   * @param config Service configuration
   */
  initialize(config: AIServiceConfig): Promise<void>;

  /**
   * Process a request to the AI service
   * @param options Request options
   * @returns AI response data
   */
  processRequest(options: AIRequestOptions): Promise<AIResponseData>;

  /**
   * Get the name of the AI service
   */
  getServiceName(): string;

  /**
   * Check if the service is properly initialized
   */
  isInitialized(): boolean;
}

/**
 * Abstract base class for AI services
 */
export abstract class BaseAIService implements AIService {
  protected config: AIServiceConfig;
  protected initialized = false;

  constructor(config?: AIServiceConfig) {
    if (config) {
      this.config = { ...config };
    }
  }

  abstract processRequest(options: AIRequestOptions): Promise<AIResponseData>;

  abstract getServiceName(): string;

  async initialize(config: AIServiceConfig): Promise<void> {
    this.config = { ...this.config, ...config };
    this.initialized = true;
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  protected validateConfig(): void {
    if (!this.initialized) {
      throw new Error(`${this.getServiceName()} service is not initialized`);
    }
  }
}
