import {
  HarmBlockThreshold,
  HarmCategory,
  VertexAI,
} from '@google-cloud/vertexai';

import { BaseAIService } from './base-ai-service';
import type {
  AIRequestOptions,
  AIResponseData,
  AIServiceConfig,
} from './types';

export class GeminiService extends BaseAIService {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private vertexAI: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private generativeModel: any;
  private defaultModel = 'gemini-2.0-flash-001';

  // biome-ignore lint/complexity/noUselessConstructor: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  constructor(config?: AIServiceConfig) {
    super(config);
  }

  async initialize(config: AIServiceConfig): Promise<void> {
    await super.initialize(config);

    if (!this.config.projectId || !this.config.location) {
      throw new Error('Gemini service requires projectId and location');
    }

    this.vertexAI = new VertexAI({
      project: this.config.projectId,
      location: this.config.location,
    });

    const model = this.config.model || this.defaultModel;

    this.generativeModel = this.vertexAI.preview.getGenerativeModel({
      model,
      generationConfig: {
        maxOutputTokens: this.config.maxTokens || 8192,
        temperature: this.config.temperature || 0.5,
        topP: this.config.topP || 0.95,
        responseMimeType: 'application/json',
      },
      safetySettings: [
        {
          category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_HARASSMENT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
      ],
    });

    this.initialized = true;
  }

  async processRequest(options: AIRequestOptions): Promise<AIResponseData> {
    this.validateConfig();

    const chat = this.generativeModel.startChat({});
    const reqs = [];

    if (options.inlineData) {
      reqs.push({
        inlineData: {
          data: options.inlineData.data,
          mimeType: options.inlineData.mimeType,
        },
      });
    }

    if (options.text) {
      reqs.push({ text: options.text });
    }

    const streamResult = await chat.sendMessageStream(reqs);
    const response = await streamResult.response;
    const content = response.candidates[0].content;

    return {
      content: JSON.stringify(content),
      raw: response,
    };
  }

  getServiceName(): string {
    return 'Gemini';
  }
}
