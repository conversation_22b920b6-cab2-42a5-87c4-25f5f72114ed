import * as Sentry from '@sentry/nextjs';

import { BaseAIService } from './base-ai-service';
import {
  DEFAULT_MAX_TOKEN,
  DEFAULT_TEMPERATURE,
  DEFAULT_TOP_P,
} from './constants';
import type {
  AIRequestOptions,
  AIResponseData,
  AIServiceConfig,
  InlineData,
} from './types';

export class ClaudeService extends BaseAIService {
  private apiKey: string;
  private defaultModel = 'claude-3-opus-20240229';
  private apiUrl = 'https://api.anthropic.com/v1/messages';

  // biome-ignore lint/complexity/noUselessConstructor: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  constructor(config?: AIServiceConfig) {
    super(config);
  }

  async initialize(config: AIServiceConfig): Promise<void> {
    await super.initialize(config);

    if (!this.config.apiKey) {
      throw new Error('Claude service requires an API key');
    }

    this.apiKey = this.config.apiKey;
    this.initialized = true;
  }

  async processRequest(options: AIRequestOptions): Promise<AIResponseData> {
    this.validateConfig();

    const model = options.model || this.config.model || this.defaultModel;

    const content = [];

    if (options.text) {
      content.push({
        type: 'text',
        text: options.text,
      });
    }

    // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (options.inlineData && options.inlineData.data) {
      content.push(this.getBase64AttachmentContent(options.inlineData));
    }

    const requestBody = {
      model,
      messages: [
        {
          role: 'user',
          content,
        },
      ],
      max_tokens:
        options.maxTokens || this.config.maxTokens || DEFAULT_MAX_TOKEN,
      temperature:
        options.temperature || this.config.temperature || DEFAULT_TEMPERATURE,
      top_p: options.topP || this.config.topP || DEFAULT_TOP_P,
    };

    try {
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.apiKey,
          'anthropic-version': '2023-06-01',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          `Claude API error: ${errorData.error?.message || response.statusText}`
        );
      }

      const data = await response.json();
      return {
        content: data.content[0].text,
        raw: data,
      };
    } catch (error) {
      Sentry.captureException(error);
      throw new Error(`Claude request failed: ${error.message}`);
    }
  }

  getServiceName(): string {
    return 'Claude';
  }

  getBase64AttachmentContent(inlineData: InlineData) {
    const mimeType: string = inlineData.mimeType || 'application/pdf';
    const type: 'image' | 'document' = mimeType.startsWith('image/')
      ? 'image'
      : 'document';

    return {
      type,
      source: {
        type: 'base64',
        media_type: mimeType,
        data: inlineData.data,
      },
    };
  }
}
