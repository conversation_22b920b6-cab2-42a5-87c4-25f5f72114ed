/**
 * Common types for AI service integrations
 */

import type { AIModel } from 'common/constants/prompt';

export interface AIRequestOptions {
  text: string;
  inlineData?: InlineData;
  model?: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
}

export interface InlineData {
  mimeType: string;
  data: string; // Base64 encoded data
  fileName: string;
}

export interface AIResponseData {
  content: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  raw?: any; // Raw response from the AI service
}

export interface AIServiceConfig {
  apiKey?: string;
  organizationId?: string;
  projectId?: string;
  location?: string;
  model?: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  apiUrl?: string;
}

export interface ExtractionResult {
  id?: string;
  str_id?: string;
  method: string;
  output: string;
  result: string;
  result_id?: string;
}

export interface DocumentData {
  document_id: string | number;
  account_id: string;
  uid: string;
  extract_str_id?: string;
  prompt_id?: number;
}

export interface ParserResult {
  result?: {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data: any;
    extraction: Partial<ExtractionResult>;
  };
  model: AIModel;
  error?: string;
}
