import { Prisma } from '@prisma/client';
import { PrismaClient } from '@prisma/client';
import { numberOrDefault } from 'common/helpers';
import { pagination } from 'prisma-extension-pagination';
import {
  type Operation,
  PrismaClientKnownRequestError,
} from '@prisma/client/runtime/library';
import pRetry from 'p-retry';
import { DEFAULT_BATCH_SIZE } from 'common/constants/database';

import {
  AppLoggerService,
  asyncLocalStorage,
} from '@/services/logger/appLogger';
import { removeNotAllowedFieldsForDatabaseSelect } from '@/lib/helpers';

// Types for pagination extension
type PaginationArgs = {
  skip?: number;
  take?: number;
  batchSize?: number;
  retries?: number;
};

type PaginationResult<T, A> = Prisma.Result<T, A, 'findMany'>;

// Type for models with pagination extension
type ModelWithPagination<T> = {
  findManyWithPagination<A extends PaginationArgs>(
    args?: A
  ): Promise<PaginationResult<T, A>>;
};

// PrismaClient is attached to the `global` object in development to prevent
// exhausting your database connection limit.
//
// Learn more:
// https://pris.ly/d/help/next-js-best-practices

// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
const globalForPrisma = global as unknown as { prisma: any };
const logger = new AppLoggerService({
  defaultMeta: { service: 'prisma' },
});

let isKeepAliveRunning = false;

export const OPTIONAL_ARGS_OPERATIONS = [
  'findMany',
  'findFirst',
  'count',
  'groupBy',
  'findFirstOrThrow',
  'aggregate',
  // 'findUnique',
] as const satisfies ReadonlyArray<Operation>;

export const UPDATE_CREATE_OPERATIONS = [
  'update',
  'updateMany',
  'upsert',
  'create',
  'createMany',
  'createManyAndReturn',
] as const satisfies ReadonlyArray<Operation>;

export interface IgnoreAccountInjectArgs {
  accountInject?: boolean;
}
export interface IgnoreAStateInjectArgs {
  stateInject?: boolean;
}
type OptionalArgsOperation = (typeof OPTIONAL_ARGS_OPERATIONS)[number];

type OptionalArgsFunction<O extends OptionalArgsOperation> = <T, A>(
  this: T,
  args?: Prisma.Exact<
    A,
    Prisma.Args<T, O> & IgnoreAccountInjectArgs & IgnoreAStateInjectArgs
  >
) => Promise<Prisma.Result<T, A, O>>;

export type ModelExtension = {
  [O2 in OptionalArgsOperation]: OptionalArgsFunction<O2>;
};

const modelExtension = Prisma.defineExtension({
  name: 'extend method type',
  model: {
    $allModels: {} as ModelExtension,
  },
});

const modelsWithState = new Map(
  Prisma.dmmf.datamodel.models
    .filter((m) => m.fields.some((f) => f.name === 'state'))
    .map((m) => [m.name, m])
);

const modelProxyFieldsMap = new Map(
  Prisma.dmmf.datamodel.models.map((m) => [m.name, m.fields])
);

const modelsWithoutUpdatedAt = Prisma.dmmf.datamodel.models
  .filter((m) => !m.fields.some((f) => f.name === 'updated_at'))
  .map((m) => m.name);

/**
 * Recursively checks if state is present in a nested query object
 */

// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
export const checkIfStateIsPresent = (obj: any): boolean => {
  // Base case: if obj is not an object or is null
  if (!obj || typeof obj !== 'object') {
    return false;
  }

  // Check if state exists directly in this object
  if ('state' in obj) {
    return true;
  }

  // Handle array operators (AND, OR)
  if (Array.isArray(obj)) {
    return obj.some((item) => checkIfStateIsPresent(item));
  }

  // Recursively check all nested objects
  for (const key in obj) {
    if (key === 'AND' || key === 'OR') {
      // Handle AND/OR arrays
      if (Array.isArray(obj[key])) {
        if (obj[key].some((item) => checkIfStateIsPresent(item))) {
          return true;
        }
      }
    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
      // Handle other nested objects (like report, etc)
      if (checkIfStateIsPresent(obj[key])) {
        return true;
      }
    }
  }

  return false;
};

const stateInjectionExtension = Prisma.defineExtension({
  name: 'state injection',
  query: {
    $allOperations: async ({ model, operation, args, query }) => {
      const hasStateField = modelsWithState.has(model);
      if (
        [...OPTIONAL_ARGS_OPERATIONS, 'paginate'].includes(operation) &&
        hasStateField &&
        args?.stateInject !== false &&
        !checkIfStateIsPresent(args) &&
        model !== 'history'
      ) {
        args.where = {
          ...args.where,
          state: 'active',
        };
      }

      if (args?.stateInject !== undefined) {
        delete args.stateInject;
      }

      return query(args);
    },
  },
});

// Automatically update updated_at timestamp for update operations if updated_at is not provided
const updateTimestampExtension = Prisma.defineExtension({
  name: 'update timestamp',
  query: {
    $allOperations: async ({ model, operation, args, query }) => {
      const UpdateOperations = ['update', 'updateMany', 'upsert'];
      // Only handle update operations
      if (
        !UpdateOperations.includes(operation) ||
        modelsWithoutUpdatedAt.includes(model)
      ) {
        return query(args);
      }

      if (operation === 'upsert') {
        if (!args.update?.updated_at) {
          args.update = {
            ...args.update,
            updated_at: new Date(),
          };
        }
      } else {
        // Handle regular update operations
        if (!args.data?.updated_at) {
          args.data = {
            ...args.data,
            updated_at: new Date(),
          };
        }
      }

      return query(args);
    },
  },
});

// Pagination extension for cursor-based pagination
const paginationExtension = Prisma.defineExtension({
  name: 'cursor pagination',
  model: {
    $allModels: {
      async findManyWithPagination<T, A extends PaginationArgs>(
        this: T,
        args?: Prisma.Exact<
          A,
          Prisma.Args<T, 'findMany'> &
            IgnoreAccountInjectArgs &
            IgnoreAStateInjectArgs
        >
      ): Promise<PaginationResult<T, A>> {
        const {
          skip,
          take,
          batchSize = DEFAULT_BATCH_SIZE,
          retries = 3,
          ...prismaArgs
        } = args || ({} as A);

        // Behavior:
        // - If skip and take are both undefined: fetch ALL data using cursor-based pagination with ID
        // - If skip or take is provided: use normal offset-based pagination with the given skip/take
        const shouldFetchAll = skip === undefined && take === undefined;

        if (shouldFetchAll) {
          const allData = [];
          let lastId: number | undefined;
          let hasNext = true;

          while (hasNext) {
            const items = await pRetry(
              async () => {
                const queryArgs = {
                  ...prismaArgs,
                  take: batchSize,
                  skip: lastId ? 1 : undefined,
                  cursor: lastId ? { id: lastId } : undefined,
                };

                // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                return await (this as any).findMany(queryArgs);
              },
              {
                retries,
              }
            );

            allData.push(...items);

            if (items.length < batchSize) {
              hasNext = false;
            } else {
              // Get the ID of the last item for the next iteration
              const lastItem = items[items.length - 1];
              lastId = lastItem.id;
            }
          }

          return allData as PaginationResult<T, A>;
        } else {
          // Use normal offset-based pagination with provided skip/take
          const result = await pRetry(
            async () => {
              const queryArgs = {
                ...prismaArgs,
                skip,
                take,
              };

              // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              return await (this as any).findMany(queryArgs);
            },
            {
              retries,
            }
          );

          return result;
        }
      },
    },
  },
});

const createPrismaClient = () => {
  const client = new PrismaClient({
    // log: [{ emit: 'event', level: 'query' }],
  });

  // Uncomment to log queries
  // client.$on('query', (e) => {
  //   logger.debug(
  //     `Query: ${e.query} - Duration: ${e.duration}ms - Params: ${e.params}`
  //   );
  // });

  const keepAlive = async () => {
    if (isKeepAliveRunning) return;

    setInterval(async () => {
      try {
        await client.$queryRaw`SELECT 1`;
      } catch {
        logger.warn('dead connection removed');
      }
    }, 1000);

    // Every 2 minutes, check if there are idle connections and keep them alive, max 30 connections
    setInterval(
      async () => {
        const metrics = await client.$metrics.json();
        const idleConnections = Math.min(
          metrics.gauges.find(
            (gauge) => gauge.key === 'prisma_pool_connections_idle'
          )?.value,
          10
        );
        if (idleConnections === 0) return;
        // Keep minimum 10 active connections
        await Promise.all(
          Array.from({
            length: Math.max(Math.max(idleConnections, 30), 10),
          }).map(async () => {
            // biome-ignore lint/suspicious/noEmptyBlockStatements: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            await client.$queryRaw`SELECT 1`.catch(() => {});
          })
        );
      },
      1000 * 60 * 2
    );
    isKeepAliveRunning = true;
  };

  return client
    .$extends(modelExtension)
    .$extends(stateInjectionExtension)
    .$extends(updateTimestampExtension)
    .$extends(paginationExtension)
    .$extends({
      name: 'reconciliation_data',
      result: {
        reconciliation_data: {
          amount_paid_commissionable_premium_amount_pct: {
            needs: {
              amount_paid: true,
              commissionable_premium_amount: true,
            },
            compute: (reconciliation_data) => {
              const res =
                +(
                  (
                    // biome-ignore format: biome likes the comments to be outside the parens, but then won't find the ignore comment because it's moved
                    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                    (reconciliation_data?.amount_paid as any)?.amount_paid
                      ?.amount_paid ?? 0
                  )
                ) /
                numberOrDefault(
                  +reconciliation_data.commissionable_premium_amount,
                  0
                );
              return Number.isNaN(res) ? null : res;
            },
          },
          amount_paid_premium_amount_pct: {
            needs: {
              amount_paid: true,
              premium_amount: true,
            },
            compute: (reconciliation_data) => {
              const res =
                +(
                  (
                    // biome-ignore format: biome likes the comments to be outside the parens, but then won't find the ignore comment because it's moved
                    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                    (reconciliation_data?.amount_paid as any)?.amount_paid
                      ?.amount_paid ?? 0
                  )
                ) / numberOrDefault(+reconciliation_data.premium_amount, 0);
              return Number.isNaN(res) ? null : res;
            },
          },
        },
      },
    })
    .$extends({
      query: {
        $allOperations: async ({ model, operation, args, query }) => {
          keepAlive();

          await client.$metrics.json();

          // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          let result;

          // Retry once on connection error
          try {
            /**
             * TODO: This function (removeNotAllowedFieldsForDatabaseSelect) filters out fields
             *       that cause Prisma select errors. Ideally, we should handle this with a more
             *       sophisticated validation layer between the request and Prisma.
             *       Marking as TODO for future improvement.
             */
            if (Array.isArray(args.orderBy)) {
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              args.orderBy.forEach((order) => {
                removeNotAllowedFieldsForDatabaseSelect(order);
              });
            }

            result = await query(args);
          } catch (error) {
            if (
              error instanceof PrismaClientKnownRequestError &&
              args.sql !== 'SELECT 1'
            ) {
              logger.error(
                `Error in prisma client ${model}.${operation} ${error}`,
                { error }
              );
            } else {
              throw error;
            }
            result = await query(args);

            logger.info(`Query ${model}.${operation} - Retried successfully`, {
              result,
            });
          }
          return result;
        },
      },
    })
    .$extends(pagination());
};

type Client = ReturnType<typeof createPrismaClient>;
const targetPrisma: Client = globalForPrisma.prisma || createPrismaClient();

// Export enhanced client type with pagination
export type PrismaClientWithPagination = Client & {
  [K in Prisma.ModelName]: Client[K] & ModelWithPagination<K>;
};

const PROXIED = Symbol('proxied');

const modelsWithoutAccountId = Prisma.dmmf.datamodel.models
  .filter((model) => !model.fields.some((f) => f.name === 'account_id'))
  .map((r) => r.name);

export const proxiedPrismaClient = new Proxy(targetPrisma, {
  get(target, property, receiver) {
    const origProperty = Reflect.get(target, property, receiver);
    // If the property is a model client (i.e. an object with findMany or findFirst methods)
    if (
      origProperty &&
      typeof origProperty === 'object' &&
      [...OPTIONAL_ARGS_OPERATIONS, ...UPDATE_CREATE_OPERATIONS].some(
        (operation) => typeof origProperty[operation] === 'function'
      )
    ) {
      return new Proxy(origProperty, {
        get(innerTarget, innerProp, innerReceiver) {
          const origMethod = Reflect.get(innerTarget, innerProp, innerReceiver);

          // Logic for handling account_id injection
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          if (OPTIONAL_ARGS_OPERATIONS.includes(innerProp as any)) {
            // We have some excpetion logic on accounts table
            const isAccounts = property === 'accounts';
            if (
              modelsWithoutAccountId.includes(property as string) &&
              !isAccounts
            ) {
              return origMethod;
            }
            if (
              // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              (OPTIONAL_ARGS_OPERATIONS.includes(innerProp as any) ||
                innerProp === 'paginate') &&
              typeof origMethod === 'function'
            ) {
              return function (...args) {
                // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                (this as any)[PROXIED] = true;

                try {
                  // Inject account_id into the query's where clause if not provided
                  const store = asyncLocalStorage.getStore();
                  const accountId = store?.account?.account_id;

                  if (
                    !accountId &&
                    args[0]?.accountInject !== false &&
                    (isAccounts
                      ? !args[0].where?.str_id
                      : !args[0].where?.account_id)
                  ) {
                    throw new Error(
                      `Account ID is required to query (${String(innerProp)}) ${String(property)}`
                    );
                  }

                  const newArgs = args[0] ? { ...args[0] } : {};
                  // biome-ignore lint/suspicious/noDoubleEquals: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  if (newArgs.accountInject != false) {
                    newArgs.where =
                      innerProp === 'findUnique'
                        ? {
                            ...(newArgs.where || {}),
                            [isAccounts ? 'str_id' : 'account_id']: accountId,
                          }
                        : {
                            AND: [
                              isAccounts
                                ? { str_id: accountId }
                                : { account_id: accountId },
                              ...(newArgs.where ? [newArgs.where] : []),
                            ],
                          };
                  }

                  delete newArgs.accountInject;
                  return origMethod.apply(innerTarget, [newArgs]);
                } finally {
                  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  (this as any)[PROXIED] = false;
                }
              };
            }
            return origMethod;
          }
          // Logic for handling created_* & updated_* fields injection
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          if (UPDATE_CREATE_OPERATIONS.includes(innerProp as any)) {
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            const trimProxyFields = (model: string, data: any) => {
              const fields = modelProxyFieldsMap.get(model);
              if (!fields) return data;
              // Remove data fields that are not in the fields array
              return Object.fromEntries(
                Object.entries(data).filter(([key]) =>
                  fields.some((f) => f.name === key)
                )
              );
            };
            const handleRelationField = (
              data: Record<string, unknown>,
              model: string
            ) => {
              const fields = modelProxyFieldsMap.get(model);
              if (
                !fields ||
                ['createMany', 'createManyAndReturn', 'updateMany'].includes(
                  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  innerProp as any
                )
              )
                return data;

              const targetFields = [
                'created_by',
                'created_proxied_by',
                'updated_by',
                'updated_proxied_by',
              ];

              const otherRelationalFields = fields
                .filter((f) => f.isReadOnly)
                .filter((r) => !targetFields.includes(r.name))
                .filter(
                  (r) =>
                    data[r.name] !== undefined &&
                    data[r.name] &&
                    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                    !Object.hasOwn(data[r.name] as any, 'connect')
                );

              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              targetFields.forEach((field) => {
                const fieldMetaData = fields.find((f) => f.name === field);
                if (fieldMetaData.isReadOnly) {
                  const relationField = fields.find(
                    (r) =>
                      r.relationFromFields?.length === 1 &&
                      r.relationFromFields[0] === fieldMetaData.name
                  );
                  if (
                    relationField &&
                    !data[relationField.name] &&
                    !otherRelationalFields.length
                  ) {
                    // Don't override if relation field is not empty
                    data[relationField.name] = data[field]
                      ? {
                          connect: {
                            [relationField.relationToFields[0]]: data[field],
                          },
                        }
                      : undefined;
                  }
                  delete data[field];
                }
              });
              return data;
            };
            return function (...args) {
              // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              (this as any)[PROXIED] = true;

              try {
                const store = asyncLocalStorage.getStore();
                const account = store?.account;
                const modelName = property as string;

                // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                if (['create'].includes(innerProp as any) && args[0].data) {
                  args[0].data = handleRelationField(
                    trimProxyFields(modelName, {
                      ...args[0].data,
                      created_by: args[0].data.created_by || account?.uid,
                      created_proxied_by:
                        args[0].data.created_proxied_by || account?.ouid,
                    }),
                    modelName
                  );
                }
                if (
                  ['createMany', 'createManyAndReturn'].includes(
                    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                    innerProp as any
                  )
                ) {
                  args[0].data = args[0].data.map((item) => {
                    item.created_by = item.created_by || account?.uid;
                    item.created_proxied_by =
                      item.created_proxied_by || account?.ouid;
                    item = handleRelationField(
                      trimProxyFields(modelName, item),
                      modelName
                    );
                    return item;
                  });
                }
                if (innerProp === 'upsert') {
                  args[0].create = handleRelationField(
                    trimProxyFields(modelName, {
                      ...args[0].create,
                      created_by: args[0].create.created_by || account?.uid,
                      created_proxied_by:
                        args[0].create.created_proxied_by || account?.ouid,
                      updated_by: args[0].create.updated_by || account?.uid,
                      updated_proxied_by:
                        args[0].create.updated_proxied_by || account?.ouid,
                    }),
                    modelName
                  );
                  args[0].update = handleRelationField(
                    trimProxyFields(modelName, {
                      ...args[0].update,
                      updated_by: args[0].update.updated_by || account?.uid,
                      updated_proxied_by:
                        args[0].update.updated_proxied_by || account?.ouid,
                    }),
                    modelName
                  );
                }
                if (
                  !['createMany', 'createManyAndReturn', 'upsert'].includes(
                    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                    innerProp as any
                  )
                ) {
                  args[0].data = handleRelationField(
                    trimProxyFields(modelName, {
                      ...args[0].data,
                      updated_by: args[0].data.updated_by || account?.uid,
                      updated_proxied_by:
                        args[0].data.updated_proxied_by || account?.ouid,
                    }),
                    modelName
                  );
                }

                return origMethod.apply(innerTarget, args);
              } finally {
                // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                (this as any)[PROXIED] = false;
              }
            };
          }
          return origMethod;
        },
      });
    }
    return origProperty;
  },
});

// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
export const prisma = proxiedPrismaClient as any;
if (process.env.NODE_ENV !== 'production')
  globalForPrisma.prisma = targetPrisma;

export default prisma;
export const prismaClient = prisma as PrismaClient & Client;
