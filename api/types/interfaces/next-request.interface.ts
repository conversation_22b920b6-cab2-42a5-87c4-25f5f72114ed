import type { NextApiRequest } from 'next';

import type { AppLoggerService } from '@/services/logger/appLogger';

export interface ExtNextApiRequest extends NextApiRequest {
  uid?: string;
  ouid?: string | null;
  account_id?: string;
  role_id?: string;
  timezone?: string;
  query: {
    [key: string]: string;
  };
  page?: number;
  limit?: number;
  logger?: AppLoggerService;
  fintaryAdmin: AuthResponse['fintaryAdmin'];
}
