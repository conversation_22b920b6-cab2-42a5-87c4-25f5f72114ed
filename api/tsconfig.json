{"compilerOptions": {"target": "es2015", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "incremental": true, "isolatedModules": true, "jsx": "preserve", "module": "ESNext", "moduleResolution": "node", "noEmit": true, "noFallthroughCasesInSwitch": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "strict": false, "baseUrl": ".", "paths": {"@/*": ["*"], "components": ["components/*"], "lib": ["lib/*"], "pages": ["pages/*"], "class-transformer": ["./node_modules/class-transformer"], "class-validator": ["./node_modules/class-validator"]}, "sourceMap": true}, "exclude": ["node_modules"], "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "../common"], "ts-node": {"compilerOptions": {"module": "commonjs"}}}