import { describe, it, expect } from 'vitest';

import { TransGlobalWorker } from './transGlobal';
import type { IPolicy } from './transGlobal';
import type { DBData } from './base';

describe('TransGlobalWorker', () => {
  const worker = new TransGlobalWorker();

  const createMockContact = (
    id: number,
    strId: string,
    firstName: string,
    lastName: string
  ) => ({
    str_id: strId,
    id,
    first_name: firstName,
    last_name: lastName,
    name: `${firstName} ${lastName}`,
    account_id: '1',
    uid: '1',
    state: 'active',
    created_at: new Date(),
    created_by: '1',
    created_proxied_by: '1',
    updated_at: new Date(),
    updated_by: '1',
    updated_proxied_by: '1',
    account_role_settings_id: null,
    agent_code: `A00${id}`,
    balance: null,
    bank_info: null,
    birthday: null,
    city: null,
    company_name: null,
    company: null,
    config: null,
    contact_group_id: null,
    country: null,
    customer_id: null,
    email: null,
    end_date: null,
    gender: null,
    geo_state: null,
    level: null,
    log: null,
    middle_name: null,
    nickname: null,
    notes: null,
    payable_status: null,
    phone_type: null,
    phone: null,
    phone2_type: null,
    phone2: null,
    reports: [],
    status: 'active',
    start_date: null,
    sync_id: `${id}`,
    sync_worker: null,
    title: null,
    type: null,
    user_str_id: null,
    zip: null,
  });

  const createMockDbData = (mocks: Partial<DBData>) => {
    const dbData: DBData = {
      contacts: new Map([]),
      contact_levels: new Map([]),
      companies: new Map([]),
      company_products: new Map([]),
      contact_hierarchy: new Map([]),
      report_data: new Map([]),
      comp_grid_levels: new Map([]),
      comp_grids: new Map([]),
      comp_grid_rates: new Map([]),
      comp_grid_products: new Map([]),
      comp_grid_criteria: new Map([]),
      agent_commission_schedule_profiles: new Map([]),
      agent_commission_schedule_profiles_sets: new Map([]),
      contacts_agent_commission_schedule_profiles_sets: new Map([]),
      date_ranges: new Map([]),
      documents: new Map([]),
      customers: new Map([]),
    };

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    Object.entries(mocks).forEach(([entity, data]) => {
      if (entity) {
        dbData[entity] = data;
      }
    });

    return dbData;
  };

  describe('policyTransformer', () => {
    it('should transform contacts correctly when contacts exist in dbData', () => {
      const mockPolicy: IPolicy = {
        id: 123,
        agent_id: 123,
        carrier_id: 456,
        product_id: 789,
        policy_holder: 'John Doe',
        policy_number: 'POL123',
        policy_date: '2024-01-01',
        policy_state: 'NY',
        signed_date: '2024-01-01',
        issue_age: 30,
        status: 'Active',
        mode: 'Monthly',
        target_premium: 1000,
        custom_field_referring_agent: '',
        created_timestamp: '2024-01-01',
        commit_timestamp: '2024-01-01',
      };

      const mockDbData: DBData = {
        contacts: new Map([
          ['123', createMockContact(1, 'contact_001', 'Agent', 'One')],
        ]),
        contact_levels: new Map([]),
        companies: new Map([]),
        company_products: new Map([]),
        contact_hierarchy: new Map([]),
        report_data: new Map([]),
        comp_grid_levels: new Map([]),
        comp_grids: new Map([]),
        comp_grid_rates: new Map([]),
        comp_grid_products: new Map([]),
        comp_grid_criteria: new Map([]),
        agent_commission_schedule_profiles: new Map([]),
        agent_commission_schedule_profiles_sets: new Map([]),
        contacts_agent_commission_schedule_profiles_sets: new Map([]),
        documents: new Map([]),
        customers: new Map([]),
        date_ranges: new Map([]),
      };

      const result = worker.policyTransform(mockPolicy, mockDbData);

      expect(result.contacts).toEqual(['contact_001']);
    });

    it('should handle missing contacts in dbData', () => {
      const mockPolicy: IPolicy = {
        id: 123,
        agent_id: 123,
        carrier_id: 456,
        product_id: 789,
        policy_holder: 'John Doe',
        policy_number: 'POL123',
        policy_date: '2024-01-01',
        policy_state: 'NY',
        signed_date: '2024-01-01',
        issue_age: 30,
        status: 'Active',
        mode: 'Monthly',
        target_premium: 1000,
        custom_field_referring_agent: '',
        created_timestamp: '2024-01-01',
        commit_timestamp: '2024-01-01',
      };

      const mockDbData: DBData = {
        contacts: new Map([]),
        contact_levels: new Map([]),
        companies: new Map([]),
        company_products: new Map([]),
        contact_hierarchy: new Map([]),
        report_data: new Map([]),
        comp_grid_levels: new Map([]),
        comp_grids: new Map([]),
        comp_grid_rates: new Map([]),
        comp_grid_products: new Map([]),
        comp_grid_criteria: new Map([]),
        agent_commission_schedule_profiles: new Map([]),
        agent_commission_schedule_profiles_sets: new Map([]),
        contacts_agent_commission_schedule_profiles_sets: new Map([]),
        documents: new Map([]),
        customers: new Map([]),
        date_ranges: new Map([]),
      };

      const result = worker.policyTransform(mockPolicy, mockDbData);

      expect(result.contacts).toEqual([]);
    });

    it('should append agent to contacts if the db policy data has agent', () => {
      const mockPolicy: IPolicy = {
        id: 123,
        agent_id: 123,
        carrier_id: 456,
        product_id: 789,
        policy_number: 'POL123',
        policy_holder: 'John Doe',
        policy_date: '2024-01-01',
        policy_state: 'NY',
        signed_date: '2024-01-01',
        issue_age: 30,
        status: 'Active',
        mode: 'Monthly',
        target_premium: 1000,
        custom_field_referring_agent: '',
        created_timestamp: '2024-01-01',
        commit_timestamp: '2024-01-01',
      };

      const mockDbData: DBData = createMockDbData({
        contacts: new Map([
          ['123', createMockContact(1, 'contact_001', 'Agent', 'One')],
        ]),
        report_data: new Map([
          ['123', { sync_id: 'sync_123', contacts: ['contact_002'] }],
        ]),
      });

      const result = worker.policyTransform(mockPolicy, mockDbData);

      expect(result.contacts).toEqual(['contact_002', 'contact_001']);
    });
    it('should only contain unique agent to contacts if agent is already in the contacts', () => {
      const mockPolicy: IPolicy = {
        id: 123,
        agent_id: 123,
        carrier_id: 456,
        product_id: 789,
        policy_number: 'POL123',
        policy_holder: 'John Doe',
        policy_date: '2024-01-01',
        policy_state: 'NY',
        signed_date: '2024-01-01',
        issue_age: 30,
        status: 'Active',
        mode: 'Monthly',
        target_premium: 1000,
        custom_field_referring_agent: '',
        created_timestamp: '2024-01-01',
        commit_timestamp: '2024-01-01',
      };

      const mockDbData: DBData = createMockDbData({
        contacts: new Map([
          ['123', createMockContact(1, 'contact_001', 'Agent', 'One')],
        ]),
        report_data: new Map([
          [
            '123',
            { sync_id: 'sync_123', contacts: ['contact_002', 'contact_001'] },
          ],
        ]),
      });

      const result = worker.policyTransform(mockPolicy, mockDbData);

      expect(result.contacts).toEqual(['contact_002', 'contact_001']);
    });
  });

  describe('policySplitTransformer', () => {
    it('should transform contacts and contacts_split correctly when contacts exist', () => {
      const mockPolicySplit = {
        policy_id: 'POL123',
        splits: {
          '123': '60',
          '456': '40',
        },
      };

      const mockDbData: DBData = createMockDbData({
        contacts: new Map([
          ['123', createMockContact(1, 'contact_001', 'Agent', 'One')],
          ['456', createMockContact(2, 'contact_002', 'Agent', 'Two')],
        ]),
        report_data: new Map([['POL123', { sync_id: 'sync_123' }]]),
      });

      const result = worker.policySplitTransform(mockPolicySplit, mockDbData);

      expect(result.contacts).toEqual(['contact_001', 'contact_002']);
      expect(result.contacts_split).toEqual({
        contact_001: '60',
        contact_002: '40',
      });
    });

    it('should handle missing contacts in splits', () => {
      const mockPolicySplit = {
        policy_id: 'POL123',
        splits: {
          '123': '60',
          '456': '40',
        },
      };

      const mockDbData: DBData = createMockDbData({
        contacts: new Map([
          ['123', createMockContact(1, 'contact_001', 'Agent', 'One')],
        ]),
        report_data: new Map([['POL123', { sync_id: 'sync_123' }]]),
      });

      const result = worker.policySplitTransform(mockPolicySplit, mockDbData);

      expect(result.contacts).toEqual(['contact_001']);
      expect(result.contacts_split).toEqual({
        contact_001: '60',
        null: '40',
      });
    });

    it('should append agent to contacts if the db policy data has agent', () => {
      const mockPolicySplit = {
        policy_id: 'POL123',
        splits: {
          '123': '60',
          '456': '40',
        },
      };

      const mockDbData: DBData = createMockDbData({
        contacts: new Map([
          ['123', createMockContact(1, 'contact_001', 'Agent', 'One')],
          ['456', createMockContact(2, 'contact_002', 'Agent', 'Two')],
        ]),
        report_data: new Map([
          [
            'POL123',
            {
              sync_id: 'sync_123',
              contacts: ['contact_001', 'contact_002', 'contact_003'],
            },
          ],
        ]),
      });

      const result = worker.policySplitTransform(mockPolicySplit, mockDbData);

      expect(result.contacts).toEqual([
        'contact_001',
        'contact_002',
        'contact_003',
      ]);
      expect(result.contacts_split).toEqual({
        contact_001: '60',
        contact_002: '40',
      });
    });

    it('should handle empty splits', () => {
      const mockPolicySplit = {
        policy_id: 'POL123',
        splits: {},
      };

      const mockDbData: DBData = createMockDbData({
        contacts: new Map([]),
        report_data: new Map([['POL123', { sync_id: 'sync_123' }]]),
      });

      const result = worker.policySplitTransform(mockPolicySplit, mockDbData);

      expect(result.contacts).toEqual(null);
      expect(result.contacts_split).toEqual({});
    });
  });
});
