import {
  type Prisma,
  type report_data,
  type statement_data,
  virtual_type,
} from '@prisma/client';
import type { PrismaClient } from '@prisma/client/extension';
import { inject, injectable } from 'inversify';
import type {
  UngroupingDTO,
  BulkUngroupingDTO,
} from 'common/dto/data_processing/grouping';
import { nanoid } from 'nanoid';
import type { GroupingRule } from 'common/dto/data_processing/grouping-rules';
import { GroupingCalculationMethod } from 'common/dto/data_processing/interfaces';
import { isNill } from 'common/helpers';
import { AccountIds } from 'common/constants';

import { GroupingCalculator } from './grouping-calculator';
import { prismaTransactionHandler } from '@/lib/prismaUtils';
import { prismaClient } from '@/lib/prisma';
import dayjs from '@/lib/dayjs';
import { type ExtAccountInfo, DataStates, Tables } from '@/types';
import { applyTransformers } from '@/lib/reconciliation';
import { matchesFilter } from '@/lib/matcher';
import { AppLoggerService } from '@/services/logger/appLogger';
import { limitConcurrency } from '@/lib/helpers';

@injectable()
export class GroupDedupeService {
  @inject(GroupingCalculator) groupingCalculator: GroupingCalculator;
  logger: AppLoggerService = new AppLoggerService({
    defaultMeta: {
      service: 'group-dedupe',
    },
  });

  async reAssignPolicySyncId(
    data: {
      targetId: number;
      syncId: string;
      accountId: string;
    },
    prisma: PrismaClient | Prisma.TransactionClient = prismaClient
  ) {
    const { syncId, accountId, targetId } = data;
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.log(`Re-assign sync_id ${syncId} to policy (${targetId})`);
    return await prismaTransactionHandler(prisma, async (client) => {
      await client.report_data.updateMany({
        where: { sync_id: syncId, account_id: accountId },
        data: { sync_id: null },
      });
      await client.report_data.update({
        where: { id: targetId },
        data: { sync_id: syncId },
      });
    });
  }

  async manualDedupe(
    data: statement_data[],
    masterId: number,
    options?: {
      useVirtualRecords?: boolean;
      accountId?: string;
      advancedRate?: number;
      calculationMethod?: GroupingCalculationMethod;
      advancedRatePercent?: number;
    }
  ) {
    if (!options?.useVirtualRecords) {
      const masterRecord = data.find((r) => r.id === masterId);
      if (!masterRecord) {
        throw new Error(`Master record with id ${masterId} not found in data`);
      }
      const groupedData = data.filter((r) => r.id !== masterId);
      const groupedDataIds = groupedData.map((r) => r.id);

      return {
        ids: groupedDataIds,
        data: groupedData,
        relations: new Map([[masterId, groupedData]]),
        replaced: data,
        virtualRecords: new Map(),
      };
    }

    // For non-virtual master record, create a virtual record
    const virtualRecord = await this.createOrUpdateVirtualRecord(
      data,
      Tables.COMMISSION_DATA,
      options?.accountId,
      options?.calculationMethod,
      options?.advancedRatePercent
    );

    // All records are grouped under the new virtual record
    const groupedData = data.filter((r) => r.id !== virtualRecord.id);
    const groupedDataIds = groupedData.map((r) => r.id);

    return {
      ids: groupedDataIds,
      data: groupedData,
      relations: new Map([[virtualRecord.id, groupedData]]),
      replaced: data,
      virtualRecords: new Map([[virtualRecord.id, virtualRecord]]),
    };
  }

  getReAssignTasks(replaceData: report_data[][], accountId: string) {
    return replaceData
      .map((data) => {
        if (data[0].sync_id) {
          return null;
        }
        const syncedPolicy = data.find(
          (r) => r.sync_id && this.isSamePolicy(data[0], r)
        );
        return syncedPolicy
          ? {
              syncId: syncedPolicy.sync_id,
              targetId: data[0].id,
              accountId: accountId,
            }
          : null;
      })
      .filter((r) => r);
  }

  // TODO: to match policies, should use the same grouping logic in findDupes,
  // but currently, it's only used to sort policies after grouping, it's ok to use policy_id as the only key
  isSamePolicy(a: report_data, b: report_data) {
    return (
      // Convert null to undefined, otherwise dayjs will return false
      a.effective_date || b.effective_date
        ? dayjs(a.effective_date || undefined).isSame(
            dayjs(b.effective_date || undefined)
          ) && a.policy_id === b.policy_id
        : a.policy_id === b.policy_id
    );
  }

  prioritizePolicy(data: report_data[]) {
    if (data[0].sync_id) {
      return data;
    }
    const idx = data.findIndex(
      (r) => r.sync_id && this.isSamePolicy(data[0], r)
    );
    const tmp = data[0];
    // biome-ignore lint/suspicious/noDoubleEquals: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (idx != -1) {
      data[0] = data[idx];
      data[idx] = tmp;
    }
    return data;
  }

  async unGroup(account: ExtAccountInfo, body: UngroupingDTO) {
    // Get all statements to ungroup
    const statements = await prismaClient.statement_data.findMany({
      where: {
        id: { in: body.statementIds },
        state: { in: [DataStates.ACTIVE, DataStates.GROUPED] },
      },
      include: {
        children_data: {
          select: {
            id: true,
          },
        },
      },
    });

    // Collect parent ids from statements that are children
    const childRecords = statements.filter(
      (record) =>
        record.parent_id !== null && record.state === DataStates.GROUPED
    );

    // Collect parent records from statements that have children
    const parentRecords = statements.filter(
      (record) => record.children_data?.length > 0
    );

    // Process child records - set parent_id to null and state to active
    if (childRecords.length) {
      await this.ungroupChildStatements(
        account.account_id,
        childRecords.map((r) => r.id)
      );
    }

    // Process parent records - remove all children
    if (parentRecords.length) {
      await this.ungroupStatementsByParentIds(
        account.account_id,
        parentRecords.map((r) => r.id)
      );
    }

    return true;
  }

  async bulkUngroup(account: ExtAccountInfo, body: BulkUngroupingDTO) {
    this.logger.info('Starting bulk ungroup operation', {
      accountId: account.account_id,
      startDate: body.startDate,
      endDate: body.endDate,
      carriers: body.carriers,
    });

    // Build the where clause for finding grouped statements
    const whereClause: Prisma.statement_dataWhereInput = {
      account_id: account.account_id,
      state: DataStates.ACTIVE,
      children_data: {
        some: {},
      },
    };

    // Add date filtering if provided
    if (body.startDate || body.endDate) {
      whereClause.processing_date = {
        gte: body.startDate,
        lte: body.endDate,
      };
    }

    // Add carrier filtering if provided
    if (body.carriers && body.carriers.length > 0) {
      whereClause.carrier_name = {
        in: body.carriers,
      };
    }

    // Find all statements that match the criteria
    const statements = await prismaClient.statement_data.findMany({
      where: whereClause,
      include: {
        children_data: {
          select: {
            id: true,
          },
        },
      },
    });

    this.logger.info(`Found ${statements.length} statements to ungroup`);

    const ungroupedResults = await limitConcurrency(
      async (statement: statement_data) => {
        return await this.unGroup(account, {
          statementIds: [statement.id],
        });
      },
      statements,
      10
    );

    const successCount = ungroupedResults.filter((r) => r).length;
    this.logger.info(
      `Bulk ungroup completed. Total ungrouped: ${successCount}`
    );

    const { count } = await this.deleteOrphanVirtualRecords(
      Tables.COMMISSION_DATA,
      account.account_id
    );
    this.logger.info(`deletedVirtualRecords: deleted ${count} virtual records`);

    return {
      success: true,
      ungroupedCount: successCount,
    };
  }

  private async ungroupChildStatements(accountId: string, ids: number[]) {
    if (!ids?.length) {
      return;
    }
    return await prismaClient.statement_data.updateMany({
      where: {
        account_id: accountId,
        id: { in: ids },
      },
      data: {
        parent_id: null,
        state: DataStates.ACTIVE,
      },
    });
  }

  private async ungroupStatementsByParentIds(
    accountId: string,
    parentIds: number[]
  ) {
    if (!parentIds?.length) {
      return;
    }
    return await prismaClient.statement_data.updateMany({
      where: {
        account_id: accountId,
        parent_id: { in: parentIds },
      },
      data: {
        parent_id: null,
        state: DataStates.ACTIVE,
      },
    });
  }

  public findDupesWithKeyCondition(
    data: (statement_data | report_data)[],
    keyCondition: Array<{
      field?: string;
      transformers?: string[];
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      params?: Record<string, any>;
    }>,
    options?: {
      prioritySortFn?: (
        a: statement_data | report_data,
        b: statement_data | report_data
      ) => number;
      table?: Tables;
      periodDateField?: string;
    }
  ) {
    const accountId = data[0].account_id;
    const dupedData: { [key: string]: (statement_data | report_data)[] } = {};
    const priorityField = 'created_at';
    const sortFn =
      options?.prioritySortFn ??
      ((a, b) => b[priorityField].getTime() - a[priorityField].getTime());

    // Process each datum to create record keys based on key_condition
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data.forEach((datum) => {
      // Create base key from key_condition
      const baseKey = keyCondition
        .map((kc) => {
          let value = datum[kc.field];

          // Handle unique_empty transformer first
          if (
            kc.transformers?.includes('unique_empty') &&
            (value == null || value === '')
          ) {
            return `rnd-${nanoid()}`;
          }

          // Use applyTransformers from reconciliation.js for other transformers
          const otherTransformers =
            kc.transformers?.filter((t) => t !== 'unique_empty') || [];
          if (otherTransformers.length > 0) {
            // Import applyTransformers dynamically to avoid circular dependency issues
            value = applyTransformers(value, otherTransformers, kc.params);
          }

          return value;
        })
        .join('::');

      // If periodDateField is specified, append formatted period_date to the key
      const recordKey = options?.periodDateField
        ? `${baseKey}::${
            datum[options.periodDateField]
              ? dayjs(datum[options.periodDateField]).format('YYYY-MM-DD')
              : ''
          }`
        : baseKey;

      dupedData[recordKey] = [...(dupedData[recordKey] ?? []), datum];
    });

    // Filter and process duplicates
    let groupedItems = Object.entries(dupedData).filter(([, v]) => {
      if (accountId === AccountIds.ALLIED) {
        if (v.length === 1 && v[0].virtual_type === virtual_type.grouped) {
          return false;
        }
        return v.length >= 1;
      } else {
        return v.length > 1;
      }
    });
    groupedItems = groupedItems.map(([k, v]) => {
      v.sort(sortFn);
      return [k, v];
    });

    const toBeRemovedIds: number[] = [];
    const toBeRemovedData: (statement_data | report_data)[] = [];
    const relations = new Map<number, (statement_data | report_data)[]>();

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    groupedItems.forEach(([, v]) => {
      v.forEach((e, i) => {
        if (i === 0) {
          // First item is the master
          relations.set(e.id, v.slice(1));
        } else {
          // Rest are duplicates to be removed
          toBeRemovedIds.push(e.id);
          toBeRemovedData.push(e);
        }
      });
    });

    return {
      ids: toBeRemovedIds,
      data: toBeRemovedData,
      relations,
      groupedItems: groupedItems,
    };
  }

  mergeJson(
    records: (statement_data | report_data)[],
    field: string
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Record<string, any> | null {
    const withValue = records.filter((r) => r[field]);
    if (withValue.length === 0) {
      return null;
    }
    return (
      withValue
        .map((r) => r[field])
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        .reduce((acc: Record<string, any>, curr: any) => {
          // biome-ignore lint/performance/noAccumulatingSpread: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          return { ...acc, ...curr };
        }, {})
    );
  }

  mergeContacts(records: (statement_data | report_data)[]): string[] {
    return [...new Set(records.flatMap((r) => r.contacts).filter(Boolean))];
  }

  async createOrUpdateVirtualRecord(
    records: (statement_data | report_data)[],
    table: Tables,
    accountId: string,
    method?: GroupingCalculationMethod,
    advancedRatePercent?: number,
    prisma: PrismaClient | Prisma.TransactionClient = prismaClient
  ) {
    // Find if any of the records is a virtual record
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const virtualRecord = records.find((r) => (r as any).is_virtual === true);

    // Get all fields from the records
    const fields = Object.keys(records[0]).filter(
      (key) =>
        ![
          'agent_commissions_log',
          'agent_commissions_status',
          'agent_commissions_status2',
          'agent_commissions_v2',
          'agent_commission_payout_rate',
          'agent_payout_rate',
          'allocated_amount',
          'children_data',
          'commission_rate',
          'commission_rate_percent',
          'master_id',
          'new_commission_rate',
          'contacts',
          'created_at',
          'created_by',
          'created_proxied_by',
          'flags',
          'id',
          'import_id',
          'is_virtual',
          'reconciliation_method',
          'reconciliation_stats',
          'reconciliation_status',
          'remain_amount',
          'report_data_id',
          'parent_id',
          'virtual_type',
          'processing_status',
          'split_percentage',
          'state',
          'str_id',
          'sync_id',
          'updated_at',
          'updated_by',
        ].includes(key)
    );

    // Create a new record with the latest values for each field
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let virtualData: any = {
      account_id: accountId,
      state: DataStates.ACTIVE,
      is_virtual: true,
      virtual_type: virtual_type.grouped,
    };

    // For each field, find the latest non-null value
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    fields.forEach((field) => {
      // Sort records by updated_at to get the latest value
      const sortedRecords = [...records].sort(
        (a, b) =>
          (b.effective_date?.getTime() ?? b.created_at.getTime()) -
          (a.effective_date?.getTime() ?? a.created_at.getTime())
      );

      // Find the first non-null value
      const latestValue = sortedRecords
        .filter((r) => !r.is_virtual)
        .find((r) => r[field] != null)?.[field];
      if (latestValue !== undefined) {
        virtualData[field] = latestValue;
      }
    });

    virtualData.contacts = this.mergeContacts(records);

    if (table === Tables.COMMISSION_DATA) {
      virtualData.agent_commission_payout_rate = this.mergeJson(
        records,
        'agent_commission_payout_rate'
      );
      virtualData.agent_payout_rate = this.mergeJson(
        records,
        'agent_payout_rate'
      );
    }

    const documentIds = new Set(
      records
        .filter((r) => !r.is_virtual && r.document_id)
        .map((r) => r.document_id)
    );
    if (documentIds.size !== 1) {
      virtualData.document_id = null;
    }

    if (table === Tables.COMMISSION_DATA) {
      const {
        commission_amount,
        premium_amount,
        commission_rate,
        commission_rate_percent,
        new_commission_rate,
      } = this.groupingCalculator.calculate(
        method ?? GroupingCalculationMethod.SUM_BY_COMMISSION,
        records.filter((r) => !r.is_virtual) as statement_data[],
        advancedRatePercent
      );
      virtualData = {
        ...virtualData,
        commission_amount,
        premium_amount,
        commission_rate,
        commission_rate_percent,
        new_commission_rate,
      };

      // For BA MOO grouping, set compensation_type appropriately
      if (method === GroupingCalculationMethod.BA_MOO_COMMISSION_CALCULATION) {
        virtualData.compensation_type = this.getBaMooGroupedCompensationType();
      }
    }

    if (virtualRecord) {
      // Update existing virtual record
      return await prisma[table].update({
        where: { id: virtualRecord.id },
        data: virtualData,
      });
    } else {
      // Create new virtual record
      return await prisma[table].create({
        data: virtualData,
      });
    }
  }

  async processVirtualRecords(
    groupedItems: [string, (statement_data | report_data)[]][],
    options: {
      table?: Tables;
      accountId?: string;
      calculationMethod?: GroupingCalculationMethod;
      advancedRatePercent?: number;
    }
  ) {
    const toBeRemovedIds: number[] = [];
    const toBeRemovedData: (statement_data | report_data)[] = [];
    const relations = new Map<number, (statement_data | report_data)[]>();
    const virtualRecords = new Map<number, statement_data | report_data>();

    for (const [_key, records] of groupedItems) {
      if (
        options.accountId === AccountIds.ALLIED
          ? records.length < 1
          : records.length <= 1
      )
        continue;

      // Create or update virtual record for this group
      const virtualRecord = await this.createOrUpdateVirtualRecord(
        records,
        options.table,
        options.accountId,
        options.calculationMethod,
        options.advancedRatePercent
      );

      // Store virtual record for future reference
      virtualRecords.set(virtualRecord.id, virtualRecord);

      // Update relations - all records in the group will be linked to the virtual record
      relations.set(
        virtualRecord.id,
        records.filter((r) => !r.is_virtual)
      );

      // Add all records except the virtual one to be removed
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      records.forEach((record) => {
        if (record.id !== virtualRecord.id) {
          toBeRemovedIds.push(record.id);
          toBeRemovedData.push(record);
        }
      });
    }

    return {
      ids: toBeRemovedIds,
      data: toBeRemovedData,
      relations,
      virtualRecords,
    };
  }

  async deleteOrphanVirtualRecords(table: Tables, accountId: string) {
    return await (prismaClient[table] as PrismaClient).updateMany({
      where: {
        is_virtual: true,
        virtual_type: virtual_type.grouped,
        parent_id: null,
        account_id: accountId,
        state: DataStates.ACTIVE,
        [table === Tables.COMMISSION_DATA
          ? 'children_data'
          : 'children_report_data']: { none: {} },
      },
      data: {
        state: DataStates.DELETED,
      },
    });
  }

  async findDupesWithRules(
    data: (statement_data | report_data)[],
    rules: GroupingRule[],
    options?: {
      prioritySortFn?: (
        a: statement_data | report_data,
        b: statement_data | report_data
      ) => number;
      table?: Tables;
      useVirtualRecords?: boolean;
      accountId?: string;
    }
  ) {
    let remainingData = [...data];
    const allRelations: Map<number, (statement_data | report_data)[]> =
      new Map();
    const allToBeRemovedIds: number[] = [];
    const allToBeRemovedData: (statement_data | report_data)[] = [];
    const processedIds = new Set<number>();
    const virtualRecords = new Map<number, statement_data | report_data>();

    // Iterate through each rule
    for (const rule of rules) {
      this.logger.info(`Processing rule: ${rule.name}`, {
        rule,
      });
      // Filter data based on rule's filter conditions, excluding already processed records
      const filteredData = remainingData.filter((datum) => {
        if (processedIds.has(datum.id)) return false;
        if (matchesFilter(datum, rule.filter)) {
          processedIds.add(datum.id);
          return true;
        }
        return false;
      });

      if (filteredData.length === 0) continue;

      // Use findDupesWithKeyCondition to find duplicates, passing period_date_field if specified
      const dupedResult = this.findDupesWithKeyCondition(
        filteredData,
        rule.key_condition,
        {
          ...options,
          periodDateField: rule.config?.period_date_field,
        }
      );

      const useVirtualRecords = options?.useVirtualRecords ?? true;
      if (!useVirtualRecords) {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        dupedResult.groupedItems.forEach(([_key, records]) => {
          records.forEach((record, idx) => {
            if (idx !== 0) {
              allToBeRemovedIds.push(record.id);
              allToBeRemovedData.push(record);
            } else {
              allRelations.set(record.id, records.slice(1));
            }
          });
        });
      } else {
        // Process virtual records using the new shared function
        const result = await this.processVirtualRecords(
          dupedResult.groupedItems,
          {
            table: options?.table,
            accountId: options?.accountId,
            calculationMethod: rule.config?.calculation_method,
            advancedRatePercent: rule.config?.advanced_rate_percent,
          }
        );

        // Merge results into the existing collections
        allToBeRemovedIds.push(...result.ids);
        allToBeRemovedData.push(...result.data);
        result.relations.forEach((value, key) => allRelations.set(key, value));
        result.virtualRecords.forEach((value, key) =>
          virtualRecords.set(key, value)
        );
      }

      // Remove processed data
      remainingData = remainingData.filter(
        (datum) => !processedIds.has(datum.id)
      );
    }

    return {
      ids: allToBeRemovedIds,
      data: allToBeRemovedData,
      relations: allRelations,
      remainingData,
      virtualRecords,
    };
  }

  // Legacy default function for backward compatibility
  async findDupes(
    data: (statement_data | report_data)[],
    config: {
      priority_field?: string;
      unique_empty?: string[];
      fields?: string[];
      diff_fields?: string[];
    },
    options?: {
      prioritySortFn?: (
        a: statement_data | report_data,
        b: statement_data | report_data
      ) => number;
      table?: Tables;
      useVirtualRecords?: boolean;
      accountId?: string;
    }
  ) {
    const dupedData: { [key: string]: (statement_data | report_data)[] } = {};
    const priorityField = config?.priority_field ?? 'created_at';
    let sortFn =
      options?.prioritySortFn ??
      ((a, b) => b[priorityField] - a[priorityField]);
    if (!options?.prioritySortFn && priorityField === 'compensation_type') {
      sortFn = (a, b) => {
        const isACommission =
          typeof a[priorityField] === 'string' &&
          ['commissions', 'commission', 'override', 'overrides'].includes(
            a[priorityField].toLowerCase()
          );
        const isBCommission =
          typeof b[priorityField] === 'string' &&
          ['commissions', 'commission', 'override', 'overrides'].includes(
            b[priorityField].toLowerCase()
          );
        if (isACommission && !isBCommission) return -1;
        if (isBCommission && !isACommission) return 1;
        return 0;
      };
    }

    const uniqueEmptyKeys = config?.unique_empty ?? [];
    const fieldKeys =
      Array.isArray(config?.fields) && config.fields.length > 0
        ? config.fields
        : ['id'];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data.forEach((datum) => {
      const recordKey = fieldKeys
        .map((field) =>
          isNill(datum?.[field]) && uniqueEmptyKeys.includes(field)
            ? `rnd-${nanoid()}`
            : typeof datum?.[field] === 'string'
              ? datum[field].trim()
              : datum[field]
        )
        .join('::');
      dupedData[recordKey] = [...(dupedData[recordKey] ?? []), datum];
    });

    let replaced = Object.entries(dupedData).filter(
      // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      ([k, v]) => (v as (statement_data | report_data)[]).length > 1
    );
    // TODO: Not the most effective way of doing this...
    replaced = replaced.map(([k, v]) => {
      v.sort(sortFn);
      const diffFields = config.diff_fields ?? [];
      let newVals = v;
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      diffFields.forEach((field) => {
        const seenVals = {};
        const newNewVals = [];
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        newVals.forEach((e) => {
          if (seenVals[e[field]] !== true) {
            newNewVals.push(e);
          }
          seenVals[e[field]] = true;
        });
        newVals = newNewVals;
      });
      return [k, newVals];
    });
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    replaced = replaced.filter(([k, v]) => v.length > 1);
    if (options?.table === Tables.POLICY_DATA) {
      replaced = replaced.map((r) => [
        r[0],
        this.prioritizePolicy(r[1] as report_data[]),
      ]);
    }

    // If useVirtualRecords is enabled, process with virtual records
    if (options?.useVirtualRecords && options.table !== Tables.POLICY_DATA) {
      const result = await this.processVirtualRecords(
        replaced as [string, (statement_data | report_data)[]][],
        {
          table: options?.table,
          accountId: options?.accountId,
          calculationMethod: GroupingCalculationMethod.EMPTY_PREMIUM_AMOUNT,
          advancedRate: 1,
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        } as any
      );

      return {
        ...result,
        replaced,
      };
    }

    // Original logic for backward compatibility
    const toBeRemovedIds = [];
    const toBeRemovedData = [];
    const relations = new Map();

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    replaced.forEach(([k, v]) => {
      // Handle boolean and product_name
      v.forEach((e, i) => {
        if (i === 0) {
          relations.set(
            e.id,
            v
              .map((e) => ({
                id: e.id,
                contacts: e.contacts,
                // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                contacts_split: (e as any).contacts_split,
                // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                statement_data: (e as any).statement_data,
              }))
              .slice(1)
          );
        } else {
          // Newest one is 1st
          toBeRemovedIds.push(e.id);
          toBeRemovedData.push(e);
        }
      });
    });

    return {
      ids: toBeRemovedIds,
      data: toBeRemovedData,
      relations,
      replaced,
      virtualRecords: new Map(),
    };
  }

  // Helper method to determine compensation type for BA MOO grouped lines
  getBaMooGroupedCompensationType(): string {
    return 'Override';
  }
}
