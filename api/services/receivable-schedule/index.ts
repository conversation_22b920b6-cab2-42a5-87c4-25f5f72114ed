import type { ReceivableDTO } from 'common/dto/receivable/dto';
import { inject, injectable } from 'inversify';
import { BigNumber } from 'bignumber.js';
import type { contacts, report_data } from '@prisma/client';
import Formatter from 'common/Formatter';

import { prismaClient } from '@/lib/prisma';
import { BusinessException } from '@/lib/exceptionHandler';
import { CompanyService } from '@/services/company';
import { DataStates } from '@/types/enum';
import { ProductService } from '@/services/products';
import { checkIssueAge, checkTextRule } from '@/lib/commissions';
import { limitConcurrency } from '@/lib/helpers/limitConcurrency';
import type { ExtAccountInfo } from '@/types';
import { DataProcessingTypes } from 'common/dataProcessingType';
import { DataProcessingStatuses } from '@/types';
import { DataProcessService } from '@/services/data_processing';
import type { ReceivableCalcRequest } from 'common/dto/data_processing/receivable_calc';
import { nanoid } from 'nanoid';
import type { data_processing } from '@prisma/client';
import type { ReportData } from '../report/types';

interface ReceivableResult {
  party: string;
  amount?: BigNumber;
  rate: BigNumber;
  report_id: number;
  account_id: string;
  contact_id?: number;
  logs: ReceivableResultLog;
}

interface ReceivableResultLog {
  expectedRate: BigNumber;
  expectedAmount?: BigNumber;
  actualRate?: BigNumber;
  actualAmount?: BigNumber;
  agentRate?: BigNumber;
  agentAmount?: BigNumber;
  agentSplit?: BigNumber;
  policyRate?: BigNumber;
  contactStrId?: string;
  contact?: string;
}

interface ReceivableCalcData {
  amount?: BigNumber;
  contactsRates: { [contactStrId: string]: BigNumber };
  agents: contacts[];
  reportId: number;
  policyTotalRate: BigNumber;
  contactsSplit: Record<string, BigNumber>;
  accountId: string;
}

interface ReceivableCalcResult {
  taskId: string;
  success: boolean;
}

@injectable()
export class ReceivableScheduleService {
  @inject(CompanyService)
  // biome-ignore lint/correctness/noUnusedPrivateClassMembers: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private companyService: CompanyService;

  @inject(ProductService)
  // biome-ignore lint/correctness/noUnusedPrivateClassMembers: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private productService: ProductService;

  @inject(DataProcessService)
  private readonly dataProcessingService: DataProcessService;

  async getCompGridsForPolicy(policy: {
    product_name: string;
    account_id: string;
    writing_carrier_name: string;
  }) {
    const errors: string[] = [];

    if (!policy.product_name) {
      throw BusinessException.from(
        'Product name is required for fetching comp grids'
      );
    }
    if (!policy.writing_carrier_name) {
      throw BusinessException.from(
        'Writing carrier name is required for fetching comp grids'
      );
    }

    // Get all comp grids that have the grid product and the criteria that include the product
    const compGrids = await prismaClient.comp_grids.findMany({
      where: {
        account_id: policy.account_id,
        company: {
          company_name: policy.writing_carrier_name,
        },
        comp_grid_products: {
          some: {
            company_products: {
              some: {
                product_name: policy.product_name,
              },
            },
          },
        },
      },
      include: {
        comp_grid_levels: {
          where: {
            state: DataStates.ACTIVE,
          },
          include: {
            comp_grid_rates: true,
          },
        },
        related_comp_grid_levels: {
          where: {
            state: DataStates.ACTIVE,
          },
          include: {
            comp_grid_rates: true,
          },
        },
        // Only return grid products that include the product
        comp_grid_products: {
          where: {
            state: DataStates.ACTIVE,
            company_products: {
              some: {
                product_name: policy.product_name,
              },
            },
          },
          include: {
            company_products: true,
          },
        },
        // Only return criteria that include the product
        comp_grid_criteria: {
          where: {
            state: DataStates.ACTIVE,
            comp_grid_product: {
              company_products: {
                some: {
                  product_name: policy.product_name,
                },
              },
            },
          },
          include: {
            comp_grid_product: true,
            date_ranges: true,
            company: true,
          },
        },
      },
    });

    if (compGrids.length === 0) {
      errors.push(
        `No comp grids found for product ${policy.product_name} and carrier ${policy.writing_carrier_name}`
      );
    }

    return { success: errors.length === 0, compGrids, errors };
  }

  getMatchedCriteria(
    compGrids: Awaited<
      ReturnType<ReceivableScheduleService['getCompGridsForPolicy']>
    >['compGrids'],
    policy: report_data
  ) {
    let matchedCriteriaId = null;
    let matchedCompGrid = null;
    for (const compGrid of compGrids) {
      for (const compGridCriterion of compGrid.comp_grid_criteria) {
        const issueAgeCheck = checkIssueAge(compGridCriterion, policy);
        if (!issueAgeCheck.match) {
          continue;
        }
      

        const transactionTypeCheck = checkTextRule(
          'transaction_type',
          compGridCriterion,
          policy
        );
        if (!transactionTypeCheck.match) continue;
        const paymentModeCheck = checkTextRule(
          'payment_mode',
          compGridCriterion,
          policy
        );
        if (!paymentModeCheck.match) continue;
        matchedCriteriaId = compGridCriterion.id;
        matchedCompGrid = compGrid;
        break;
      }
    }
    return { matchedCriteriaId, matchedCompGrid };
  }

  async getAgentRate(query: {
    compGrid: Awaited<
      ReturnType<ReceivableScheduleService['getCompGridsForPolicy']>
    >['compGrids'][0];
    criteriaId: number;
    contactId: number;
    accountId: string;
    productType: string;
  }) {
    const { contactId, accountId, productType, criteriaId } = query;

    const contactLevel = await prismaClient.contact_levels.findFirst({
      where: {
        account_id: accountId,
        contact_id: contactId,
        AND: [
          {
            OR: [
              { product_type: productType },
              { product_type: '' },
              { product_type: null },
            ],
          },
          {
            OR: [
              { company_id: query.compGrid.company_id },
              { company_id: null },
            ],
          },
          {
            OR: [
              { start_date: null, end_date: null },
              { start_date: { lte: new Date() }, end_date: null },
              { start_date: null, end_date: { gte: new Date() } },
              {
                start_date: { lte: new Date() },
                end_date: { gte: new Date() },
              },
            ],
          },
        ],
      },
      orderBy: { start_date: 'desc' },
    });

    if (!contactLevel) {
      throw BusinessException.from(
        `Contact level not found for agent ${contactId} and product type ${productType}`
      );
    }

    const compGridLevel = [
      ...query.compGrid.related_comp_grid_levels,
      ...query.compGrid.comp_grid_levels,
    ].find((level) => level.name === contactLevel?.level_label);
    if (!compGridLevel) {
      throw BusinessException.from(
        `Comp grid level not found for level ${contactLevel?.level_label}`
      );
    }

    const compGridRate = compGridLevel?.comp_grid_rates.find(
      (rate) => rate.comp_grid_criterion_id === criteriaId
    );

    if (!compGridRate) {
      throw BusinessException.from(
        `Comp grid rate not found for criteriaId ${criteriaId} and level ${contactLevel?.level_label}`
      );
    }

    return BigNumber(compGridRate?.rate.toNumber());
  }

  getProductTypeFromCompGrid(
    productName: string,
    compGrid: Awaited<
      ReturnType<ReceivableScheduleService['getCompGridsForPolicy']>
    >['compGrids'][0]
  ) {
    const compGridProduct = compGrid.comp_grid_products.find((product) =>
      product.company_products.some(
        (companyProduct) => companyProduct.product_name === productName
      )
    );
    return compGridProduct?.type;
  }

  async getPolicyTotalRate(query: {
    compGrid: Awaited<
      ReturnType<ReceivableScheduleService['getCompGridsForPolicy']>
    >['compGrids'][0];
    criteriaId: number;
  }) {
    const { compGrid, criteriaId } = query;
    const compGridLevel = [
      ...compGrid.related_comp_grid_levels,
      ...compGrid.comp_grid_levels,
    ].find((level) => level.type === 'Agency level');
    if (!compGridLevel) {
      throw BusinessException.from(
        'Comp grid level not found for agency level'
      );
    }
    const compGridRate = compGridLevel?.comp_grid_rates.find(
      (rate) => rate.comp_grid_criterion_id === criteriaId
    );
    if (!compGridRate) {
      throw BusinessException.from(
        `Comp grid rate not found for criteriaId ${criteriaId} and level ${compGridLevel?.name}`
      );
    }
    return BigNumber(compGridRate?.rate.toNumber());
  }

  getActualRateSplit(data: {
    contactStrId: string;
    contacts: string[];
    contactSplits: { [contactStrId: string]: BigNumber };
  }) {
    const { contactStrId, contacts, contactSplits } = data;
    if (contactSplits[contactStrId]) {
      // Convert split percentage (0-100) to decimal (0-1) for rate calculation
      return BigNumber(contactSplits[contactStrId]).div(100);
    }
    // Evenly split the remain rates between the ones that don't have a specific rate
    const usedRates = Object.values(contactSplits).reduce(
      (acc, curr) => acc.plus(curr),
      BigNumber(0)
    );
    const remainRates = BigNumber(1).minus(usedRates);
    const splitRate =
      contacts.length > 1 ? remainRates.div(contacts.length - 1) : BigNumber(1);
    return splitRate;
  }

  // TODO: tests will be added later
  async calc(data: ReceivableCalcData) {
    const result: ReceivableResult[] = [];

    // Add policy record
    result.push({
      party: 'policy',
      rate: data.policyTotalRate,
      report_id: data.reportId,
      account_id: data.accountId,
      logs: {
        expectedRate: data.policyTotalRate,
      },
    });

    // Calculate agency records
    const { policyTotalRate } = data;

    // Calculate agent records
    for (const [contactStrId, agentRate] of Object.entries(
      data.contactsRates
    )) {
      const splitRate = this.getActualRateSplit({
        contactStrId,
        contacts: Object.keys(data.contactsRates),
        contactSplits: data.contactsSplit,
      });

      result.push({
        party: 'agent',
        rate: agentRate,
        report_id: data.reportId,
        account_id: data.accountId,
        contact_id: data.agents.find((agent) => agent.str_id === contactStrId)
          ?.id,
        logs: {
          policyRate: data.policyTotalRate,
          expectedRate: agentRate,
          actualRate: agentRate,
          agentRate,
          agentSplit: splitRate,
          contactStrId,
          contact: Formatter.contact(
            data.agents.find((agent) => agent.str_id === contactStrId)
          ),
        },
      });

      const expectedAgencyRate = policyTotalRate.minus(agentRate);

      result.push({
        party: 'agency',
        rate: expectedAgencyRate,
        report_id: data.reportId,
        account_id: data.accountId,
        contact_id: data.agents.find((agent) => agent.str_id === contactStrId)
          ?.id,
        logs: {
          policyRate: data.policyTotalRate,
          expectedRate: expectedAgencyRate,
          agentRate: agentRate,
          agentSplit: splitRate,
          contactStrId,
          contact: Formatter.contact(
            data.agents.find((agent) => agent.str_id === contactStrId)
          ),
        },
      });
    }

    return result;
  }

  async generateSchedules(data: ReceivableDTO) {
    const { report_id, report_ids } = data;

    const results: Array<{
      success: boolean;
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      result?: any;
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      error?: any;
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      message?: any;
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      statusText?: any;
    }> = [];

    if (report_id) {
      return await this.generateSchedule(report_id);
    } else if (report_ids) {
      await limitConcurrency(
        async (reportId) => {
          const scheduleResponse = await this.generateSchedule(reportId);
          results.push(scheduleResponse);
        },
        report_ids,
        30,
        {
          retries: 0,
          onFail: ({ error }) => {
            results.push({
              success: false,
              statusText: error.message,
              message: error.message,
              error: error.message,
            });
          },
        }
      );
      return results;
    }
  }

  async generateSchedule(report_id: number) {
    // Currently we only support receivable schedule for reports
    const report = await prismaClient.report_data.findUnique({
      where: { id: report_id },
    });
    const { contacts_split, account_id } = report;
    let contacts: string[] = report.contacts || [];

    const contactsSplit = contacts_split
      ? Object.fromEntries(
          Object.entries(contacts_split).map(([key, value]) => [
            key,
            BigNumber(value),
          ])
        )
      : {};
    if (!report) {
      throw BusinessException.from('Report not found');
    }

    const {
      success: compGridsSuccess,
      compGrids,
      errors,
    } = await this.getCompGridsForPolicy(report);

    if (!compGridsSuccess) {
      throw BusinessException.from(errors.join(', '));
    }

    const { matchedCriteriaId, matchedCompGrid } = this.getMatchedCriteria(
      compGrids,
      report
    );
    if (!matchedCriteriaId) {
      throw BusinessException.from('No matched criteria found');
    }
    if (!matchedCompGrid) {
      throw BusinessException.from('No matched comp grid found');
    }

    const productType = this.getProductTypeFromCompGrid(
      report.product_name,
      matchedCompGrid
    );
    if (!productType) {
      throw BusinessException.from(
        `No product type found for product ${report.product_name}`
      );
    }

    const agentRates: Record<string, BigNumber> = {};
    const allAgents = contacts?.length
      ? await prismaClient.contacts.findMany({
          where: {
            account_id: account_id,
            str_id: { in: contacts },
          },
        })
      : [];
    const agents = allAgents.filter((agent) => agent.type !== 'Sales rep');
    contacts = contacts.filter((contactStrId) =>
      allAgents.some(
        (agent) => agent.str_id === contactStrId && agent.type !== 'Sales rep'
      )
    );
    const agentsMap = new Map(agents.map((r) => [r.str_id, r]));

    for (const contactStrId of contacts) {
      const agent = agentsMap.get(contactStrId);

      if (!agent) {
        throw BusinessException.from(
          `Agent not found for contactStrId ${contactStrId}`
        );
      }

      const agentRate = await this.getAgentRate({
        compGrid: matchedCompGrid,
        criteriaId: matchedCriteriaId,
        contactId: agent.id,
        accountId: account_id,
        productType,
      });
      agentRates[contactStrId] = agentRate;
    }

    const policyTotalRate = await this.getPolicyTotalRate({
      compGrid: matchedCompGrid,
      criteriaId: matchedCriteriaId,
    });

    const result = await this.calc({
      amount: BigNumber(+report.commissionable_premium_amount),
      agents,
      contactsRates: agentRates,
      reportId: report.id,
      policyTotalRate,
      contactsSplit,
      accountId: account_id,
    });

    const resultCalc = await prismaClient.$transaction(async (tx) => {
      await tx.accounting_transaction_details.deleteMany({
        where: { report_id: report.id, type: 'receivable' },
      });
      return await tx.accounting_transaction_details.createManyAndReturn({
        data: result.map((r) => ({
          ...r,
          type: 'receivable',
          rate: r.rate?.toNumber(),
          amount: r.amount?.toNumber(),
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          logs: r.logs as any,
        })),
      });
    });

    return { success: true, result: resultCalc };
  }

  async executeReceivableCalc(
    data: [ReportData[]],
    account: ExtAccountInfo,
    body: ReceivableCalcRequest,
    proxyUserId?: string
  ): Promise<ReceivableCalcResult> {
    let dataProcessing: data_processing | null = null;
    const startTime = Date.now();

    try {
      // Check if task is already running
      const isLocked = await this.dataProcessingService.lockSyncTask({
        account,
        type: DataProcessingTypes.RECEIVABLE_CALC,
      });
      if (!isLocked) {
        throw new Error(
          'A receivable calculation is already running, please try again later'
        );
      }

      // Create or update data processing record
      dataProcessing = await this.dataProcessingService.create({
        str_id: nanoid(),
        account: { connect: { str_id: account.account_id } },
        user: { connect: { uid: account.uid } },
        params: JSON.stringify(body),
        proxy_user: proxyUserId ? { connect: { uid: proxyUserId } } : undefined,
        type: DataProcessingTypes.RECEIVABLE_CALC,
        status: DataProcessingStatuses.PROCESSING,
      });

      const policyData = data[0] || [];

      if (policyData.length === 0) {
        throw new Error('No policies found');
      }

      const policyDataIds = policyData.map((policy) => policy.id);

      // Generate receivable schedules
      const compGrids = await this.generateSchedules({
        report_ids: policyDataIds,
      });

      const generatedSchedules = Array.isArray(compGrids)
        ? (compGrids?.filter((schedule) => schedule.success).length ?? 0)
        : 0;

      // Update task status to completed
      await this.dataProcessingService.updateTaskStatus({
        str_id: dataProcessing.str_id,
        status: DataProcessingStatuses.COMPLETED,
        duration: Date.now() - startTime,
        stats: {
          totalPolicies: policyData.length,
          generatedSchedules: generatedSchedules,
        },
      });

      return {
        taskId: dataProcessing.str_id,
        success: true,
      };
    } catch (error) {
      // Update task status to error if data processing record exists
      if (dataProcessing) {
        await this.dataProcessingService.updateTaskStatus({
          str_id: dataProcessing.str_id,
          status: DataProcessingStatuses.ERROR,
          duration: Date.now() - startTime,
          notes: error.message,
        });
      }

      throw error;
    }
  }
}
