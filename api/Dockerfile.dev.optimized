FROM node:22-alpine3.20 AS base

# Set timezone to UTC by default
ENV TZ=UTC

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

# 优化：使用npm ci的缓存挂载
RUN --mount=type=cache,target=/root/.npm \
    mkdir -p ./common/node_modules ./api/node_modules

# 优化：分层复制package文件以更好地利用缓存
COPY ./package.json ./package-lock.json ./
COPY ./api/package.json ./api/
COPY ./common/package.json ./common/package.json

# 优化：只在需要时复制patches和prisma
COPY ./patches ./patches
COPY ./api/prisma ./api/prisma/
COPY ./api/.sentryclirc ./api/.sentryclirc

# 优化：使用缓存挂载安装依赖
RUN --mount=type=cache,target=/root/.npm \
    npm ci --prefer-offline --no-audit

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app

# 优化：只复制必要的源代码文件
COPY ./api ./api
COPY ./common ./common

# 优化：使用多阶段复制减少层数
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/common/node_modules ./common/node_modules
COPY --from=deps /app/api/node_modules ./api/node_modules

# COPY CHANGELOGS - 只复制必要的
COPY ./api/CHANGELOG.release.md ./api/CHANGELOG.release.md
COPY ./release/CHANGELOG.release.md ./release/CHANGELOG.release.md
COPY ./web/CHANGELOG.release.md ./web/CHANGELOG.release.md
COPY ./common/CHANGELOG.release.md ./common/CHANGELOG.release.md

# Next.js collects completely anonymous telemetry data about general usage.
# Learn more here: https://nextjs.org/telemetry
# Uncomment the following line in case you want to disable telemetry during the build.
ENV NEXT_TELEMETRY_DISABLED=1

WORKDIR /app/api
ENV NODE_OPTIONS="--max-old-space-size=4096"

# 优化：使用缓存挂载构建
RUN --mount=type=cache,target=/app/api/.next/cache \
    npm run build

# Production image, copy all the files and run next
FROM base AS runner
RUN npm i -g npm@10
WORKDIR /app

ENV NODE_ENV=development
ENV RUNTIME=DEV
ENV NEXT_TELEMETRY_DISABLED=1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 优化：合并COPY命令减少层数
COPY --from=builder --chown=nextjs:nodejs /app/api/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/api/.next/static ./api/.next/static
COPY --from=builder --chown=nextjs:nodejs /app/api/CHANGELOG.release.md ./api/CHANGELOG.release.md
COPY --from=builder --chown=nextjs:nodejs /app/release/CHANGELOG.release.md ./release/CHANGELOG.release.md
COPY --from=builder --chown=nextjs:nodejs /app/web/CHANGELOG.release.md ./web/CHANGELOG.release.md
COPY --from=builder --chown=nextjs:nodejs /app/common/CHANGELOG.release.md ./common/CHANGELOG.release.md

USER nextjs

EXPOSE 3000

ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

CMD ["node", "api/server.js"]
