# api

## Releases on 2025-08-11

### Version 6.14.6
<details>

### Patch Changes
 - Added legend size limit widgets and enabled time series for stacked bar charts
 - Fixing manual group handling on the Commissions page
</details>

### Version 6.14.5
<details>

### Patch Changes
 - Allow admin to create global data update
 - Improves the handling of filters related to empty fields in policy data.
 - Fixed comp grid pdf export not exporting all rates
 - Add custom report delete feature
 - 1. Add filters to show documents with missing companies, types, statement amounts, and unverified fields. 2. Tweak the workflow to auto-verify untrusted fields when the document is processed. 3. Reduce the confidence threshold to get more classification results.
 - Extend data actions tool to being able to set json fields in the db
 - Speed up login / middleware by reducing/parallelizing db queries
- Updated dependencies [e9a974f]
- Updated dependencies [d8e887e]
- Updated dependencies [f4dc66e]
- Updated dependencies [c2de5f7]
  - common@0.24.26
</details>

## Releases on 2025-08-07

### Version 6.14.4
<details>

### Patch Changes
 - Fix data update retrieval logic to include access type for account queries
 - Only retain the flags that are valid for the current `where` clause, and move `flags` and `tags` from the `globalWhere` into the `applyStatementFilter` function.
 - Add virtual_type filter and update loadFieldFilters function
 - Export alignment on Commission page
  - Move config from web to common, and add textFormatter for use in export
  - Refactor the statement_data export logic with the following steps: getData → get fields based on config and accountSetting → fetch dynamicSelect data → formatData → export CSV
 - Fixed pagination on agents transactions details page
 - Enhance reconciliation data API to support Agents filtering
 - Revamped widget spec schema and applied it across all saved widgets
 - Update the text editor to take in a function instead of just a piece of code.
 - Fixed agent commissions payout rate for sales rep in comp report calculation.
 - Fixed agent groups not saving on agents page
 - Move the 'Is grouped' column to the Views and Fields config
- Updated dependencies [b73e9b4]
- Updated dependencies [779544f]
- Updated dependencies [3bfdb3e]
- Updated dependencies [cd54602]
- Updated dependencies [d6a5402]
- Updated dependencies [634007f]
- Updated dependencies [39a4664]
- Updated dependencies [422c05c]
  - common@0.24.25
</details>

## Releases on 2025-08-05

### Version 6.14.3
<details>

### Patch Changes
 - Fix the issue where bulk document edits cannot be saved
- Updated dependencies [6159369]
  - common@0.24.24
</details>

## Releases on 2025-08-04

### Version 6.14.2
<details>

### Patch Changes
 - Remove JavaScript-based sorting and pagination, use database pagination instead
 - Update the commission total calculation on the Documents page to exclude duplicate commission amounts introduced by Grouping V2.
</details>

### Version 6.14.1
<details>

### Patch Changes
 - Remove duplicated field definitions
 - Added validation and default values to agent balance and total commissions in the report summary page.
- Updated dependencies [6b62359]
  - common@0.24.23
</details>

## Releases on 2025-08-01

### Version 6.14.0
<details>

### Minor Changes
 - Implemented agent payout balance optimization for report group summary page

### Patch Changes
 - Add auto document processing trigger to bulk edit.
 - Fix save report details issue
 - Add 'Is grouped' field to export strategy with handler for state representation
 - Bulk run receivable calc from data processing page
- Updated dependencies [3d41e67]
  - common@0.24.22
</details>

## Releases on 2025-07-31

### Version 6.13.0
<details>

### Minor Changes
 - Added statement month filter to documents page.

### Patch Changes
 - Document part optimisations:
  1. Fixed page crash issue when uploading files in the local environment
  2. Added status tooltips on the admin document page
  3. Added processor selector and classification to the API whitelist
  4. Replaced document type logic with currency.js
 - Add 'json' to the list of allowed file types in the saved reports.
 - Revert comp calc with no comp grid to its previous working logic.
- Updated dependencies [4513b03]
  - common@0.24.21
</details>

## Releases on 2025-07-29

### Version 6.12.1
<details>

### Patch Changes
 - Dashboard improvements including data filter in chart info, reduction of implicit defaults (such as date filter type), schema validation on save, and `none` as a date type.
 - Improvements for Global Documents:
  • Add an enhanced company filters that shows global companies, or account companies if no global ones exist.
  • Remove fuzzy search and replace with strict matching.
  • Add edit mode for documents.
  • Fix various UI bugs.
 - Align export Reconciliation behavior with FE, move config from web to common, and add textFormatter for use in export.
 - Added capability to update grouped commission lines to data actions tool
- Updated dependencies [d1f39d9]
- Updated dependencies [ad9cdf8]
- Updated dependencies [90a43ba]
- Updated dependencies [2baa4bc]
  - common@0.24.20
</details>

### Version 6.12.0
<details>

### Minor Changes
 - Fixed: bulk edit from csv of commissions failed on grouped data and it didn't show a clear error message.

### Patch Changes
 - Fixed error when update policy term months
 - Add an auto-trigger for E2E document processing.
  Currently, auto-processing is triggered in two scenarios:
  1. When a document is uploaded for the first time, and both the company and type fields are not empty.
  2. When the company and type were previously empty but are now filled, and the document status is still “new”.
</details>

## Releases on 2025-07-28

### Version 6.11.15
<details>

### Patch Changes
 - Added feature to edit agent receivables at policies page
- Updated dependencies [ad615d5]
  - common@0.24.19
</details>

## Releases on 2025-07-25

### Version 6.11.14
<details>

### Patch Changes
 - Added support to edit receivables when edited from the policies page.
 - Add comp grid viewer pdf export
 - Added document classification stats to Metrics page
- Updated dependencies [d5a38c8]
- Updated dependencies [fab41cf]
  - common@0.24.18
</details>

## Releases on 2025-07-24

### Version 6.11.13
<details>

### Patch Changes
 - Display the Policy's Target Premium data when there is no Commission data.
 - Add some missing fields in the export.
- Updated dependencies [adbb460]
  - common@0.24.17
</details>

