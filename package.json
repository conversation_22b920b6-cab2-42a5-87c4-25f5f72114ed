{"name": "@fintary/root", "private": true, "version": "1.0.0", "workspaces": ["api", "common", "web", "release", "functions/**"], "overrides": {"typescript": "^5.8.3", "next": "^15.4.2", "@codemirror/state": "6.5.2", "brace-expansion": "^2.0.2"}, "devDependencies": {"@biomejs/biome": "^2.1.2", "@changesets/cli": "^2.29.3", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "concurrently": "^9.2.0", "eslint-plugin-prettier": "^5.5.3", "globals": "^14.0.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "patch-package": "^8.0.0"}, "scripts": {"changelog": "changeset", "build:functions": "npm run build --workspace=functions/** --if-present", "build:functions:watch": "npm run build:watch --workspace=functions/** --if-present", "local:emulate": "concurrently 'npm:build:functions:watch' 'firebase use fintary-dev && firebase emulators:start --only auth,functions,firestore'", "db-proxy-dev": "./misc/db/cloud-sql-proxy --port 5433 fintary-dev:us-central1:fintary-dev", "db-proxy-prod": "./misc/db/cloud-sql-proxy fintary-prod:us-central1:fintary-db-prod", "deep-clean": "npm run deep-clean --workspaces && rm -rf node_modules", "deploy-dev-db": "dotenv -e ./api/.env.dev -- npx prisma migrate deploy --schema=./api/prisma/schema.prisma", "deploy-dev": "cp ./api/Dockerfile.dev Dockerfile && time gcloud builds submit --tag gcr.io/fintary-dev/api --project fintary-dev --ignore-file .dockerignore && gcloud run deploy api --image=gcr.io/fintary-dev/api:latest --set-cloudsql-instances=fintary-dev:us-central1:fintary-dev --region=us-central1 --project=fintary-dev && gcloud run services update-traffic api --project=fintary-dev --region=us-central1 --to-latest && rm Dockerfile && npm run deploy-dev-db", "deploy-preview": "cp ./api/Dockerfile.prod Dockerfile && time gcloud builds submit . --tag gcr.io/fintary-prod/api --project fintary-prod --ignore-file .dockerignore && gcloud run deploy api-preview --image=gcr.io/fintary-prod/api:latest --set-cloudsql-instances=fintary-prod:us-central1:fintary-db-prod --region=us-central1 --project=fintary-prod && gcloud run services update-traffic api-preview --project=fintary-prod --region=us-central1 --to-latest && rm Dockerfile", "deploy-prod-db": "dotenv -e ./api/.env.prod -- npx prisma migrate deploy --schema=./api/prisma/schema.prisma", "deploy-prod": "cp ./api/Dockerfile.prod Dockerfile && time gcloud builds submit --tag gcr.io/fintary-prod/api --project fintary-prod  --ignore-file .dockerignore && gcloud run deploy api --image=gcr.io/fintary-prod/api:latest --set-cloudsql-instances=fintary-prod:us-central1:fintary-db-prod --region=us-central1 --project=fintary-prod && gcloud run services update-traffic api --project=fintary-prod --region=us-central1 --to-latest && rm Dockerfile && npm run deploy-prod-db", "deploy": "npm run deploy-dev && npm run deploy-prod", "docker:build-dev": "docker build . -f api/Dockerfile.dev -t fintary-api:dev", "docker:build-prod": "docker build . -f api/Dockerfile.prod -t fintary-api:prod", "lint": "biome check", "lint:fix": "biome check --write", "lint:changed": "biome check --write --changed", "lint:ci": "biome ci", "type:check": "npm run -w api type:check && npm run -w common type:check && npm run type:check -w 'functions/**'", "postinstall": "patch-package", "prepare": "husky", "release": "npx --yes @changesets/cli version && ./scripts/update-changelog-dates.sh && node scripts/changelog-consolidate.js", "test": "cd api && npm run test:unit -- --silent && cd ../common && npm run test:unit -- --silent && cd ../web && npm run test -- --silent"}, "lint-staged": {"*": ["biome check --no-errors-on-unmatched --files-ignore-unknown=true"]}}