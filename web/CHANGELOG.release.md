# web

## Releases on 2025-08-11

### Version 10.23.5
<details>

### Patch Changes
 - Added legend size limit widgets and enabled time series for stacked bar charts
 - Fix bug where the page crashes immediately when navigating to the "Data actions" page.
</details>

### Version 10.23.4
<details>

### Patch Changes
 - Allow admin to create global data update
 - Adds group search and alphabetical sorting for Data update tools
 - Add commissions custom types fields to bulk update csv
 - Fixed document groups not passing date filters correctly to the documents view.
 - Add custom report delete feature
 - Documents page more date filters text overlapping fix
 - 1. Add filters to show documents with missing companies, types, statement amounts, and unverified fields. 2. Tweak the workflow to auto-verify untrusted fields when the document is processed. 3. Reduce the confidence threshold to get more classification results.
 - Speed up login / middleware by reducing/parallelizing db queries
- Updated dependencies [e9a974f]
- Updated dependencies [d8e887e]
- Updated dependencies [f4dc66e]
- Updated dependencies [c2de5f7]
  - common@0.24.26
</details>

## Releases on 2025-08-07

### Version 10.23.3
<details>

### Patch Changes
 - Removed 'Select one' option from unit selector for date fields in data actions criteria
 - Add virtual_type filter and update loadFieldFilters function
 - Export alignment on Commission page
  - Move config from web to common, and add textFormatter for use in export
  - Refactor the statement_data export logic with the following steps: getData → get fields based on config and accountSetting → fetch dynamicSelect data → formatData → export CSV
 - Fixed pagination on agents transactions details page
 - Enhance reconciliation data API to support Agents filtering
 - Revamped widget spec schema and applied it across all saved widgets
 - Update the text editor to take in a function instead of just a piece of code.
 - Added agent payout rate field to bulk edit in commissions page
 - Move the 'Is grouped' column to the Views and Fields config
- Updated dependencies [b73e9b4]
- Updated dependencies [779544f]
- Updated dependencies [3bfdb3e]
- Updated dependencies [cd54602]
- Updated dependencies [d6a5402]
- Updated dependencies [634007f]
- Updated dependencies [39a4664]
- Updated dependencies [422c05c]
  - common@0.24.25
</details>

## Releases on 2025-08-05

### Version 10.23.2
<details>

### Patch Changes
- Updated dependencies [6159369]
  - common@0.24.24
</details>

## Releases on 2025-08-04

### Version 10.23.1
<details>

### Patch Changes
 - Remove duplicated field definitions
 - Added validation and default values to agent balance and total commissions in the report summary page.
- Updated dependencies [6b62359]
  - common@0.24.23
</details>

## Releases on 2025-08-01

### Version 10.23.0
<details>

### Minor Changes
 - Implemented agent payout balance optimization for report group summary page

### Patch Changes
 - Bulk run receivable calc from data processing page
 - Fix the Target premium field shows as [object Object] in edit mode on Commissions page
- Updated dependencies [3d41e67]
  - common@0.24.22
</details>

## Releases on 2025-07-31

### Version 10.22.0
<details>

### Minor Changes
 - Added statement month filter to documents page.

### Patch Changes
 - Document part optimisations:
  1. Fixed page crash issue when uploading files in the local environment
  2. Added status tooltips on the admin document page
  3. Added processor selector and classification to the API whitelist
  4. Replaced document type logic with currency.js
 - Add more tests for comp calc regression tests.
 - Custom reports improvements
- Updated dependencies [4513b03]
  - common@0.24.21
</details>

## Releases on 2025-07-30

### Version 10.21.10
<details>

### Patch Changes
 - Fix the document page crash issue by filter color
</details>

## Releases on 2025-07-29

### Version 10.21.9
<details>

### Patch Changes
 - Include Premium amount field to bulk edit csv in the commissions page.
 - Dashboard improvements including data filter in chart info, reduction of implicit defaults (such as date filter type), schema validation on save, and `none` as a date type.
 - Improvements for Global Documents:
  • Add an enhanced company filters that shows global companies, or account companies if no global ones exist.
  • Remove fuzzy search and replace with strict matching.
  • Add edit mode for documents.
  • Fix various UI bugs.
 - Align export Reconciliation behavior with FE, move config from web to common, and add textFormatter for use in export.
 - Added capability to update grouped commission lines to data actions tool
- Updated dependencies [d1f39d9]
- Updated dependencies [ad9cdf8]
- Updated dependencies [90a43ba]
- Updated dependencies [2baa4bc]
  - common@0.24.20
</details>

### Version 10.21.8
<details>

### Patch Changes
 - Add admin ability to invite a user that is not currently associated with an existing account
 - Fixed override documents upload
 - Fix file removal logic in UploadReportModal component
</details>

## Releases on 2025-07-28

### Version 10.21.7
<details>

### Patch Changes
 - Added feature to edit agent receivables at policies page
 - Fix crash when dynamicSelects is undefined
- Updated dependencies [ad615d5]
  - common@0.24.19
</details>

## Releases on 2025-07-25

### Version 10.21.6
<details>

### Patch Changes
 - Add comp grid viewer pdf export
 - Added document classification stats to Metrics page
- Updated dependencies [d5a38c8]
- Updated dependencies [fab41cf]
  - common@0.24.18
</details>

