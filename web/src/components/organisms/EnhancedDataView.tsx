// @ts-nocheck
import { <PERSON>, Button, Divider, Tooltip, Typography } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import { ResponseAction } from 'common/constants';
import { endpoint } from 'common/constants/table';
import { SystemRoles } from 'common/globalTypes';
import { numberOrDefault } from 'common/helpers';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import cloneDeep from 'lodash-es/cloneDeep';

import { axios } from '@/api/interceptor';
import DataBulkAdd from '@/components/DataBulkAdd';
import DataForm from '@/components/DataForm';
import LoadingCircle from '@/components/atoms/LoadingCircle';
import EnhancedTable from '@/components/molecules/EnhancedTable';
import { LOCAL_STORAGE_KEYS } from '@/constants/account';
import useSnackbar from '@/contexts/useSnackbar';
import { auth } from '@/firebase';
import API from '@/services/API';
import Formatter from '@/services/Formatter';
import { isNill } from '@/services/tools';
import { useMenuStore } from '@/store';
import useAccountStore from '@/store/accountStore';
import { DocumentPreviewKeys, FieldTypes } from '@/types';
import { getControlledPagination } from '@/utils/getControlledPagination';
import { Toolbar } from './EnhancedDataView/components/Toolbar';
import { useEnhancedDataViewStore } from './EnhancedDataView/store';
import { useBuildFilterList } from './EnhancedDataView/hooks/useBuildFilterList';
import {
  useDefaultSortingFromQuery,
  useUpdateSortingInQuery,
} from './EnhancedDataView/hooks/useDefaultSortingFromQuery';
import { DeleteConfirmDialog } from '../DeleteConfirmDialog';
import { useDeleteAndCopyActions } from './useDeleteAndCopyActions';

dayjs.extend(utc);

const internalDefaultSorting = {
  order: 'desc',
  orderBy: 'created_at',
};

/**
 * EnhancedDataView component provides a highly configurable and dynamic data table view
 * with support for filtering, sorting, pagination, bulk actions, and more.
 *
 * @param {Object} props - The properties object.
 * @param {Object} props.dataSpec - Specification for the data table, including fields, labels, and query configurations.
 * @param {boolean} [props.bulkAdd=false] - Enables bulk add functionality.
 * @param {boolean} [props.enableBulkEditCsv=false] - Enables bulk edit functionality.
 * @param {Function} [props.setSelectedData=(a) => {}] - Callback to set selected data rows.
 * @param {Object} [props.options={ mode: 'default' }] - Configuration options for the table view.
 * @param {Function} [props.prefilter=undefined] - Function to prefilter the data.
 * @param {Array} [props.suggested=undefined] - Array of suggested data items.
 * @param {Object|null} [props.filters=null] - Custom filters for the data table.
 * @param {boolean} [props.hideAdd=false] - Hides the add button.
 * @param {boolean} [props.hideExport=false] - Hides the export button.
 * @param {boolean} [props.hideSelectedCount=false] - Hides the selected count indicator.
 * @param {boolean} [props.enableSaves=false] - Enables saving reports functionality.
 * @param {Array} [props.exportOptions=[]] - Options for exporting data.
 * @param {Object} [props.defaultData={}] - Default data for the table.
 * @param {string|null} [props.reportId=null] - Report ID for the data table.
 * @param {boolean} [props.showTotals=false] - Displays totals in the table.
 * @param {boolean} [props.readOnly=false] - Makes the table read-only.
 * @param {string} [props.rowKey=''] - Key to uniquely identify rows.
 * @param {Function} [props.actionsEnabled=() => false] - Function to enable actions for rows.
 * @param {any} [props.actions=[]] - Actions available for rows in the table.
 * @param {Function} [props.lockRow=() => false] - Function to lock specific rows.
 * @param {Array} [props.outstandingMobileFields=[]] - Fields to highlight in mobile view.
 * @param {boolean} [props.enableMultiSelect=true] - Enables multi-select functionality.
 * @param {boolean} [props.customHeaderActions=false] - Enables custom header actions.
 * @param {boolean} [props.enableEdit=true] - Enables edit functionality for rows.
 * @param {boolean} [props.enableResetFilters=true] - Enables reset filters functionality.
 * @param {boolean} [props.nonSelectableOnMobile=false] - Disables selection on mobile devices.
 * @param {Function} [props.onBulkSync=undefined] - Callback for bulk synchronization.
 * @param {Array} [props.bulkActions=[]] - Bulk actions available for the table.
 * @param {Array} [props.extraActions=[]] - Extra actions available for the table.
 * @param {string} [props.variant=''] - Variant of the table view.
 * @param {Array} [props.extraFormActions=[]] - Extra actions for the form.
 * @param {Function} [props.onQueryKeyChange=(_queryKey) => {}] - Callback for query key changes.
 * @param {Array} [props.notUpdateFields=[]] - Fields that should not be updated.
 * @param {boolean} [props.enableBulkDelete=true] - Enables bulk delete functionality.
 * @param {Object} [props.tableConfig={}] - Configuration for the table.
 * @param {Object} [props.extraBatchEditFields={}] - Extra fields for bulk edit functionality.
 * @param {boolean} [props.enableRowDeleteAction=false] - Enables row delete action.
 * @param {boolean} [props.enableRowCreateCopyAction=false] - Enables row create copy action.
 * @returns {React.ReactElement} The EnhancedDataView component.
 */
const EnhancedDataView = (props) => {
  const {
    dataSpec,
    bulkAdd = false,
    enableBulkEditCsv = false,
    // biome-ignore lint/suspicious/noEmptyBlockStatements: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    setSelectedData = (_a) => {},
    options = { mode: 'default' },
    prefilter = undefined,
    filters = null,
    hideSelectedCount = false,
    exportOptions = [],
    defaultData = {},
    reportId = null,
    showTotals = false,
    readOnly = false,
    rowKey = '',
    actionsEnabled = () => false,
    lockRow = () => false,
    outstandingMobileFields = [],
    enableMultiSelect = true,
    customHeaderActions = false,
    enableEdit = true,
    nonSelectableOnMobile = false,
    onBulkSync = undefined,
    bulkActions = [],
    extraFormActions = [],
    // biome-ignore lint/suspicious/noEmptyBlockStatements: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    onQueryKeyChange = (_queryKey) => {},
    notUpdateFields = [],
    enableBulkDelete = true,
    tableConfig = {},
  } = props;

  const addGlobalCompanyConfig = useEnhancedDataViewStore(
    (s) => s.addGlobalCompanyConfig
  );

  const { menuOpen } = useMenuStore();
  const [searchParams, setSearchParams] = useSearchParams();
  const prevSearchParams = useRef(searchParams.toString());
  const mode = searchParams.get('m') ?? 'list';
  const rowsPerPage = numberOrDefault(searchParams.get('limit'), 50);
  let page = numberOrDefault(searchParams.get('page'), 0);
  const [newData, setNewData] = useState(defaultData || {});
  const [oldData, setOldData] = useState(defaultData || {});
  const { order: defaultOrder, orderBy: defaultOrderBy } =
    useDefaultSortingFromQuery();
  const [orderBy, setOrderBy] = useState(
    defaultOrderBy || dataSpec.defaultOrderBy || internalDefaultSorting.orderBy
  );
  const [order, setOrder] = useState(
    defaultOrder || dataSpec.defaultSort || internalDefaultSorting.order
  );
  const { data: accountInfo } = API.getBasicQuery('accounts');
  const { selectedAccount } = useAccountStore();
  const { showSnackbar } = useSnackbar();
  const displayFields = useEnhancedDataViewStore((s) => s.displayFields || []);

  const qcParam = searchParams.get('qc');
  const idParam = searchParams.get('id');
  const includeZeroCommissionParam =
    searchParams.get('incl_zero_commissions') === 'true';

  const reconciliationThreshold = accountInfo?.reconciliation_threshold ?? 1;

  const poster = API.getMutation(dataSpec.table, 'POST');
  const patcher = API.getMutation(dataSpec.table, 'PATCH');

  const API_PREFIX = `${process.env.REACT_APP_API}/api`;
  // TODO: should start using apiEndpoint instead of table, clear meaning
  const bulkEditUrl = `${API_PREFIX}/${dataSpec.apiEndpoint || dataSpec.table}/${dataSpec.bulkEditEndpoint || 'bulk_edit'}`;
  const bulkPatcher = useMutation({
    mutationKey: [bulkEditUrl, selectedAccount?.accountId],
    // @ts-ignore
    mutationFn: async ({ data, params }) => {
      return axios.patch(bulkEditUrl, data, {
        params,
        paramsSerializer: (params) => {
          const paramsString = new URLSearchParams(params).toString();
          return paramsString;
        },
      });
    },
  });

  const deleter = API.getMutation(dataSpec.table, 'DELETE');
  const posterBulkAdd = API.getMutation(`${dataSpec.table}/bulk_add`, 'POST');
  const bulkEditCsv = API.getMutation(
    `${dataSpec.table}/bulk-edit-csv`,
    'PATCH'
  );

  // === end handle scroll filter ===

  // TODO (frank.santillan): Fix commission_filters. Probably better for us to just move onto the next version of configuring these, so will just comment this out for now.
  // useEffect(() => {
  //   if (accountSettings?.commissions_filters?.length > 0) {
  //     accountSettings.commissions_filters.forEach((_filter) => {
  //       const filter = _filter.filter((item) => item);
  //       if (!filter) {
  //         console.warn('Invalid filter', filter);
  //         return;
  //       }
  //       const filterNameValue = filter.split('::');
  //       setSearchParams((prev) => {
  //         prev.set(filterNameValue[0], filterNameValue[1]);
  //         return prev;
  //       });
  //     });
  //   }
  // }, [accountSettings]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    const currentSearchParams = new URLSearchParams(searchParams.toString());
    const oldSearchParams = new URLSearchParams(prevSearchParams.current);

    const inPreview =
      currentSearchParams.get('m') === DocumentPreviewKeys.PREVIEW;
    if (inPreview) {
      return;
    }

    if (currentSearchParams.get('m') !== 'edit') {
      currentSearchParams.delete('page');
      oldSearchParams.delete('page');

      if (currentSearchParams.toString() !== oldSearchParams.toString()) {
        handleChangePage('', 0);
      }
      prevSearchParams.current = currentSearchParams.toString();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]);

  // Add cleanup function for form data
  useEffect(() => {
    return () => {
      // Clean up form data when component unmounts
      setNewData({});
      setOldData({});
    };
  }, []);

  const queryKey = useMemo(
    () => [
      selectedAccount?.accountId,
      dataSpec.table,
      page,
      rowsPerPage,
      orderBy,
      order,
      Array.from(searchParams.entries()).toString(),
    ],
    [
      selectedAccount?.accountId,
      dataSpec.table,
      page,
      rowsPerPage,
      orderBy,
      order,
      searchParams,
    ]
  );

  const updateSearchParams = (kvMap) =>
    setSearchParams((prev) => {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      Object.entries(kvMap).forEach(([k, v]) => {
        if ([undefined, null].includes(v)) {
          prev.delete(k);
        } else {
          prev.set(k, v);
        }
      });
      return prev;
    });

  const {
    isLoading,
    isError,
    data: queryData,
    refetch,
    error,
  } = useQuery({
    queryKey,
    queryFn: async () => {
      let queryParams = `?page=${page}&limit=${rowsPerPage}&orderBy=${orderBy}&sort=${order}`;
      if (reportId) {
        queryParams += `&comp_report_id=${reportId}`;
      }

      // Filter out limit, page, orderBy, and sort from prevSearchParams to avoid duplication
      const searchParamsObj = new URLSearchParams(prevSearchParams.current);
      searchParamsObj.delete('limit');
      searchParamsObj.delete('page');
      searchParamsObj.delete('orderBy');
      searchParamsObj.delete('sort');

      let additionalQueryParams = searchParamsObj.toString()
        ? `&${searchParamsObj.toString()}`
        : '';

      if (dataSpec.queryChips && qcParam) {
        const chipQuery = dataSpec.queryChips[qcParam]?.query ?? {};
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        Object.entries(chipQuery).forEach(([k, v]) => {
          if (Array.isArray(v)) {
            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            v.forEach((e) => {
              additionalQueryParams += `&${k}=${encodeURIComponent(e)}`;
            });
          } else {
            additionalQueryParams += `&${k}=${encodeURIComponent(v)}`;
          }
        });
      }

      const url = `${process.env.REACT_APP_API}/api/${dataSpec.table}${queryParams}${additionalQueryParams}`;
      const res = await fetch(url, {
        method: 'GET',
        headers: await API.getHeaders(),
      });
      const data = await res.json();
      if (res.status === 401 && data.action === ResponseAction.LOG_OUT) {
        window.dispatchEvent(new Event('sessionExpired'));
        return;
      }
      if (data.success === false) {
        throw new Error(data.message);
      }
      return data;
    },
    enabled:
      !!auth?.currentUser ||
      !!localStorage.getItem(LOCAL_STORAGE_KEYS.ssoToken),
  });

  useEffect(() => {
    onQueryKeyChange(queryKey);
  }, [queryKey, onQueryKeyChange]);

  useEffect(() => {
    if (isError) {
      showSnackbar(
        error?.message || 'Error retrieving data, please try again later',
        'error'
      );
    }
  }, [error, isError, showSnackbar]);

  // UseEffect(() => {
  //   setInitialFilterValues(null);
  // }, [queryKey]);

  /**
   * For context, the API/query?args is fetch dynamically based on the dataSpec.table
   * Given that, to have the pagination working properly when uses this component, the API should return the following structure:
   *  {
   *      data: Array<any>, // list of data items
   *      total: number    // total number of items in the current query, same as data.length if no pagination is applied
   *      count: number,  // total number of items in the database
   *  }
   */
  const data = useMemo(() => {
    if (Array.isArray(queryData)) {
      return queryData;
    }
    if (Array.isArray(queryData?.data)) {
      return queryData.data;
    }
    return [];
  }, [queryData]);

  const count = useMemo(() => queryData?.count ?? 0, [queryData?.count]);
  const fieldOptions = useMemo(
    () => (filters || queryData?.fieldOptions) ?? {},
    [filters, queryData?.fieldOptions]
  );
  const totals = useMemo(() => queryData?.totals ?? {}, [queryData?.totals]);
  useEffect(() => {
    if (
      mode === 'edit' &&
      idParam &&
      Array.isArray(data) &&
      data?.length === 1
    ) {
      setNewData(data[0]);
      setOldData(JSON.parse(JSON.stringify(data[0])));
    }
  }, [data, mode, idParam]);

  const searchParamsToString = searchParams.toString();

  const handleChangePage = async (_event, newPage) => {
    setSearchParams((prev) => {
      if (newPage && +newPage > 0) {
        page = newPage;
        prev.set('page', newPage);
      } else {
        prev.delete('page');
      }
      return prev;
    });
  };

  const handleChangeRowsPerPage = (e) => {
    setSearchParams((prev) => {
      prev.delete('page');
      prev.set('limit', e.target.value);
      return prev;
    });
  };

  const dataDesc = useMemo(() => {
    return {
      label: dataSpec.label,
      table: dataSpec.table,
      copyable:
        typeof dataSpec.copyable === 'function'
          ? dataSpec.copyable(newData)
          : (dataSpec.copyable ?? false),
      disableDelete: dataSpec.disableDelete ?? false,
      editable: true,
      fields: Object.entries(dataSpec.fields)
        .filter(([_k, v]) => v.enabled)
        // @ts-expect-error
        // biome-ignore lint/performance/noAccumulatingSpread: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        .reduce((acc, [k, v]) => [...acc, { ...v, id: k }], []),
    };
  }, [dataSpec, newData]);

  const deleteRows = async (ids) => {
    await deleter.mutateAsync({ ids }).catch((err) => {
      showSnackbar(err?.message || 'Delete failed', 'error');
    });
    setTimeout(refetch, 300);
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const headers = useMemo(() => {
    const newHeaders = Object.entries(dataSpec.fields)
      .map(([k, v]) => ({
        ...v,
        id: k,
      }))
      .filter((i) => i.enabled)
      .filter((i) => (options.mode === 'reconciler' ? i.reconciler : true))
      .filter((i) => displayFields.includes(i.label));

    const contacts = searchParams.getAll('contacts');

    if (contacts.length === 1) {
      const exportOption = exportOptions.find(
        (option) => option.id === 'export-producer-view'
      );
      if (exportOption) {
        exportOption.options.disabled = false;
      }
    } else {
      const exportOption = exportOptions.find(
        (option) => option.id === 'export-producer-view'
      );
      if (exportOption) {
        exportOption.options.disabled = true;
      }
    }

    // TODO: Find a better way of handling this

    // Figure out first and last day of payments, rather than the filters set in this view
    // queryData?.data?.forEach((row) => {
    //   if (row.payments) {
    //     const payments = Object.keys(row.payments);
    //     const firstPayment = payments[0];
    //     const lastPayment = payments[payments.length - 1];
    //     if (firstPayment) {
    //       const firstPaymentDate = dayjsUTC(firstPayment);
    //       if (
    //         !filteredValues?.effective_date_start ||
    //         firstPaymentDate.isBefore(filteredValues?.effective_date_start)
    //       ) {
    //         setFilteredValues({
    //           ...filteredValues,
    //           effective_date_start: firstPaymentDate.toDate(),
    //         });
    //       }
    //     }
    //     if (lastPayment) {
    //       const lastPaymentDate = dayjsUTC(lastPayment);
    //       if (
    //         !filteredValues?.effective_date_end ||
    //         lastPaymentDate.isAfter(filteredValues?.effective_date_end)
    //       ) {
    //         setFilteredValues({
    //           ...filteredValues,
    //           effective_date_end: lastPaymentDate.toDate(),
    //         });
    //       }
    //     }
    //   }
    // });
    // TODO: This is very specific for commissions. Generalize somehow outside of this component.

    if (dataSpec?.fields?.commission_amount_monthly?.enabled) {
      const startEffective = dayjs.utc(
        searchParams.get('effective_date_start')
      );
      const startPayment = dayjs.utc(fieldOptions.payment_date_first);
      const endEffective = dayjs.utc(searchParams.get('effective_date_end'));
      const endPayment = dayjs.utc(fieldOptions.payment_date_last);
      const start = startEffective.isBefore(startPayment)
        ? startEffective.startOf('month')
        : startPayment.startOf('month');
      const end = endEffective.isAfter(endPayment)
        ? endEffective.startOf('month')
        : endPayment.startOf('month');
      let maxMonths = 60; // 5 years max
      for (let i = end; i >= start; i = i.subtract(1, 'month')) {
        newHeaders.push({
          id: 'commission_amount_monthly',
          id2: i.format('MM/DD/YYYY'),
          label: i.format('MMM YYYY'),
          // Formatter: Formatter.currency,
          numeric: true,
          getter: (row) => {
            const month = i.format('MM/DD/YYYY');
            let commissionReceived =
              row.commission_amount_monthly?.[month]?.commission_amount_monthly;
            let commissionExpected = row.commission_expected_monthly?.[month];
            let commissionBalance = row.commission_balance_monthly?.[month];
            // Aggregate child values into parent
            // TODO: Unlink when showing child policies, will ahve to do with normal headers too
            if (row?.children_reconciliation_data?.length > 0) {
              // biome-ignore lint/suspicious/noImplicitAnyLet: Initial?
              let childrenCommissionReceived;
              // biome-ignore lint/suspicious/noImplicitAnyLet: Initial?
              let childrenCommissionExpected;
              // biome-ignore lint/suspicious/noImplicitAnyLet: Initial?
              let childrenCommissionBalance;
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              row.children_reconciliation_data.forEach((child) => {
                if (
                  child.commission_amount_monthly?.[month]
                    ?.commission_amount_monthly
                )
                  childrenCommissionReceived =
                    +child.commission_amount_monthly?.[month]
                      ?.commission_amount_monthly +
                    (childrenCommissionReceived ?? 0);
                if (child.commission_expected_monthly?.[month])
                  childrenCommissionExpected =
                    +child.commission_expected_monthly?.[month] +
                    (childrenCommissionExpected ?? 0);
                if (child.commission_balance_monthly?.[month])
                  childrenCommissionBalance =
                    child.commission_balance_monthly?.[month] +
                    (childrenCommissionBalance ?? 0);
              });
              if (childrenCommissionReceived)
                commissionReceived =
                  childrenCommissionReceived + (commissionReceived ?? 0);
              if (childrenCommissionExpected)
                commissionExpected =
                  childrenCommissionExpected + (commissionExpected ?? 0);
              if (childrenCommissionBalance)
                commissionBalance =
                  childrenCommissionBalance + (commissionBalance ?? 0);
            }

            let result = Formatter.currency(commissionReceived);
            // biome-ignore lint/suspicious/noImplicitAnyLet: Initial?
            let commissionDiff;
            if (!isNill(commissionExpected) || !isNill(commissionReceived)) {
              // const effCommissionReceived = commissionReceived ?? 0;
              // const balance = commissionBalance;
              commissionDiff = (
                <div>
                  Due for this month: {Formatter.currency(commissionExpected)}
                  {commissionBalance < 0 &&
                    Math.abs(commissionBalance) > reconciliationThreshold && (
                      <>
                        <br />
                        <span>
                          Excess: {Formatter.currency(commissionBalance)}
                        </span>
                      </>
                    )}
                </div>
              );
            }
            if (
              commissionBalance < 0 &&
              Math.abs(commissionBalance) > reconciliationThreshold &&
              commissionReceived
            ) {
              result = (
                <Tooltip title={commissionDiff}>
                  <div className="whitespace-nowrap text-black text-right">
                    {Formatter.currency(commissionReceived)}*
                    <br />
                    <Typography variant="caption" className="invisible">
                      Bal: {Formatter.currency(commissionBalance ?? 0)}{' '}
                    </Typography>
                  </div>
                </Tooltip>
              );
            } else if (
              commissionBalance > 0 &&
              Math.abs(commissionBalance) > reconciliationThreshold
            ) {
              result = (
                <Tooltip title={commissionDiff}>
                  <div className="whitespace-nowrap text-black text-right">
                    {Formatter.currency(commissionReceived ?? 0)}
                    <br />
                    <Typography variant="caption" className="text-red-600">
                      Bal: {Formatter.currency(commissionBalance ?? 0)}{' '}
                    </Typography>
                  </div>
                </Tooltip>
              );
            }
            return (
              <div className="whitespace-nowrap text-black text-right">
                {result}
              </div>
            );
          },
        });
        maxMonths -= 1;
        if (maxMonths <= 0) break;
      }
    }
    return newHeaders;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    dataSpec.fields,
    searchParamsToString,
    fieldOptions?.payment_date_first,
    fieldOptions?.payment_date_last,
    options.mode,
    displayFields,
  ]);

  const columns = useMemo(() => headers.map((header) => header.id), [headers]);
  useUpdateSortingInQuery({
    order,
    orderBy,
    columns,
  });

  const dataAll = data;

  let dataFiltered = dataAll;

  // TODO: Reconcile prefilter vs suggested, xor for now
  if (prefilter instanceof Function) {
    dataFiltered = dataAll.filter(prefilter);
  }

  const bulkEditAddFields = useMemo(() => {
    const extraFields =
      (mode === 'bulkEditCsv'
        ? dataSpec.extraBulkEditCsvFields
        : dataSpec.bulkAddFields) || [];
    return [
      ...extraFields,
      ...(addGlobalCompanyConfig.fields || dataDesc.fields)
        .flat()
        .filter((field) => {
          const isReadOnly =
            typeof field.readOnly === 'function' ? false : field.readOnly;
          return (
            ([
              undefined,
              FieldTypes.SELECT,
              FieldTypes.DYNAMIC_SELECT,
              FieldTypes.BOOLEAN,
              FieldTypes.DATE,
              FieldTypes.TEXT,
              FieldTypes.CURRENCY,
              FieldTypes.CUSTOM,
            ].includes(field.type) &&
              field.access !== SystemRoles.ADMIN &&
              !isReadOnly &&
              !field.bulkAddUnsupported) ||
            field.enabledBulkAddCsv
          );
        }),
    ];
  }, [
    mode,
    dataSpec.extraBulkEditCsvFields,
    dataSpec.bulkAddFields,
    addGlobalCompanyConfig.fields,
    dataDesc,
  ]);

  useBuildFilterList({
    filters,
    fieldOptions: queryData?.fieldOptions,
  });

  const setDefaultData = useCallback((data) => {
    setNewData(data);
    setOldData(cloneDeep(data));
  }, []);

  const {
    actions,
    showDelConfirm,
    setDeleteIds,
    setShowDelConfirm,
    deleteIds,
  } = useDeleteAndCopyActions({
    actions: props.actions,
    table: dataSpec.table,
    enableRowCreateCopyAction: props.enableRowCreateCopyAction,
    enableRowDeleteAction: props.enableRowDeleteAction,
    setDefaultData,
    fields: Object.values(dataSpec.fields),
  });

  // @ts-ignore
  if (!dataSpec) return null;

  return (
    <Box
      sx={{
        width:
          options.mode === 'reconciler'
            ? 'calc((100vw - 200px)/2 - 120px)'
            : `calc(100vw - ${menuOpen ? '200px' : '0px'})`,
      }}
    >
      {/* @ts-ignore */}
      <Toolbar {...props} setNewData={setNewData} />

      {!isLoading &&
        ((mode === 'bulkAdd' && bulkAdd) ||
          (mode === 'bulkEditCsv' && enableBulkEditCsv)) && (
          <Box
            sx={{
              overflowY: 'scroll',
              height: 'calc(100vh - 168px)',
              display: 'flex',
              justifyContent: 'center',
              p: 2,
            }}
          >
            <DataBulkAdd
              fields={bulkEditAddFields}
              btnLabel={mode === 'bulkAdd' ? 'Bulk add' : 'Bulk edit'}
              isUpdate={mode === 'bulkEditCsv'}
              onCancel={() => {
                updateSearchParams({ m: null });
              }}
              onSave={async (jsonEntities) => {
                let res = null;
                if (mode === 'bulkAdd') {
                  res = await posterBulkAdd.mutateAsync(jsonEntities);
                } else {
                  res = await bulkEditCsv.mutateAsync(jsonEntities);
                }

                if (res?.total) {
                  showSnackbar(`Added ${res.total} records`, 'success');
                  updateSearchParams({ m: null });
                  setTimeout(refetch, 200);
                } else {
                  showSnackbar('Error bulk adding data', 'error');
                }
              }}
            />
          </Box>
        )}

      {!isLoading && ['add', 'edit'].includes(mode) && (
        <Box
          sx={{
            overflowY: 'scroll',
            height: 'calc(100vh - 168px)',
            display: 'flex',
            justifyContent: 'center',
            p: 2,
          }}
        >
          <DataForm
            dataDesc={dataDesc}
            fields={
              addGlobalCompanyConfig.fields ||
              Object.values(dataDesc.fields).filter((field) =>
                field.condition ? field.condition(newData) : true
              )
            }
            useNewTable={dataSpec.useNewTable}
            dynamicSelectsConfig={dataSpec.dynamicSelectsConfig}
            newData={newData}
            readOnly={readOnly}
            oldData={oldData}
            setNewData={setNewData}
            onCancel={() => {
              setNewData({});
              updateSearchParams({ m: null, id: null });
            }}
            onSave={async () => {
              // In general, don't send read-only fields. But some are necessary for identifying records to update.
              // For general forms, id and str_id, and for Views and fields and Agent settings, key, role is needed.
              const normalizedNewData = Object.fromEntries(
                Object.entries(newData)
                  .filter(
                    ([key]) =>
                      ['id', 'str_id', 'role', 'key'].includes(key) ||
                      (typeof dataDesc.fields.find((field) => field.id === key)
                        ?.readOnly === 'function'
                        ? !dataDesc.fields
                            .find((field) => field.id === key)
                            ?.readOnly(newData)
                        : !dataDesc.fields.find((field) => field.id === key)
                            ?.readOnly)
                  )
                  .map(([key, value]) => [
                    key,
                    dataDesc.fields.find((field) => field.id === key)
                      ?.normalizer
                      ? dataDesc.fields
                          .find((field) => field.id === key)
                          .normalizer(value, [])
                      : value,
                  ])
              );
              if (includeZeroCommissionParam)
                normalizedNewData.incl_zero_commissions =
                  includeZeroCommissionParam;

              // biome-ignore lint/suspicious/noImplicitAnyLet: Initial?
              let res;

              if (notUpdateFields.length > 0) {
                // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                notUpdateFields.forEach((field) => {
                  delete normalizedNewData[field];
                });
              }

              try {
                if (
                  normalizedNewData.id &&
                  dataDesc.table !== endpoint.viewsAndFields &&
                  dataDesc.table !== endpoint.agents
                ) {
                  if (reportId) normalizedNewData.comp_report_id = reportId;
                  res = await patcher.mutateAsync(normalizedNewData);
                } else {
                  res = await poster.mutateAsync(normalizedNewData);
                }
                setNewData({});
                updateSearchParams({ m: null, id: null });
              } catch (e) {
                console.error('Error encountered while saving data');
                return {
                  // @ts-ignore
                  error: e.message || 'Error encountered while saving data',
                };
              } finally {
                setTimeout(refetch, 300);
              }
              return res;
            }}
            onDelete={async () => {
              await deleter.mutate({ ids: [newData.id] });

              setNewData({});
              updateSearchParams({ m: null, id: null });
              setTimeout(refetch, 300);
            }}
            // @ts-ignore
            validateData={dataDesc.validateData}
            // @ts-ignore
            extraActions={extraFormActions}
            currentData={newData}
          />
        </Box>
      )}

      <Divider />
      {isLoading && (
        <Box sx={{ textAlign: 'center', mt: 6 }}>
          <LoadingCircle />
        </Box>
      )}
      {!isLoading &&
        !['edit', 'bulkAdd', 'add'].includes(mode) &&
        dataFiltered.length > 0 &&
        headers.length > 0 && (
          <EnhancedTable
            dense
            bulkEditFields={dataSpec.bulkEditFields}
            dynamicQueryKeys={dataSpec.dynamicQueryKeys}
            dynamicSelectsConfig={dataSpec.dynamicSelectsConfig}
            useNewTable={dataSpec.useNewTable}
            headers={headers}
            stickyColumns={dataSpec.stickyColumns}
            rows={dataFiltered}
            readOnly={readOnly}
            lockRow={lockRow}
            actions={actions}
            outstandingFieldsInMobileView={
              outstandingMobileFields?.length > 0
                ? outstandingMobileFields
                : (dataSpec.outstandingFieldsInMobileView ?? [])
            }
            actionsEnabled={actionsEnabled}
            customHeaderActions={customHeaderActions}
            enableBulkDelete={enableBulkDelete}
            onDelete={enableMultiSelect ? deleteRows : false}
            nonSelectableOnMobile={nonSelectableOnMobile}
            rowKey={rowKey}
            onBulkSync={onBulkSync}
            bulkActions={bulkActions}
            onEdit={
              enableEdit
                ? options.mode !== 'reconciler'
                  ? (row) => {
                      updateSearchParams({
                        m: 'edit',
                        id: row.str_id,
                      });
                      setNewData(row);
                      setOldData(JSON.parse(JSON.stringify(row)));
                    }
                  : undefined
                : false
            }
            onBulkEdit={async (selected, updateData) => {
              const { clearDataFields, ...rest } = updateData;
              bulkPatcher.mutate(
                // @ts-ignore
                {
                  data: {
                    ids: selected,
                    ...rest,
                  },
                  params: {
                    clearDataFields,
                  },
                },
                {
                  onError: (error) => {
                    showSnackbar(
                      error.message ||
                        'Something wrong happened, please try again!',
                      'error'
                    );
                  },
                  onSuccess: () => {
                    showSnackbar('Bulk edit successful', 'success');
                    refetch();
                  },
                }
              );
            }}
            // TODO: Should be controlled selection...hack for now
            setSelectedData={setSelectedData}
            stickyHeader
            paginated={tableConfig?.paginated ?? true}
            controlledOrdering={{
              order,
              orderBy,
              setOrder: (e) => {
                handleChangePage(e, 0);
                setOrder(e);
              },
              setOrderBy: (newOrderBy) => {
                if (!newOrderBy) {
                  console.warn(
                    'Invalid orderBy value. Falling back to default.'
                  );
                  newOrderBy = orderBy;
                }
                handleChangePage(null, 0); // Reset to the first page
                setOrderBy(newOrderBy);
              },
            }}
            // If controlledPagination is not defined, use the default pagination
            // controlledPagination null means no pagination
            controlledPagination={getControlledPagination(
              tableConfig?.controlledPagination,
              {
                count, // +(filterVals.showDupes ? dataCounts.countDupes : 0),
                page,
                onPageChange: handleChangePage,
                rowsPerPage,
                onRowsPerPageChange: handleChangeRowsPerPage,
              }
            )}
            options={{ hideSelectedCount, radio: options.radio }}
            showTotals={showTotals}
            totals={totals}
            refetch={refetch}
          />
        )}
      {!isLoading && dataFiltered.length === 0 && (
        <Box sx={{ textAlign: 'center', mt: 6, width: '100%' }}>
          {isError ? (
            <Box>
              <Typography variant="h5">
                Error retrieving data, please try again later
              </Typography>
              <Button
                variant="outlined"
                onClick={(_e) => refetch()}
                sx={{ mt: 5 }}
              >
                Retry
              </Button>
            </Box>
          ) : (
            <Typography variant="h5">No results</Typography>
          )}
        </Box>
      )}
      <DeleteConfirmDialog
        open={showDelConfirm}
        onClose={() => setShowDelConfirm(false)}
        onDelete={async () => {
          await deleter.mutateAsync({ ids: deleteIds });
          setShowDelConfirm(false);
          setDeleteIds([]);
          showSnackbar('Deleted successfully', 'success');
          refetch();
          setTimeout(() => {
            showSnackbar('Refreshing data...', 'info');
          }, 1000);
        }}
      />
    </Box>
  );
};

export default EnhancedDataView;
