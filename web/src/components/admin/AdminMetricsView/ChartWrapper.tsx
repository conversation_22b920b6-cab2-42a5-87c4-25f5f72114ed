import { MoreVert } from '@mui/icons-material';
import {
  Card,
  Card<PERSON>ontent,
  Grid,
  IconButton,
  InputLabel,
  FormControl,
  Select,
  Menu,
  MenuItem,
  type SelectChangeEvent,
  Typography,
} from '@mui/material';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import type React from 'react';
import { useState, useEffect } from 'react';

import BasicTable from '@/components/molecules/BasicTable';
import { exportCsv } from '@/services/helpers';
import Formatter from '@/services/Formatter';
import ChartWidget from '@/components/DashboardView/ChartWidget';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';

dayjs.extend(utc);

interface ChartWrapperProps {
  title?: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  data?: any;
  variants?: Variant[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  dataSet?: any[];
}

interface Variant {
  title: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  chart: any[];
}

const ChartWrapper = ({
  title,
  data: _data,
  variants,
  dataSet: _dataSet,
}: ChartWrapperProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [view, setView] = useState('chart');
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [selectVal, setSelectVal] = useState<any>({});
  const [currTitle, setCurrTitle] = useState<string>('');
  const [currVariants, setCurrVariants] = useState<Variant[] | undefined>(
    variants
  );
  const [options, setOptions] = useState<string[]>([]);
  const [filterType, setFilterType] = useState('all');
  const [filterCount, setFilterCount] = useState('20');
  const [variantTitle, setVariantTitle] = useState<string>('');

  // Map chart title to variants for each chart
  const [variantsMap, setVariantsMap] = useState<
    Record<string, Variant[] | undefined>
  >({});

  const isDocumentRelatedTitle = (title: string) => {
    return (
      title === 'Document types' ||
      title === 'Document import trends' ||
      title === 'Document types import trends'
    );
  };

  // Extracted useEffect for dataset/variants logic
  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    if (_dataSet) {
      const newOptions: string[] = [];
      const newVariantsMap: Record<string, Variant[] | undefined> = {};
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      _dataSet.forEach((eachData) => {
        const _title = eachData?.title;
        if (_title) {
          newOptions.push(_title);
          newVariantsMap[_title] = eachData?.variants;
        }
      });

      setOptions(newOptions);
      setVariantsMap(newVariantsMap);

      if (newOptions.length > 0) {
        const curr = !currTitle ? newOptions[0] : currTitle;
        setCurrVariants(newVariantsMap[curr]);
        setSelectVal(
          _dataSet.find((eachData) => eachData.title === curr)?.value || []
        );
        setCurrTitle(curr);
        setVariantTitle('Default'); // Always set to Default
      }
    } else if (_data && title) {
      setSelectVal(_data);
      setCurrTitle(title);
      setCurrVariants(variants);
      setVariantTitle('Default');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [_data, _dataSet, title, variants]);

  // Handle chart selection
  const handleDataSet = (selectedTitle: string) => {
    const selectedData = _dataSet?.find(
      (eachData) => eachData.title === selectedTitle
    );
    setSelectVal(selectedData?.value);
    setCurrTitle(selectedTitle);
    setCurrVariants(variantsMap[selectedTitle]);
    setVariantTitle('');
  };

  // Handle variant selection
  const handleVariantChange = (e: SelectChangeEvent<string>) => {
    const selectedVariantTitle = e.target.value as string;
    setVariantTitle(selectedVariantTitle);
    if (selectedVariantTitle === 'Default') {
      // Reset to default chart data
      const selectedData = _dataSet?.find(
        (eachData) => eachData.title === currTitle
      );
      setSelectVal(selectedData?.value || {});
      return;
    }
    const selectedVariant = currVariants?.find(
      (v) => v.title === selectedVariantTitle
    );
    if (selectedVariant) {
      setSelectVal({
        ...selectVal,
        variantTitle: selectedVariant.title,
        ...(selectedVariant.chart ? { ...selectedVariant.chart } : {}),
      });
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const showFilter =
    currTitle?.includes('processors') || currTitle?.includes('profiles');

  // Get data
  let processedData = { ...selectVal };
  if (showFilter && filterType !== 'all' && selectVal?.series?.[0]?.data) {
    const sortedData = [...selectVal.series[0].data].sort(
      (a, b) => b.val - a.val
    );
    const count = parseInt(filterCount);
    processedData = {
      ...selectVal,
      series: [
        {
          ...selectVal.series[0],
          data:
            filterType === 'top'
              ? sortedData.slice(0, count)
              : sortedData.slice(-count),
        },
      ],
    };
  }

  const headers = Object.keys(processedData?.series?.[0]?.data?.[0] || {});
  headers[0] = selectVal?.xAxis?.type || headers[0];

  if (
    (isDocumentRelatedTitle(currTitle) ||
      currTitle === 'Company documents count') &&
    selectVal?.series
  ) {
    headers.splice(1, 1);
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    selectVal.series.forEach((series) => {
      headers.push(series.name);
    });
  } else {
    headers[1] = selectVal?.series?.[0]?.name || headers[1];
  }

  let data = processedData?.series?.[0]?.data?.map((row) => {
    return Object.values(row);
  });
  let exportData: [string, ...(number | string)[]][] = data;

  if (selectVal?.xAxis?.type === 'time') {
    exportData = [];

    if (isDocumentRelatedTitle(currTitle)) {
      const timeMap = new Map();

      processedData.series.forEach((series, index) => {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        series.data?.forEach(([time, value]) => {
          if (!timeMap.has(time)) {
            timeMap.set(time, new Array(processedData.series.length).fill(0));
          }
          timeMap.get(time)[index] = value;
        });
      });

      data = Array.from(timeMap.entries())
        .map(([time, values]) => {
          const date = dayjs.utc(time).format('YYYY-MM-DD');
          exportData.push([date, ...values]);
          return [date, ...values];
        })
        .sort((a, b) => dayjs(a[0]).valueOf() - dayjs(b[0]).valueOf());
    } else {
      data = data.map(([k, v]) => {
        let date = dayjs.utc(k).format('YYYY-MM-DD');
        // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        let value;
        date = dayjs(date).format('YYYY-MM-DD');
        exportData.push([date, v]);

        switch (currTitle) {
          case 'Time to import':
            value = Formatter.highlightChip(v, 10, 100);
            break;
          case 'Processing time':
            value = Formatter.highlightChip(v, 1, 5);
            break;
          case 'Records':
            value = Formatter.highlightChip(v, 30, 300);
            break;
          case 'Records per minutes':
            value = Formatter.highlightChip(v, 0, 5);
            break;
          case 'Total actions':
            value = Formatter.highlightChip(v, 5, 30);
            break;
          case 'Add data':
            value = Formatter.highlightChip(v, 1, 5);
            break;
          case 'Edit data':
            value = Formatter.highlightChip(v, 2, 20);
            break;
          case 'Delete data':
            value = Formatter.highlightChip(v, 2, 20);
            break;
          case 'Edit mapping':
            value = Formatter.highlightChip(v, 2, 20);
            break;
          case 'Select method':
            value = Formatter.highlightChip(v, 1, 2);
            break;
          case 'Select mapping':
            value = Formatter.highlightChip(v, 1, 2);
            break;
          case 'Select processor':
            value = Formatter.highlightChip(v, 1, 2);
            break;
          default:
            value = v;
            break;
        }
        return [date, value];
      });
    }
  } else if (currTitle === 'Company documents count') {
    data = [];
    exportData = [];

    if (
      processedData?.series &&
      Array.isArray(processedData.series) &&
      processedData.series.length > 0
    ) {
      const categoryMap = new Map();
      const categories = processedData.xAxis?.data || [];

      processedData.series.forEach((series, index) => {
        if (series?.data && Array.isArray(series.data)) {
          series.data.forEach((value, dataIndex) => {
            const company = categories[dataIndex];
            if (company) {
              if (!categoryMap.has(company)) {
                categoryMap.set(
                  company,
                  new Array(processedData.series.length).fill(0)
                );
              }
              categoryMap.get(company)[index] = value;
            }
          });
        }
      });

      if (categoryMap.size > 0) {
        data = Array.from(categoryMap.entries()).map(([company, values]) => {
          return [company, ...values];
        });
        exportData = data.map((row) => row as [string, ...(string | number)[]]);
      }
    }
  }

  return (
    <Card>
      <CardContent sx={{ pb: 0 }}>
        <Grid
          container
          justifyContent="space-between"
          alignItems="center"
          style={{ borderBottom: '20px solid black' }}
        >
          <Grid item style={{ flex: 1 }}>
            <Typography gutterBottom variant="body1">
              {currTitle}
            </Typography>
          </Grid>
          <Grid
            item
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '16px',
            }}
          >
            {showFilter && (
              <>
                <FormControl>
                  <Select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value)}
                    variant="outlined"
                    sx={{
                      ml: 3,
                      width: 120,
                      height: 32,
                      fontSize: '0.9rem',
                      '& .MuiSelect-icon': {
                        color: 'primary.main',
                      },
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                        borderRadius: 0.5,
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.dark',
                      },
                    }}
                  >
                    <MenuItem value="all">All items</MenuItem>
                    <MenuItem value="top">Top items</MenuItem>
                    <MenuItem value="bottom">Bottom items</MenuItem>
                  </Select>
                </FormControl>
                {filterType !== 'all' && (
                  <FormControl>
                    <Select
                      value={filterCount}
                      onChange={(e) => setFilterCount(e.target.value)}
                      variant="outlined"
                      sx={{
                        width: 100,
                        height: 32,
                        fontSize: '0.9rem',
                        '& .MuiSelect-icon': {
                          color: 'primary.main',
                        },
                        '& .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'primary.main',
                          borderRadius: 0.5,
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'primary.dark',
                        },
                      }}
                    >
                      {[5, 10, 20, 50].map((num) => (
                        <MenuItem key={num} value={num.toString()}>
                          {filterType === 'top'
                            ? `Top ${num}`
                            : `Bottom ${num}`}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              </>
            )}
            <div style={{ flexGrow: 1 }} />
            {options.length > 0 && (
              <FormControl>
                <EnhancedSelect
                  options={options}
                  label="Select data"
                  value={currTitle}
                  onChange={(option) => {
                    handleDataSet(option);
                  }}
                />
              </FormControl>
            )}
            <FormControl
              sx={{ minWidth: 150, ml: 2 }}
              disabled={!currVariants || currVariants.length === 0}
            >
              <InputLabel id="variant-select-label">Variant</InputLabel>
              <Select
                labelId="variant-select-label"
                value={
                  currVariants && currVariants.length > 0
                    ? variantTitle || 'Default'
                    : 'Default'
                }
                label="Variant"
                onChange={handleVariantChange}
                variant="outlined"
                sx={{
                  width: 170,
                  height: 32,
                  fontSize: '0.9rem',
                  '& .MuiSelect-icon': {
                    color: 'primary.main',
                  },
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.main',
                    borderRadius: 0.5,
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.dark',
                  },
                }}
                displayEmpty
                renderValue={(selected) => selected || 'Default'}
              >
                <MenuItem value="Default">Default</MenuItem>
                {currVariants &&
                  currVariants.length > 0 &&
                  currVariants.map((variant) => (
                    <MenuItem key={variant.title} value={variant.title}>
                      {variant.title}
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>
            <IconButton onClick={handleMenuClick} sx={{ p: 0 }}>
              <MoreVert sx={{ p: 0 }} />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem
                key="view-toggle"
                onClick={() => {
                  setView(view === 'table' ? 'chart' : 'table');
                }}
              >
                {view === 'table' ? 'Chart view' : 'Table view'}
              </MenuItem>
              <MenuItem
                key="export"
                onClick={() => {
                  exportCsv(headers, exportData, 'Fintary-Metrics-Export.csv');
                }}
              >
                Export
              </MenuItem>
            </Menu>
          </Grid>
        </Grid>
        {view === 'chart' && <ChartWidget data={processedData} />}
        {view === 'table' && (
          <BasicTable headers={headers} rows={data} formatters={{}} />
        )}
      </CardContent>
    </Card>
  );
};

export default ChartWrapper;
