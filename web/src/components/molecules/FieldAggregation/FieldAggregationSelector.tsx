import { useMemo, useState } from 'react';
import { Icon<PERSON>utton, Menu, MenuItem, Box, Tooltip, Chip } from '@mui/material';
import CalculateOutlined from '@mui/icons-material/CalculateOutlined';
import PriceCheckOutlined from '@mui/icons-material/PriceCheckOutlined';
import ClearIcon from '@mui/icons-material/Clear';
import {
  DEFAULT_FIELD_VALUE,
  AggregationMethodTypes,
  AggregationMethodTypeLabels,
  ResultFormatterTypeLabels,
  ResultFormatterMethod,
} from 'common/dto/widgets';

import { EnhancedSelect } from '../EnhancedSelect';

const FieldAggregationSelector = ({
  fields,
  selectedField,
  onRemove,
  onUpdate,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [anchorElFormatter, setAnchorElFormatter] = useState(null);
  const selectedAggregation = selectedField.aggregation_method || '';
  const selectedFieldValue = selectedField.field || '';
  const selectedFieldFormatter = selectedField.formatter || '';

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleFormatterMenuOpen = (event) => {
    setAnchorElFormatter(event.currentTarget);
  };

  const handleFormatterMenuClose = () => {
    setAnchorElFormatter(null);
  };

  const handleAggregationChange = (method) => {
    onUpdate({ ...selectedField, aggregation_method: method });
    handleMenuClose();
  };

  const handleFormatterChange = (method) => {
    onUpdate({ ...selectedField, formatter: method });
    handleFormatterMenuClose();
  };

  const fieldsSelection = useMemo(() => {
    const anyField = {
      name: DEFAULT_FIELD_VALUE,
      displayName: 'Any',
      defaultFormatter: ResultFormatterMethod.NUMBER,
    };
    return [anyField, ...fields];
  }, [fields]);

  const handleFieldChange = (_event, value) => {
    const dataUpdate = { ...selectedField, field: value };

    const selectedFieldData = fieldsSelection.find(
      (field) => field.name === value
    );

    if (value === DEFAULT_FIELD_VALUE) {
      if (
        selectedAggregation !== AggregationMethodTypes.COUNT &&
        selectedAggregation !== AggregationMethodTypes.COUNT_ACCUMULATE
      ) {
        // The 'any' field should use 'Count' or 'CountAccumulate' as the aggregation method.
        dataUpdate.aggregation_method = AggregationMethodTypes.COUNT;
      }
    }

    if (selectedFieldData?.defaultFormatter) {
      dataUpdate.formatter = selectedFieldData.defaultFormatter;
    }

    onUpdate(dataUpdate);
  };

  const options = fieldsSelection.map((field) => ({
    id: field.name,
    label: field.displayName || field.name,
  }));

  const getFieldValue = () => {
    console.log('getFieldValue called', selectedAggregation);
    return (
      fieldsSelection.find((field) => field.name === selectedFieldValue)
        ?.displayName || selectedFieldValue
    );
  };

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}
      className="mt-3"
    >
      <Box data-name="field-aggregator-field" className="w-1/2">
        <EnhancedSelect
          label="Field"
          options={options}
          value={{
            id: selectedFieldValue,
            label: getFieldValue(),
          }}
          onChange={(value) => {
            handleFieldChange(null, value.id);
          }}
          sx={{ width: '100%' }}
        />
      </Box>

      <Box ml={1} display={'flex'} alignItems={'center'}>
        {selectedAggregation ? (
          <Chip
            sx={{ marginRight: 1 }}
            data-name="field-aggregator-aggregation"
            onClick={handleMenuOpen}
            label={AggregationMethodTypeLabels[selectedAggregation]}
            avatar={<CalculateOutlined />}
          />
        ) : (
          <Tooltip title="Aggregation method">
            <IconButton onClick={handleMenuOpen}>
              <CalculateOutlined />
            </IconButton>
          </Tooltip>
        )}

        {selectedFieldFormatter ? (
          <Chip
            data-name="field-aggregator-formatter"
            label={ResultFormatterTypeLabels[selectedFieldFormatter]}
            avatar={<PriceCheckOutlined />}
            onClick={handleFormatterMenuOpen}
          />
        ) : (
          <Tooltip title="Result formatter">
            <IconButton onClick={handleFormatterMenuOpen}>
              <PriceCheckOutlined />
            </IconButton>
          </Tooltip>
        )}

        <IconButton onClick={onRemove}>
          <ClearIcon />
        </IconButton>
      </Box>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {selectedFieldValue && selectedFieldValue !== 'any' && (
          <>
            <MenuItem
              onClick={() =>
                handleAggregationChange(AggregationMethodTypes.SUM)
              }
            >
              Sum
            </MenuItem>
            <MenuItem
              onClick={() =>
                handleAggregationChange(AggregationMethodTypes.SUM_ACCUMULATE)
              }
            >
              Running sum
            </MenuItem>
            {selectedFieldValue && selectedFieldValue.type === 'obj' && (
              <MenuItem
                onClick={() =>
                  handleAggregationChange(AggregationMethodTypes.AGGREGATE)
                }
              >
                Aggregate
              </MenuItem>
            )}
            <MenuItem
              onClick={() =>
                handleAggregationChange(AggregationMethodTypes.AVERAGE)
              }
            >
              Average
            </MenuItem>
          </>
        )}
        <MenuItem
          onClick={() => handleAggregationChange(AggregationMethodTypes.COUNT)}
        >
          Count
        </MenuItem>
        <MenuItem
          onClick={() =>
            handleAggregationChange(AggregationMethodTypes.COUNT_ACCUMULATE)
          }
        >
          Running count
        </MenuItem>
      </Menu>
      <Menu
        anchorEl={anchorElFormatter}
        open={Boolean(anchorElFormatter)}
        onClose={handleFormatterMenuClose}
      >
        {selectedFieldValue && selectedFieldValue !== DEFAULT_FIELD_VALUE && (
          <>
            <MenuItem
              onClick={() =>
                handleFormatterChange(ResultFormatterMethod.CURRENCY)
              }
            >
              Currency
            </MenuItem>
            <MenuItem
              onClick={() =>
                handleFormatterChange(ResultFormatterMethod.PERCENTAGE)
              }
            >
              Percentage
            </MenuItem>
          </>
        )}
        <MenuItem
          onClick={() => handleFormatterChange(ResultFormatterMethod.NUMBER)}
        >
          Number
        </MenuItem>
      </Menu>
    </div>
  );
};

export default FieldAggregationSelector;
