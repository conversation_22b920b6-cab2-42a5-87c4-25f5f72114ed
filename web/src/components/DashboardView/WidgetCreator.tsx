import {
  Bar<PERSON>hartOutlined,
  DonutLargeOutlined,
  ExpandMore,
  HelpOutline,
  Inventory2Outlined,
  ListAlt as ListAltIcon,
  PeopleOutlined,
  PersonOutline,
  ReceiptOutlined,
  RequestQuoteOutlined,
  ShowChartOutlined,
  SupportAgentOutlined,
  TableChartOutlined,
} from '@mui/icons-material';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Checkbox,
  Chip,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  Skeleton,
  Switch,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import CodeMirror from '@uiw/react-codemirror';
import { WidgetGroup } from 'common/constants';
import {
  CommissionsFilterByDateFieldLabels,
  CommissionsFilterByDateFields,
  DEFAULT_FIELD_VALUE,
  DEFAULT_SORTING_FIELD_VALUE,
  filterFieldsByDataSource,
  PoliciesFilterByDateFieldLabels,
  PoliciesFilterByDateFields,
  WidgetDataSourceLabels,
  WidgetDataSources,
  WidgetTypeLabels,
  WidgetTypes,
} from 'common/dto/widgets';
import { SystemRoles } from 'common/globalTypes';
import {
  isZodError,
  parseZodError,
  type ZodResponse,
} from 'common/helpers/zod';
import React, { useContext, useEffect, useState } from 'react';

import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import FieldAggregationManager from '@/components/molecules/FieldAggregation/FieldAggregationManager';
import FieldMatcher from '@/components/molecules/FieldMatcher';
import FieldsCalculatorExpressionBuilder from '@/components/molecules/FieldsCalculatorExpressionBuilder';
import { UIStateContext } from '@/contexts/UIStateProvider';
import useSnackbar from '@/contexts/useSnackbar';
import API from '@/services/API';
import { AccountAccessLevels } from '@/types';
import BoxWidget from './BoxWidget';
import ChartWidget from './ChartWidget';
import TableWidget from './TableWidget';
import WidgetWrapper from './WidgetWrapper';

const dataSourceIcon: Record<WidgetDataSources, React.ReactNode> = {
  commissions: React.createElement(RequestQuoteOutlined),
  policies: React.createElement(ReceiptOutlined),
  agentPayouts: React.createElement(SupportAgentOutlined),
  contacts: React.createElement(PersonOutline),
  customers: React.createElement(PeopleOutlined),
};
const toggleStyle = {
  width: 200,
  display: 'flex',
  justifyContent: 'space-between',
};

const widgetGroupsIcon = {
  box: <Inventory2Outlined />,
  'chart-donut': <DonutLargeOutlined />,
  'chart-line': <ShowChartOutlined />,
  'chart-bar': <BarChartOutlined />,
  table: <TableChartOutlined />,
};

interface WidgetFormErrors {
  widgetName?: string;
  dataSource?: string;
  widgetType?: string;
  dataConfiguration?: string;
  dateFilter?: string;
}

const WidgetCreator = ({
  widgetOnEdit,
  createWidget,
  closeAddWidgetDialog,
  saveChange,
  setWidgetModel,
  isEditingMode,
}) => {
  const { data: _fieldsData } = API.getBasicQuery(`fields`);
  const [errors, setErrors] = useState<WidgetFormErrors>({});
  const [isLoading, setIsLoading] = useState(false);

  if (_fieldsData) {
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    _fieldsData.forEach((field) => {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      Object.keys(filterFieldsByDataSource).forEach((dataSourceKey) => {
        const dataSource = filterFieldsByDataSource[dataSourceKey];

        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        dataSource.groupFields.forEach((groupField) => {
          if (groupField.name === field.key) {
            groupField.fieldMatcherType = field.type;
          }
        });

        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        dataSource.dataFields.forEach((dataField) => {
          if (dataField.name === field.key) {
            dataField.fieldMatcherType = field.type;
          }
        });
      });
    });
  }

  const addSelector = () => {
    setDataFieldsAggregators([
      ...dataFieldsAggregators,
      {
        id: dataFieldsAggregators.length + 1,
        field: '',
        aggregation_method: '',
      },
    ]);
  };

  useEffect(() => {
    if (widgetOnEdit) {
      setSelectedDataSource(widgetOnEdit.dataSource || '');
      setSelectedCustomCode(widgetOnEdit.customCode || '');
      setSelectedColumnValue(widgetOnEdit.column || '');
      setSelectedSortingField(widgetOnEdit.sortingField || null);
      setColumnLimit(widgetOnEdit.columnLimit || null);
      setSelectedWidgetType(widgetOnEdit.type || '');
      setSelectedGroupByValue(widgetOnEdit.groupBy || '');
      setSelectedTimePeriodValue(widgetOnEdit.timePeriod || 'month');
      setSimilarityGroupBy(widgetOnEdit.similarityGroupBy || false);
      setDataFieldsAggregators(
        widgetOnEdit.aggregationSelectors || [
          {
            field: DEFAULT_FIELD_VALUE,
            aggregation_method: 'Count',
            formatter: 'number',
          },
        ]
      );
      setFilterByDate(widgetOnEdit.filterByDate || '');
      setDataFieldsExpression(widgetOnEdit.dataFieldsExpression || null);
      setFilters(widgetOnEdit.filters || []);
      setWidgetName(widgetOnEdit.name || '');
    } else {
      setSelectedDataSource('');
      setSelectedCustomCode('');
      setSelectedColumnValue('');
      setSelectedSortingField(null);
      setColumnLimit(null);
      setSelectedWidgetType('');
      setSelectedGroupByValue('');
      setSelectedTimePeriodValue('month');
      setSimilarityGroupBy(false);
      setFilterByDate('');
      setDataFieldsAggregators([
        {
          field: DEFAULT_FIELD_VALUE,
          aggregation_method: 'Count',
          formatter: 'number',
        },
      ]);
      setDataFieldsExpression(null);
      setFilters([]);
      setWidgetName('');
    }
  }, [widgetOnEdit]);

  const removeSelector = (id) => {
    setDataFieldsAggregators(
      dataFieldsAggregators.filter((selector) => selector.id !== id)
    );
  };

  const updateSelector = (id, updatedField) => {
    setDataFieldsAggregators(
      dataFieldsAggregators.map((selector) =>
        selector.id === id ? { ...selector, ...updatedField } : selector
      )
    );
  };

  const [selectedAccessLevel, setSelectedAccessLevel] = useState<boolean>(
    widgetOnEdit?.accessLevel !== 'global'
  );
  const [selectedDataSource, setSelectedDataSource] = useState<
    WidgetDataSources | ''
  >(widgetOnEdit?.dataSource);
  const [enableCustomCode, setEnableCustomCode] = useState(false);
  const [selectedCustomCode, setSelectedCustomCode] = useState(
    widgetOnEdit?.customCode
  );
  const [selectedWidgetType, setSelectedWidgetType] = useState(
    widgetOnEdit?.type
  );
  const [selectedGroupByValue, setSelectedGroupByValue] = useState(
    widgetOnEdit?.groupBy
  );
  const [selectedTimePeriodValue, setSelectedTimePeriodValue] =
    useState<string>(widgetOnEdit?.timePeriod || 'month');
  const [filterByDate, setFilterByDate] = useState<string>(
    widgetOnEdit?.filterByDate
  );
  const [selectedColumnValue, setSelectedColumnValue] = useState(
    widgetOnEdit?.column
  );
  const [selectedSortingField, setSelectedSortingField] = useState(
    widgetOnEdit?.sortingField ?? DEFAULT_SORTING_FIELD_VALUE
  );
  const [columnLimit, setColumnLimit] = useState(widgetOnEdit?.columnLimit);

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [dataFieldsAggregators, setDataFieldsAggregators] = useState<any>(
    widgetOnEdit?.aggregationSelectors || [
      {
        field: DEFAULT_FIELD_VALUE,
        aggregation_method: 'Count',
        formatter: 'number',
      },
    ]
  );
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [dataFieldsExpression, setDataFieldsExpression] = useState<any>(
    widgetOnEdit?.dataFieldsExpression || null
  );
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [filters, setFilters] = useState<any>([
    ...(widgetOnEdit?.filters ?? []),
  ]);
  const { showSnackbar } = useSnackbar();

  const [similarityGroupBy, setSimilarityGroupBy] = useState(false);

  const [widgetName, setWidgetName] = useState(widgetOnEdit?.name || '');
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [previewData, setPreviewData] = useState<any>(null);
  const {
    role: [role],
  } = useContext(UIStateContext);
  const isFintaryAdmin = role === SystemRoles.ADMIN;

  // Function to reset states when data source changes
  const resetStatesOnDataSourceChange = () => {
    setSelectedGroupByValue('');
    setSelectedTimePeriodValue('month');
    setSimilarityGroupBy(false);
    setSelectedColumnValue('');
    setSelectedSortingField(null);
    setColumnLimit(null);
    setDataFieldsAggregators([
      {
        field: DEFAULT_FIELD_VALUE,
        aggregation_method: 'Count',
        formatter: 'number',
      },
    ]);
    setDataFieldsExpression(null);
    setFilters([]);
    setFilterByDate('');
  };

  const validateForm = () => {
    const newErrors: WidgetFormErrors = {};

    if (!widgetName) {
      newErrors.widgetName = 'Widget name is required';
    }

    if (!selectedDataSource) {
      newErrors.dataSource = 'Data source is required';
    }

    if (!selectedWidgetType) {
      newErrors.widgetType = 'Widget type is required';
    }

    if (
      !filterByDate &&
      ['policies', 'commissions'].includes(selectedDataSource)
    ) {
      newErrors.dateFilter = 'Date filter is required';
    }

    if (
      !selectedGroupByValue &&
      dataFieldsAggregators.length === 0 &&
      !selectedCustomCode &&
      !filters.length
    ) {
      newErrors.dataConfiguration =
        'At least one data configuration is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreateWidget = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const uniqueKey = filterFieldsByDataSource[
        selectedDataSource
      ]?.groupFields.find((f) => f.name === selectedGroupByValue)?.uniqueKey;
      const result = await createWidget({
        name: widgetName,
        access: selectedAccessLevel
          ? AccountAccessLevels.GLOBAL
          : AccountAccessLevels.ACCOUNT,
        dataSource: selectedDataSource,
        filterByDate: filterByDate,
        type: selectedWidgetType,
        groupBy: selectedGroupByValue,
        // Add uniqueKey to avoid duplicate names for companies or contacts.
        uniqueKey,
        similarityGroupBy: similarityGroupBy,
        filters: filters,
        timePeriod: selectedTimePeriodValue,
        column: selectedColumnValue,
        sortingField: selectedSortingField,
        columnLimit: columnLimit,
        aggregationSelectors: dataFieldsAggregators,
        dataFieldsExpression: dataFieldsExpression,
        customCode: selectedCustomCode,
      });
      setPreviewData(result);
      setWidgetModel({
        id: result.id,
        access: selectedAccessLevel
          ? AccountAccessLevels.GLOBAL
          : AccountAccessLevels.ACCOUNT,
        name: widgetName,
        displayName: result.displayName,
        widgetGroup: selectedWidgetType?.toUpperCase(),
        value: result.value,
        type: selectedWidgetType,
        spec: {
          dataSource: selectedDataSource,
          filterByDate: filterByDate,
          name: widgetName,
          type: selectedWidgetType,
          groupBy: selectedGroupByValue,
          timePeriod: selectedTimePeriodValue,
          uniqueKey,
          similarityGroupBy: similarityGroupBy,
          column: selectedColumnValue,
          sortingField: selectedSortingField,
          columnLimit: columnLimit,
          filters: filters,
          customCode: selectedCustomCode,
          aggregationSelectors: dataFieldsAggregators,
          dataFieldsExpression: dataFieldsExpression,
        },
      });
    } catch (error) {
      if (isZodError((error as { error?: unknown }).error)) {
        showSnackbar(
          `Issue with widget definition: ${parseZodError(error as ZodResponse)}`,
          'error'
        );
      } else {
        const errorMessage =
          (error as Error)?.message || 'An unexpected error occurred.';
        showSnackbar(`Error creating widget: ${errorMessage}`, 'error');
      }
      setPreviewData(null);
    } finally {
      setIsLoading(false);
    }
  };

  const selectWidget = (data) => {
    switch (data.widgetGroup) {
      case WidgetGroup.BOX:
        return BoxWidget(data);
      case WidgetGroup.TABLE:
        return TableWidget(data);
      case WidgetGroup.CHART:
        return <ChartWidget data={data.value} />;
      default:
        return null;
    }
  };

  const commonInputStyle = {
    width: '100%',
    '& .MuiInputBase-root': {
      height: '40px !important',
    },
  };

  const renderWidget = (data) => (
    <WidgetWrapper displayName={data.displayName} isPreview={true}>
      {selectWidget(data)}
    </WidgetWrapper>
  );

  const renderLoadingSkeleton = () => {
    return (
      <Box
        id="shimmer-widget-prevew-loading"
        sx={{ width: '100%', height: '100%', p: 2 }}
      >
        <Box sx={{ mb: 2 }}>
          <Skeleton variant="text" width="60%" height={24} />
        </Box>
        <Box
          sx={{
            display: 'flex',
            height: 'calc(100% - 40px)',
            alignItems: 'flex-end',
            gap: 2,
          }}
        >
          {[...Array(5)].map((_, i) => (
            <Skeleton
              // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              key={i}
              variant="rectangular"
              width="15%"
              height={`${Math.random() * 70 + 20}%`}
              sx={{ borderRadius: '4px 4px 0 0' }}
            />
          ))}
        </Box>
      </Box>
    );
  };

  const getFilterByDateOptions = () => {
    const noneOption = { id: 'none', label: 'None' };

    if (selectedDataSource === WidgetDataSources.COMMISSIONS) {
      return [
        ...Object.values(CommissionsFilterByDateFields).map((value) => ({
          id: value,
          label: CommissionsFilterByDateFieldLabels[value],
        })),
      ];
    }

    if (selectedDataSource === WidgetDataSources.POLICIES) {
      return [
        ...Object.values(PoliciesFilterByDateFields).map((value) => ({
          id: value,
          label: PoliciesFilterByDateFieldLabels[value],
        })),
      ];
    }

    return [noneOption];
  };

  const filterByDateOptions = getFilterByDateOptions();

  return (
    <Box sx={{ padding: 2 }}>
      <Typography variant="h6">
        {isEditingMode ? 'Edit widget' : 'Create a new widget'}
      </Typography>

      <FormControl fullWidth margin="normal">
        <TextField
          id="widget-name"
          sx={{ flex: 1 }}
          value={widgetName}
          label="Widget name"
          error={!!errors.widgetName}
          helperText={errors.widgetName}
          onChange={(e) => {
            setWidgetName(e.target.value);
            if (errors.widgetName) {
              setErrors({ ...errors, widgetName: undefined });
            }
          }}
        />
      </FormControl>
      <FormControl fullWidth margin="normal" id="data-source-select">
        <EnhancedSelect
          label="Data source"
          sx={commonInputStyle}
          options={Object.keys(filterFieldsByDataSource).map((source) => ({
            id: source,
            label: (
              <div className="flex items-center gap-2">
                {dataSourceIcon[source]}
                <span>{WidgetDataSourceLabels[source]}</span>
              </div>
            ),
          }))}
          value={{
            id: selectedDataSource,
            label: WidgetDataSourceLabels[selectedDataSource] || '',
          }}
          onChange={(value) => {
            const prevDataSource = selectedDataSource;
            setSelectedDataSource(value.id as WidgetDataSources);

            // Reset states if data source changes
            if (prevDataSource !== value.id) {
              resetStatesOnDataSourceChange();
            }

            if (errors.dataSource) {
              setErrors({ ...errors, dataSource: undefined });
            }
          }}
        />
        {errors.dataSource && (
          <FormHelperText error>{errors.dataSource}</FormHelperText>
        )}
      </FormControl>
      {['policies', 'commissions'].includes(selectedDataSource) && (
        <FormControl fullWidth margin="normal">
          <EnhancedSelect
            label="Filter by date"
            sx={commonInputStyle}
            options={filterByDateOptions}
            value={filterByDateOptions.find((item) => item.id === filterByDate)}
            onChange={(value) => {
              setFilterByDate(value.id);

              if (errors.dateFilter) {
                setErrors({ ...errors, dateFilter: undefined });
              }
            }}
          />
          {errors.dateFilter && (
            <FormHelperText error>{errors.dateFilter}</FormHelperText>
          )}
        </FormControl>
      )}
      <FormControl fullWidth margin="normal" id="widget-type-select">
        <EnhancedSelect
          label="Widget type"
          sx={commonInputStyle}
          options={Object.values(WidgetTypes).map((label) => ({
            id: label,
            label: (
              <div className="flex items-center gap-2">
                {widgetGroupsIcon[label]}
                <span>{WidgetTypeLabels[label]}</span>
              </div>
            ),
          }))}
          value={{
            id: selectedWidgetType,
            label: WidgetTypeLabels[selectedWidgetType],
          }}
          onChange={(value) => {
            setSelectedWidgetType(value.id);
            if (errors.widgetType) {
              setErrors({ ...errors, widgetType: undefined });
            }
          }}
        />
        {errors.widgetType && (
          <FormHelperText error>{errors.widgetType}</FormHelperText>
        )}
      </FormControl>
      {errors.dataConfiguration && (
        <Typography color="error" sx={{ mt: 1, mb: 1 }}>
          {errors.dataConfiguration}
        </Typography>
      )}
      {isFintaryAdmin && enableCustomCode && (
        <FormControl fullWidth margin="normal">
          <Accordion>
            <AccordionSummary
              expandIcon={<ExpandMore />}
              aria-controls="panel2-content"
              id="panel2-header"
            >
              {/** biome-ignore lint/a11y/noLabelWithoutControl: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX-- */}
              <label>Custom code</label>
            </AccordionSummary>
            <AccordionDetails>
              <CodeMirror
                title="Custom code"
                height="350px"
                value={selectedCustomCode}
                width="100%"
                onChange={(code) => {
                  setSelectedCustomCode(code);
                }}
              />
            </AccordionDetails>
          </Accordion>
        </FormControl>
      )}

      {selectedWidgetType !== 'box' && (
        <FormControl fullWidth margin="normal" id="group-by-select">
          <Box sx={{ display: 'flex' }}>
            <EnhancedSelect
              label="Group by field"
              sx={commonInputStyle}
              options={
                filterFieldsByDataSource[selectedDataSource]?.groupFields.map(
                  (field) => ({
                    id: field.name,
                    label: field.displayName,
                  })
                ) || []
              }
              value={{
                id: selectedGroupByValue,
                label:
                  filterFieldsByDataSource[
                    selectedDataSource
                  ]?.groupFields.find((f) => f.name === selectedGroupByValue)
                    ?.displayName || '',
              }}
              onChange={(value) => {
                setSelectedGroupByValue(value.id);
              }}
            />

            <Box className="ml-1 flex flex-col items-center">
              <Box className="text-[12px] whitespace-nowrap text-[#00000099] font-[Roboto]">
                Similarity group by
              </Box>
              <Checkbox
                className="p-0"
                checked={similarityGroupBy}
                onChange={() => setSimilarityGroupBy(!similarityGroupBy)}
              />
            </Box>
          </Box>
        </FormControl>
      )}
      {selectedGroupByValue?.includes('date') && (
        <FormControl fullWidth margin="normal">
          <EnhancedSelect
            label="Time period"
            sx={commonInputStyle}
            options={[
              { id: 'day', label: 'Day' },
              { id: 'week', label: 'Week' },
              { id: 'month', label: 'Month' },
            ]}
            value={{
              id: selectedTimePeriodValue,
              label:
                selectedTimePeriodValue.charAt(0).toUpperCase() +
                selectedTimePeriodValue.slice(1),
            }}
            onChange={(value) => {
              setSelectedTimePeriodValue(value.id);
            }}
          />
        </FormControl>
      )}
      {[WidgetTypes.CHART_BAR, WidgetTypes.TABLE].includes(
        selectedWidgetType
      ) && (
        <FormControl fullWidth margin="normal">
          <Box>
            <EnhancedSelect
              label="Columns"
              sx={{ marginRight: 1 }}
              options={
                filterFieldsByDataSource[selectedDataSource]?.groupFields.map(
                  (field) => ({
                    id: field.name,
                    label: field.displayName,
                  })
                ) || []
              }
              value={{
                id: selectedColumnValue,
                label:
                  filterFieldsByDataSource[
                    selectedDataSource
                  ]?.groupFields.find((f) => f.name === selectedColumnValue)
                    ?.displayName || '',
              }}
              onChange={(value) => {
                setSelectedColumnValue(value.id);
              }}
            />
            <EnhancedSelect
              label="Column limit"
              options={[
                { id: 5, label: 5 },
                { id: 10, label: 10 },
                { id: 20, label: 20 },
                { id: null, label: 'Unspecified' },
              ]}
              value={{
                id: columnLimit,
                label: columnLimit,
              }}
              onChange={(value) => {
                setColumnLimit(value.id);
              }}
            />
            <Tooltip
              title="For stacked bar chart"
              arrow
              placement="top"
              sx={{ marginLeft: 1 }}
            >
              <IconButton>
                <HelpOutline fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </FormControl>
      )}
      {filterFieldsByDataSource[selectedDataSource]?.dataFields && (
        <FormControl fullWidth margin="normal" id="data-fields-select">
          {/** biome-ignore lint/a11y/noLabelWithoutControl: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX-- */}
          <label>Data fields</label>
          <FieldAggregationManager
            fields={filterFieldsByDataSource[selectedDataSource]?.dataFields}
            selectors={dataFieldsAggregators}
            addSelector={addSelector}
            removeSelector={removeSelector}
            updateSelector={updateSelector}
          />
        </FormControl>
      )}

      {filterFieldsByDataSource[selectedDataSource]?.groupFields && (
        <Box>
          {/** biome-ignore lint/a11y/noLabelWithoutControl: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX-- */}
          <label>Filters</label>
          <FieldMatcher
            fields={[
              ...(filterFieldsByDataSource[selectedDataSource]?.groupFields ??
                []),
            ].map((field) => {
              return {
                id: field.name,
                label: field.displayName,
                fieldMatcherType: field.fieldMatcherType,
              };
            })}
            hideUsePolicyData={true}
            value={filters}
            setValue={setFilters}
          />
        </Box>
      )}

      {dataFieldsAggregators.length > 0 &&
        selectedWidgetType !== WidgetTypes.BOX && (
          <FormControl fullWidth margin="normal">
            <Box>
              <EnhancedSelect
                label="Sort by"
                sx={{ marginRight: 1 }}
                options={
                  dataFieldsAggregators.map((field) => ({
                    id: field.field,
                    label:
                      filterFieldsByDataSource[
                        selectedDataSource
                      ]?.dataFields.find((f) => f.name === field?.field)
                        ?.displayName || DEFAULT_SORTING_FIELD_VALUE,
                  })) || []
                }
                value={{
                  id: selectedSortingField?.field,
                  label: selectedSortingField?.field
                    ? filterFieldsByDataSource[
                        selectedDataSource
                      ]?.dataFields.find(
                        (f) => f.name === selectedSortingField?.field
                      )?.displayName || DEFAULT_SORTING_FIELD_VALUE
                    : '',
                }}
                onChange={(value) => {
                  setSelectedSortingField({
                    ...selectedSortingField,
                    field: value.id,
                  });
                }}
              />
              <EnhancedSelect
                label="Order"
                sx={{ minWidth: '80px', marginRight: 1 }}
                options={[
                  { id: 'asc', label: 'Ascending' },
                  { id: 'desc', label: 'Descending' },
                ]}
                value={{
                  id: selectedSortingField?.order,
                  label: selectedSortingField?.order,
                }}
                onChange={(value) => {
                  setSelectedSortingField({
                    ...selectedSortingField,
                    order: value.id,
                  });
                }}
              />
              <EnhancedSelect
                label="Limit"
                sx={{ minWidth: '80px', marginRight: 1 }}
                options={[
                  { id: 5, label: 5 },
                  { id: 10, label: 10 },
                  { id: 20, label: 20 },
                ]}
                value={{
                  id: selectedSortingField?.limit,
                  label: selectedSortingField?.limit,
                }}
                onChange={(value) => {
                  setSelectedSortingField({
                    ...selectedSortingField,
                    limit: value.id,
                  });
                }}
              />
              <Tooltip title="Optional" arrow placement="top">
                <IconButton>
                  <HelpOutline fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          </FormControl>
        )}
      {dataFieldsAggregators.length > 1 && (
        <FormControl fullWidth margin="normal">
          {/** biome-ignore lint/a11y/noLabelWithoutControl: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX-- */}
          <label>Fields calculation</label>
          <FieldsCalculatorExpressionBuilder
            fields={dataFieldsAggregators.map((selector) => selector.field)}
            value={dataFieldsExpression}
            setValue={setDataFieldsExpression}
          />
        </FormControl>
      )}

      <FormControl fullWidth margin="normal">
        {isFintaryAdmin && (
          <>
            <Grid container spacing={2} sx={toggleStyle}>
              <Grid item>
                <Typography>
                  Global{' '}
                  <span
                    style={{
                      color: 'transparent',
                      textShadow: '0 0 0 #e8e8e8',
                    }}
                  >
                    🔒
                  </span>
                </Typography>
              </Grid>
              <Grid item>
                <Switch
                  checked={selectedAccessLevel}
                  onChange={() => setSelectedAccessLevel(!selectedAccessLevel)}
                />
              </Grid>
            </Grid>
            <Grid container spacing={2} sx={toggleStyle}>
              <Grid item>
                <Typography>
                  Custom code{' '}
                  <span
                    style={{
                      color: 'transparent',
                      textShadow: '0 0 0 #e8e8e8',
                    }}
                  >
                    🔒
                  </span>
                </Typography>
              </Grid>
              <Grid item>
                <Switch
                  checked={enableCustomCode}
                  onChange={() => setEnableCustomCode(!enableCustomCode)}
                />
              </Grid>
            </Grid>
          </>
        )}
      </FormControl>

      <hr />
      <Box
        mt={2}
        display={'flex'}
        flexDirection={'row'}
        alignItems={'center'}
        gap={4}
      >
        <Button
          variant="contained"
          color="primary"
          onClick={handleCreateWidget}
        >
          Preview
        </Button>
        {previewData && (
          <Chip
            icon={<ListAltIcon />}
            label={`${previewData?.rowCount || 0} records found`}
            color="primary"
            variant="outlined"
            sx={{ mr: 1, fontWeight: 500, fontSize: 16 }}
          />
        )}
      </Box>
      <Box
        mt={4}
        mb={4}
        p={2}
        borderColor="grey.400"
        height={380}
        id="preview-widget"
      >
        {isLoading
          ? renderLoadingSkeleton()
          : previewData && renderWidget(previewData)}
      </Box>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row-reverse',
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingBlock: '10px',
          borderBottom: '1px solid #e0e0e0',
          position: 'fixed',
          bottom: '0',
          right: '2rem',
          maxWidth: '500px',
          zIndex: 9999,
          background: '#fff',
        }}
      >
        <Box>
          <Button
            variant="outlined"
            style={{ marginRight: '10px' }}
            onClick={() => {
              setPreviewData(null);
              closeAddWidgetDialog();
            }}
          >
            Close
          </Button>
          <Tooltip
            title={!previewData ? 'Preview the widget first' : ''}
            placement="top"
          >
            <span>
              <Button
                variant="contained"
                id="btn-save-widget"
                color="primary"
                onClick={saveChange}
                disabled={!previewData}
              >
                Save
              </Button>
            </span>
          </Tooltip>
        </Box>
      </Box>
    </Box>
  );
};

export default WidgetCreator;
