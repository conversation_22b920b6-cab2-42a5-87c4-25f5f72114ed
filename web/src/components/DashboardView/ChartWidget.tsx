import { Box, Typography } from '@mui/material';
import type { DataChart } from 'common/dto/widgets';
import EChartsReact from 'echarts-for-react';
import { useEffect, useRef } from 'react';

import { NoData } from './NoData';

export const useChartResize = (
  chartRef: React.RefObject<EChartsReact | null>
) => {
  useEffect(() => {
    const resizeChart = () => {
      chartRef.current?.getEchartsInstance()?.resize();
    };

    const timer = setTimeout(resizeChart, 100);

    const resizeObserver = new ResizeObserver(resizeChart);
    const chartElement = chartRef.current?.getEchartsInstance()?.getDom();

    if (chartElement?.parentElement) {
      resizeObserver.observe(chartElement.parentElement);
    }

    return () => {
      clearTimeout(timer);
      resizeObserver.disconnect();
    };
  }, [chartRef]);
};

const ChartWidget = ({ data }: { data: DataChart }) => {
  const chartRef = useRef<EChartsReact>(null);

  useChartResize(chartRef);

  try {
    if (
      data.series?.some((series) => series?.data && series?.data?.length > 0)
    ) {
      return (
        <EChartsReact
          ref={chartRef}
          key={JSON.stringify(data)}
          option={data}
          style={{ height: 'calc(100% - 48px)', width: '100%' }}
          opts={{ renderer: 'canvas' }}
        />
      );
    }
    return <NoData />;
  } catch (error) {
    console.error('Error rendering ChartWidget:', error);
    return (
      <Box>
        <Typography variant="h5">Error rendering ChartWidget</Typography>
      </Box>
    );
  }
};

export default ChartWidget;
