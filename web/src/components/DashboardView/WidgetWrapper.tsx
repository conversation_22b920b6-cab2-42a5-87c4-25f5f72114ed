import { MoreVert } from '@mui/icons-material';
import {
  <PERSON>,
  Card,
  CardContent,
  Grid,
  IconButton,
  Menu,
  MenuItem,
  <PERSON>lt<PERSON>,
  Typography,
} from '@mui/material';
import BigNumber from 'bignumber.js';
import {
  WidgetDataSourceLabels,
  WidgetFilterByDateFieldLabels,
} from 'common/dto/widgets';
import Formatter from 'common/Formatter';
import { SystemRoles } from 'common/globalTypes';
import type React from 'react';
import { useContext, useState } from 'react';

import BasicTable from '@/components/molecules/BasicTable';
import { UIStateContext } from '@/contexts/UIStateProvider';
import { exportCsv } from '@/services/helpers';
import { useRoleStore } from '@/store';
import { Roles } from '@/types';

interface WidgetWrapperProps {
  displayName: string;
  onEdit?: () => void;
  onDelete?: () => void;
  children: React.ReactNode;
  sharedWidget?: boolean;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  onCopy?: (data: any) => void;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  data?: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  widgetData?: any;
  type?: string;
  isPreview?: boolean;
}

const WidgetWrapper = ({
  displayName,
  onEdit,
  onDelete,
  children,
  onCopy,
  data: _data,
  widgetData,
  type,
  isPreview = false,
}: WidgetWrapperProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [view, setView] = useState('widget');
  const {
    role: [role],
  } = useContext(UIStateContext);
  const isFintaryAdmin = role === SystemRoles.ADMIN;
  const { userRole } = useRoleStore();
  const isAccountAdmin = userRole === Roles.ACCOUNT_ADMIN;

  const handleMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const getHeaders = (data: any) => {
    if (!data.legend) {
      return ['key', 'value'];
    }
    return ['Key', ...data.legend.data, 'Total'];
  };

  const getPropsFromDefinition = (defintion) => {
    if (!defintion) {
      return {};
    }
    let dataField = Array.isArray(defintion.dataField)
      ? defintion.dataField[0]
      : defintion.dataField;
    let aggregationMethod = defintion.calculation;
    let resultFormatter = defintion.resultFormatter;
    if (defintion.aggregationSelectors?.length > 0) {
      dataField = defintion.aggregationSelectors[0].field;
      aggregationMethod = defintion.aggregationSelectors[0].aggregation_method;
      resultFormatter = defintion.aggregationSelectors[0].formatter;
    }
    return { dataField, aggregationMethod, resultFormatter };
  };
  const { resultFormatter } = getPropsFromDefinition(widgetData?.spec);

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const getTableData = (_data: any) => {
    if (!_data.legend) {
      return _data?.xAxis.data.map((key, i) => [
        key,
        _data?.series?.[0].data?.[i],
      ]);
    }

    return _data?.xAxis.data.map((key, i) => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const rowData = _data.series.map((series: any) => {
        const value = series.data[i];
        return typeof value === 'string' ? parseFloat(value) : value;
      });

      let total = rowData.reduce((acc: number, curr: number) => acc + curr, 0);
      total = BigNumber(total).toFormat(2);
      if (resultFormatter) {
        total = Formatter[resultFormatter]?.(total) ?? total;
        rowData.forEach((value, i) => {
          rowData[i] = Formatter[resultFormatter]?.(value) ?? value;
        });
      }
      return [key, ...rowData, total];
    });
  };

  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let headers;
  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let data;
  if (type === 'chart-donut') {
    headers = Object.keys(_data?.series?.[0]?.data?.[0] || {});
    data = _data?.series?.[0]?.data?.map((row) => {
      return Object.values(row);
    });
  } else if (type === 'chart-bar') {
    headers = getHeaders(_data);
    data = getTableData(_data);
  } else if (type === 'chart-line') {
    headers = ['key', 'value'];
    data = _data?.xAxis.data.map((key, i) => [
      key,
      _data?.series?.[0].data?.[i],
    ]);
  } else if (type === 'table-array') {
    headers = _data ? _data[0] : [];
    data = _data ? _data.slice(1) : [];
  } else if (type === 'h5') {
    headers = [displayName];
    data = _data?.toString();
  }

  const widgetLegend = (
    <Typography variant="body2">
      {`Data source: ${WidgetDataSourceLabels[widgetData?.spec?.dataSource]}`}
      {widgetData?.spec?.filterByDate && (
        <>
          <br />
          {`Date filter: ${WidgetFilterByDateFieldLabels[widgetData.spec.filterByDate]}`}
        </>
      )}
      {widgetData?.spec?.groupBy && (
        <>
          <br />
          {`Group by: ${widgetData.spec.groupBy}`}
        </>
      )}
      {widgetData?.spec?.aggregationSelectors?.length > 0 && (
        <>
          <br />
          {`Aggregation: ${widgetData.spec.aggregationSelectors
            .map(
              (selector) => `${selector.field} (${selector.aggregation_method})`
            )
            .join(', ')}`}
        </>
      )}
    </Typography>
  );

  const canEditAndDelete =
    widgetData?.access === 'global'
      ? isFintaryAdmin
      : isFintaryAdmin || isAccountAdmin;
  return (
    <Card style={{ height: '100%', overflowY: 'hidden' }}>
      <CardContent
        sx={{
          padding: 0,
          height: '100%',
          position: 'relative',
          // height: 'calc(100% - 10px)',
          '&:last-child': { paddingBottom: 0 },
        }}
      >
        <Grid
          container
          justifyContent="space-between"
          className="grid-item__title"
          p={1}
          style={{
            borderBottom: '1px solid lightgrey',
            boxShadow: '0px 1px 1px lightgrey',
            height: '48px',
            minHeight: '48px',
            maxHeight: '48px',
          }}
        >
          <Grid
            item
            sx={{
              flex: 1,
              minWidth: 0,
              display: 'flex',
              alignItems: 'center',
              flexGrow: 1,
            }}
            className="dragHandle"
          >
            <Box
              sx={{ width: '100%' }}
              minHeight="100px"
              maxHeight="100px"
              flex="1"
              display="flex"
              flexDirection="row"
              justifyContent="flex-start"
              alignItems="center"
              flexWrap="wrap"
              flexGrow={1}
              gap={1}
              minWidth={0}
            >
              {!isPreview ? (
                <Tooltip title={widgetLegend}>
                  <Typography
                    sx={{ p: 0, m: 0 }}
                    gutterBottom
                    variant="body1"
                    noWrap
                  >
                    {displayName}
                  </Typography>
                </Tooltip>
              ) : (
                <Typography noWrap sx={{ p: 0, m: 0 }}>
                  {displayName}
                </Typography>
              )}
            </Box>
          </Grid>

          <Grid
            item
            sx={{
              flexShrink: 0,
              display: 'flex',
              alignItems: 'center',
              padding: 0,
            }}
          >
            {!isPreview && (
              <>
                <IconButton onClick={handleMenuClick} sx={{ p: 0 }}>
                  <MoreVert />
                </IconButton>
                <Menu
                  anchorEl={anchorEl}
                  open={Boolean(anchorEl)}
                  onClose={handleMenuClose}
                >
                  {[
                    onEdit && (
                      <MenuItem
                        key="edit"
                        onClick={() => {
                          handleMenuClose();
                          onEdit();
                        }}
                        className="flex justify-between"
                        disabled={!canEditAndDelete}
                      >
                        Edit{' '}
                        {!canEditAndDelete && (
                          <span
                            style={{
                              color: 'transparent',
                              textShadow: '0 0 0 #e8e8e8',
                            }}
                          >
                            🔒
                          </span>
                        )}
                      </MenuItem>
                    ),
                    onDelete && (
                      <MenuItem
                        key="delete"
                        onClick={() => {
                          handleMenuClose();
                          onDelete();
                        }}
                        className="flex justify-between"
                        disabled={!canEditAndDelete}
                      >
                        Delete{' '}
                        {!canEditAndDelete && (
                          <span
                            style={{
                              color: 'transparent',
                              textShadow: '0 0 0 #e8e8e8',
                            }}
                          >
                            🔒
                          </span>
                        )}
                      </MenuItem>
                    ),
                  ]}
                  {onCopy && (
                    <MenuItem
                      key="copy"
                      onClick={() => {
                        handleMenuClose();
                        onCopy(widgetData?.spec);
                      }}
                    >
                      Create copy
                    </MenuItem>
                  )}
                  {typeof type === 'string' &&
                    ['chart-donut', 'chart-bar'].includes(type) && [
                      <MenuItem
                        key="toggleView"
                        onClick={() => {
                          setView(view === 'table' ? 'widget' : 'table');
                        }}
                      >
                        {view === 'table' ? 'Widget view' : 'Table view'}
                      </MenuItem>,
                    ]}
                  <MenuItem
                    key="export"
                    onClick={() => {
                      exportCsv(headers, data, 'Fintary-Export.csv');
                    }}
                  >
                    Export data
                  </MenuItem>
                </Menu>
              </>
            )}
          </Grid>
        </Grid>
        {view === 'widget' && children}
        {view === 'table' && (
          <BasicTable
            headers={headers}
            rows={data}
            sorted={true}
            formatters={{}}
          />
        )}
      </CardContent>
    </Card>
  );
};

export default WidgetWrapper;
