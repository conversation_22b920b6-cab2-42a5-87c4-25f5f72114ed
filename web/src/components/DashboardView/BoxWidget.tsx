import { Box, Typography } from '@mui/material';

const BoxWidget = (widget) => {
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: 'calc(100% - 48px)',
      }}
    >
      <Typography
        fontWeight={500}
        textAlign="center"
        sx={{
          fontSize: '2rem',
        }}
      >
        {widget?.value}
      </Typography>
    </Box>
  );
};

export default BoxWidget;
