.App {
  text-align: center;
}

.react-grid-layout {
  position: relative;
  transition: height 200ms ease;
}

.reactGridItem {
  transition: all 200ms ease;
  transition-property: left, top;
  background-color: rgb(245, 246, 247);
  font-family: Arial, Helvetica, sans-serif;
  position: relative;
}
.deleteButton :hover {
  cursor: pointer;
}

.dragHandle {
  width: 100%;
  height: 100%;
}
.dragHandle:hover {
  cursor: move;
}
.grid-item-card {
  height: 100%;
}

/* Placeholder styling when dragging */
.react-grid-placeholder {
  background-color: rgba(0, 123, 255, 0.3) !important;
  border: 2px dashed #007bff !important;
  border-radius: 8px;
}
