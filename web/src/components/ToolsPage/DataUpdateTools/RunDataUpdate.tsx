import {
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
  Typography,
} from '@mui/material';
import type React from 'react';
import { useEffect, useMemo, useRef, useState } from 'react';
import type dayjs from 'dayjs';
import { LoadingButton } from '@mui/lab';
import isNil from 'lodash-es/isNil';
import { dateOrDefault } from 'common/helpers';
import {
  type DataActionsPreviewResult,
  DataEntities,
} from 'common/globalTypes';

import API from '@/services/API';
import Formatter from '@/services/Formatter';
import BasicDateRangePicker from '@/common/BasicDateRangePicker';
import useSnackbar from '@/contexts/useSnackbar';
import DataUpdateGroupViewer from '@/components/ToolsPage/DataUpdateTools/DataUpdateGroupViewer';
import type { Fields } from '@/components/ToolsPage/DataUpdateTools/types';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import DataUpdatePreviewDialog from '@/components/ToolsPage/DataUpdateTools/DataUpdatePreviewModal';

interface RunDataUpdateProps {
  fields: Fields;
}

enum DataFilterOptions {
  ALL = 'all',
  RECONCILED = 'reconciled',
}

const RunDataUpdate: React.FC<RunDataUpdateProps> = ({ fields }) => {
  const memoizedFields = useMemo(() => fields, [fields]);

  const { showSnackbar } = useSnackbar();

  const [searchDoc, setSearchDoc] = useState('');

  const [showGlobal, setShowGlobal] = useState(false);
  const [globalSelected, setGlobalSelected] = useState(false);
  const [loading, setLoading] = useState(false);
  const [startDate, setStartDate] = useState<dayjs.Dayjs | string | null>(null);
  const [endDate, setEndDate] = useState<dayjs.Dayjs | string | null>(null);
  const [selectedDocs, setSelectedDocs] = useState<string[]>([]);

  const [searchGroupOption, setSearchGroupOption] = useState('');
  const [selectedDataUpdateGroup, setSelectedDataUpdateGroup] =
    useState<string>('');

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [fieldsToPreview, setFieldsToPreview] = useState<any[]>([]);
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [previewData, setPreviewData] = useState<any>(null);
  const [openPreview, setOpenPreview] = useState(false);
  const [selectedDataFilter, setSelectedDataFilter] = useState<string>(
    DataFilterOptions.RECONCILED
  );
  const [selectGroupedRecords, setSelectGroupedRecords] =
    useState<boolean>(false);

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const fieldsToPreviewRef = useRef<any[]>([]);

  const handleClose = () => {
    setOpenPreview(false);
  };

  const { data: _documents } = API.getBasicQuery(
    'documents',
    'is_dynamic_select=true&limit=300'
  );
  const documents = useMemo(() => _documents?.data ?? [], [_documents?.data]);

  const { data: _dataUpdateConfig } = API.getBasicQuery(
    `data-update/config?global=${showGlobal}`
  );

  const dataUpdateInfo = useMemo(
    () => _dataUpdateConfig?.data ?? [],
    [_dataUpdateConfig]
  );

  const dataUpdateGroups: string[] = Array.from(
    new Set(
      dataUpdateInfo.map((data: { group: string; access: string }) =>
        data.access === 'global' ? `${data.group} - global` : data.group
      )
    )
  );

  const runCompensationTypePreviewPoster = API.getMutation(
    'admin/data-update/preview',
    'POST'
  );

  const runCompensationTypePoster = API.getMutation(
    'admin/data-update',
    'POST'
  );

  const handleGroupChange = (group: string) => {
    setSelectedDataUpdateGroup(group);
    setGlobalSelected(group.includes(' - global'));
    fieldsToPreviewRef.current = [];
  };

  const filteredDataUpdateInfo = useMemo(() => {
    if (selectedDataUpdateGroup) {
      const normalizedGroup = selectedDataUpdateGroup.replace(' - global', '');
      return dataUpdateInfo.filter(
        (data: { group: string }) => data.group === normalizedGroup
      );
    }
    return [];
  }, [selectedDataUpdateGroup, dataUpdateInfo]);

  const fieldsList = useMemo(() => {
    if (filteredDataUpdateInfo.length > 0) {
      return memoizedFields[filteredDataUpdateInfo[0]?.data_entity] || [];
    }
    return [];
  }, [filteredDataUpdateInfo, memoizedFields]);

  const handleDataUpdatePost = async (
    dataToUpdate: DataActionsPreviewResult
  ) => {
    setLoading(true);
    try {
      // Transform targetUpdates to the format expected by the backend
      const transformedUpdates = dataToUpdate.targetUpdates.map((update) => ({
        data_entity_id: update.recordId,
        ...update.proposedChanges,
      }));

      const params = {
        dataToUpdate: transformedUpdates,
        dataEntity: dataToUpdate.targetUpdates[0]?.entity || '',
      };
      const response = await runCompensationTypePoster.mutateAsync(params);

      if (response.error) {
        showSnackbar(response.error, 'error');
      } else {
        showSnackbar(`${response.data.length} records updated.`, 'success');
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : JSON.stringify(error);
      showSnackbar(errorMessage, 'error');
    }
    setLoading(false);
  };

  const handleDataUpdatePreviewPost = async (isPreview: boolean) => {
    setLoading(true);
    try {
      const params = {
        data_update_group: selectedDataUpdateGroup.replace(' - global', ''),
        global: globalSelected,
        processing_date_start: dateOrDefault(startDate, undefined),
        processing_date_end: dateOrDefault(endDate, undefined),
        document_str_ids: selectedDocs,
        only_reconcilied_data:
          selectedDataFilter === DataFilterOptions.RECONCILED,
        select_grouped_records: selectGroupedRecords,
        preview: isPreview,
        fieldsToPreview: fieldsToPreview.map((field) => field.id),
      };

      const response =
        await runCompensationTypePreviewPoster.mutateAsync(params);

      if (response.error) {
        showSnackbar(response.error, 'error');
      } else {
        if (isPreview) {
          setPreviewData(response.data);

          setOpenPreview(true);
          showSnackbar(
            `Preview generated for ${response.data.summary.sourceRecordsCount} records.`,
            'success'
          );
        } else {
          showSnackbar(`${response.data.length} records updated.`, 'success');
        }
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : JSON.stringify(error);
      showSnackbar(errorMessage, 'error');
    }
    setLoading(false);
  };

  const selectedDocsId = new Set(selectedDocs);
  const filteredDocs = useMemo(() => {
    return documents?.filter((d) =>
      (d.filename as string)?.toLowerCase().includes(searchDoc?.toLowerCase())
    );
  }, [documents, searchDoc]);

  const filteredGroups = useMemo(() => {
    const filtered = dataUpdateGroups.filter(
      (group) =>
        !isNil(group) &&
        group?.toLowerCase().includes(searchGroupOption.toLowerCase())
    );

    return filtered.length > 0 ? filtered : dataUpdateGroups;
  }, [dataUpdateGroups, searchGroupOption]);

  const criteriaDefaultFields = useMemo(() => {
    const _fields = new Set();

    const dataEntity = filteredDataUpdateInfo[0]?.data_entity || '';

    if (dataEntity === DataEntities.COMMISSIONS) {
      const commissionDefaultFields = [
        'policy_id',
        'writing_carrier_name',
        'compensation_type',
        'commission_rate',
        'product_type',
        'effective_date',
      ];
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      commissionDefaultFields.forEach((field) => _fields.add(field));
    }

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    filteredDataUpdateInfo.forEach((item) => {
      const { data_update_criteria, data_update_actions } = item;

      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      data_update_criteria.forEach((criterion) => {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        criterion.data_update_criteria?.forEach((c) => {
          _fields.add(c?.field);
        });
      });

      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      data_update_actions.forEach((action) => {
        _fields.add(action?.data_update_actions?.field);
      });
    });
    return _fields;
  }, [filteredDataUpdateInfo]);

  useEffect(() => {
    const defaultFields =
      fieldsList?.filter((field) => criteriaDefaultFields.has(field.id)) || [];
    if (fieldsToPreviewRef.current.length === 0) {
      setFieldsToPreview(defaultFields);
    } else {
      setFieldsToPreview(fieldsToPreviewRef.current);
    }
  }, [fieldsList, criteriaDefaultFields]);

  useEffect(() => {
    fieldsToPreviewRef.current = fieldsToPreview;
  }, [fieldsToPreview]);

  return (
    <>
      <Box
        sx={{
          mt: 1,
          mb: selectedDataUpdateGroup ? 4 : 8,
          ml: 2,
          gap: 2,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Typography variant="body2">Select data update group to run</Typography>
        <Box sx={{ gap: 2, display: 'flex', alignItems: 'center' }}>
          <EnhancedSelect
            label="Group"
            enableSearch
            options={filteredGroups}
            value={selectedDataUpdateGroup}
            searchKeyword={searchGroupOption}
            onSearch={setSearchGroupOption}
            onChange={handleGroupChange}
          />
          <Button
            variant="outlined"
            onClick={() => {
              setSearchGroupOption('');
              setSelectedDataUpdateGroup('');
              setShowGlobal(!showGlobal);
            }}
          >
            {showGlobal ? 'Show account groups' : 'Show global groups'}
          </Button>
        </Box>
        <DataUpdateGroupViewer
          dataUpdateData={filteredDataUpdateInfo}
          fields={memoizedFields}
        />
      </Box>
      <Box
        sx={{
          mt: 1,
          mb: 1,
          ml: 2,
          gap: 2,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Typography variant="body2">Commission filters</Typography>
        <BasicDateRangePicker
          range={{
            startDate: startDate,
            startDateLabel: 'Processing date start',
            endDate: endDate,
            endDateLabel: 'Processing date end',
          }}
          onChange={(range) => {
            setStartDate(range.startDate);
            setEndDate(range.endDate);
          }}
          justify="left"
          width={210}
        />
        <Box>
          <EnhancedSelect
            label="Document filter"
            enableSearch
            multiple
            valueKey="str_id"
            labelKey="filename"
            options={filteredDocs}
            sx={{ width: 200, mt: 1.5 }}
            value={filteredDocs.filter((item) =>
              selectedDocsId.has(item.str_id)
            )}
            searchKeyword={searchDoc}
            onSearch={setSearchDoc}
            renderLabel={(input) => {
              const doc = filteredDocs.find((d) => d.str_id === input?.key);
              const text = doc
                ? `${doc?.filename} (${Formatter.date(doc?.created_at, { format: 'YYYY/MM/DD hh:mmA' })})`
                : '';
              return (
                <Tooltip title={text}>
                  <Typography
                    sx={{
                      ...input?.sx,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {text}
                  </Typography>
                </Tooltip>
              );
            }}
            onChange={(v) => {
              setSelectedDocs(v.map((item) => item.str_id));
            }}
          />
        </Box>
        <Box sx={{ mt: 0.5, ml: 1 }}>
          <Tooltip title="For all grouped records to be included you may have to select the 'All data' filter option.">
            <FormControlLabel
              control={
                <Checkbox
                  checked={selectGroupedRecords}
                  onClick={() => setSelectGroupedRecords(!selectGroupedRecords)}
                />
              }
              label="Include grouped records"
              value={selectGroupedRecords}
            />
          </Tooltip>
        </Box>
        <ToggleButtonGroup
          sx={{ mt: 0.5 }}
          value={selectedDataFilter}
          exclusive
          onChange={(_event, reportPrefix) => {
            setSelectedDataFilter(reportPrefix);
          }}
          color="primary"
        >
          <ToggleButton value={DataFilterOptions.RECONCILED}>
            Reconcilied data
          </ToggleButton>
          <ToggleButton value={DataFilterOptions.ALL}>All data</ToggleButton>
        </ToggleButtonGroup>
      </Box>
      <Box sx={{ mt: 2 }}>
        {selectedDataUpdateGroup && fieldsList && (
          <>
            <EnhancedSelect
              label="Fields to preview"
              enableSearch
              options={fieldsList}
              value={fieldsToPreview}
              onChange={(v) => {
                setFieldsToPreview(v);
              }}
              multiple
              sx={{ ml: 1, minWidth: 200, width: 'fit-content' }}
            />
            <LoadingButton
              loading={loading}
              variant="contained"
              disabled={fieldsToPreview.length === 0}
              onClick={() => handleDataUpdatePreviewPost(true)}
              sx={{ ml: 1 }}
            >
              Preview update
            </LoadingButton>
            <DataUpdatePreviewDialog
              handleClose={handleClose}
              open={openPreview}
              handleDataUpdatePost={handleDataUpdatePost}
              loading={loading}
              previewData={previewData}
              fieldsToPreview={fieldsToPreview}
              fields={fieldsList}
            />
          </>
        )}
      </Box>
    </>
  );
};

export default RunDataUpdate;
